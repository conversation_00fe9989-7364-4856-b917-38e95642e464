import { RouteRecordRaw } from 'vue-router';

const modules = import.meta.glob('/src/page/**/*.vue');

import { baseRouterConstructor } from '@haierbusiness-front/utils';

const routes = [{
    path: '/',
    redirect: '/restaurant/hotelList'
},
{
    path: '/restaurant',
    redirect: '/restaurant/hotelList',
    component: () => import('../page/restaurant/mobile/index.vue'),
    children: [
        {
            path: '/restaurant/hotelList',
            name: 'hotelList',
            meta: {
                keepAlive: true //设置页面是否需要使用缓存
            },

            component: () => import('../page/restaurant/mobile/hotelList.vue'),
        },
        {
            path: '/restaurant/orderList',
            name: 'orderList',
            meta: {
                keepAlive: true //设置页面是否需要使用缓存
            },

            component: () => import('../page/restaurant/mobile/orderList.vue'),
        },
        // 搜索页
        {
            path: '/restaurant/search',
            name: 'search',
            meta: {
                keepAlive: false //设置页面是否需要使用缓存
            },

            component: () => import('../page/restaurant/mobile/search.vue'),
        },
        // 酒店详情
        {
            path: '/restaurant/hotelDetail',
            name: 'hotelDetail',
            meta: {
                keepAlive: false //设置页面是否需要使用缓存
            },
            component: () => import('../page/restaurant/mobile/hotelDetail.vue'),
        },

        {
            path: '/restaurant/hotel/imgList',
            name: 'imgList',
            meta: {
                keepAlive: false //设置页面是否需要使用缓存
            },
        
            component: () => import('../page/restaurant/mobile/imgList.vue'),
        },

        {
            path: '/restaurant/order/orderDetail',
            name: 'orderDetail',
            meta: {
                keepAlive: false //设置页面是否需要使用缓存
            },
        
            component: () => import('../page/restaurant/mobile/orderDetail.vue'),
        },

        {
            path: '/restaurant/book',
            name: 'book',
            meta: {
                keepAlive: false //设置页面是否需要使用缓存
            },
        
            component: () => import('../page/restaurant/mobile/book.vue'),
        },
        {
            path: '/restaurant/order/orderLog',

            name: 'orderLog',
            meta: {
                keepAlive: false //设置页面是否需要使用缓存
            },
            component: () => import('../page/restaurant/mobile/orderLog.vue'),
        },


    ]
},


];
// const router = baseRouterConstructor("haierbusiness-portalIndex", modules, flag, undefined, routes)
const router = baseRouterConstructor("haierbusiness-localhotel", modules, true, undefined, routes)

export default router;
