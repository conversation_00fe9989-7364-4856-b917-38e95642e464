<template>
  <div class="order-list" style="padding-bottom: 50px; min-height: 100vh;" >
    <van-search class="list-search" shape="round" @search="reSearch" v-model="defaultParams.keyword" @clear="reSearch" @click-left-icon="reSearch" :clearable="true" show-action placeholder="搜索我的订单">
      <template #action>
        <van-dropdown-menu :close-on-click-outside="false" ref="menuRef">
          <van-dropdown-item ref="itemRef">
            <template #title>
              <van-icon name="filter-o" />
              筛选
            </template>

            <van-cell-group title="按时间选择">
              <van-cell>
                <template #value>
                  <!-- 快速选择时间 -->
                  <van-radio-group v-model="defaultParams.timeType">
                    <van-row justify="space-between">
                      <van-radio name="week" class="my-radio">
                        <template #icon="{ checked }">
                          <van-button class="btn-com" size="small" round :type="checked ? 'primary' : 'default'"
                            >最近一周</van-button
                          >
                        </template>
                      </van-radio>

                      <van-radio name="month" class="my-radio">
                        <template #icon="{ checked }">
                          <van-button class="btn-com" size="small" round :type="checked ? 'primary' : 'default'"
                            >最近一月</van-button
                          >
                        </template>
                      </van-radio>

                      <van-radio name="year" class="my-radio">
                        <template #icon="{ checked }">
                          <van-button class="btn-com" size="small" round :type="checked ? 'primary' : 'default'"
                            >最近一年</van-button
                          >
                        </template>
                      </van-radio>
                    </van-row>
                  </van-radio-group>

                  <!-- 具体时间选择 -->
                  <van-row class="mt-10" justify="space-between">
                    <van-col :span="10">
                      <van-field
                        class="input-border"
                        readonly
                        @click="openTimePicker('begin')"
                        v-model="defaultParams.startTime"
                        placeholder="选择开始时间"
                      />
                    </van-col>
                    <van-col :span="4">
                      <van-divider :style="{ height: '100%', borderColor: '#000', padding: '0 16px', margin: '0' }" />
                    </van-col>
                    <van-col :span="10">
                      <van-field
                        class="input-border"
                        readonly
                        @click="openTimePicker('end')"
                        v-model="defaultParams.endTime"
                        placeholder="选择结束时间"
                      />
                    </van-col>
                  </van-row>
                </template>
              </van-cell>
            </van-cell-group>
            <van-cell-group title="按支付方式选择">
              <van-radio-group v-model="defaultParams.payType">
                <van-cell>
                  <van-row justify="space-between">
                    <van-radio name="1" class="my-radio">
                      <template #icon="{ checked }">
                        <van-button class="btn-com" size="small" round :type="checked ? 'primary' : 'default'"
                          >挂帐</van-button
                        >
                      </template>
                    </van-radio>
                    <van-radio name="2" class="my-radio">
                      <template #icon="{ checked }">
                        <van-button class="btn-com" size="small" round :type="checked ? 'primary' : 'default'"
                          >自付</van-button
                        >
                      </template>
                    </van-radio>

                    
                  </van-row>
                </van-cell>
              </van-radio-group>
            </van-cell-group>
            <van-cell-group title="按商家查询">
              <van-search
                shape="round"
                v-model="defaultParams.hotelName"
                :clearable="true"
                left-icon=""
                placeholder="搜索商家名称"
              />
            </van-cell-group>

            <van-row justify="space-around" style="padding: 15px 0">
              <van-button class="list-search-btn" size="small" type="default" round @click="reSet"> 清空 </van-button>
              <van-button class="list-search-btn" size="small" type="primary" round @click="reSearch">
                确认
              </van-button>
            </van-row>
          </van-dropdown-item>
        </van-dropdown-menu>
      </template>
    </van-search>

    <van-tabs v-model:active="defaultParams.orderStatusNew">
      <van-tab v-for="(item, index) in tabList" :name="item.value" :title="item.label" :key="index"> </van-tab>
    </van-tabs>

    <van-list
      v-model:loading="orderLoading"
      :finished="orderFinished"
      :finished-text="orderList.length ? '没有更多了' : ''"
      @load="loadorderList"
    >
      <van-cell-group
        inset
        class="mt-10"
        v-for="(item, index) in orderList"
        :key="index"
        @click="goToDetail(item?.applicant?.orderCode)"
      >
        <van-cell :value="item.eatingTime">
          <template #title>
            <div class="weight600 font-size-12">{{ item?.hotelInfo.fullname }}</div>
          </template>
        </van-cell>

        <van-cell class="cell-item">
          <template #title>
            <van-row>
              <van-col :span="8">订单编号:</van-col>
              <van-col :span="16">{{ item?.applicant?.orderCode }}</van-col>
            </van-row>
            <van-row>
              <van-col :span="8">就餐人数:</van-col>
              <van-col :span="16">{{ (item?.treatInfo?.accompanyCount || 0)+ (item?.treatInfo?.guestCount || 0 )}}人</van-col>
            </van-row>
            <van-row>
              <van-col :span="8">支付方式:</van-col>
              <van-col :span="16">{{ item.payType == 1 ? '挂帐' : '自付' }}</van-col>
            </van-row>
            <van-row>
              <van-col :span="8">总价:</van-col>
              <van-col :span="16">¥{{ (item.consumptionStandard  / 100) + (item.workingLunchFee / 100)}}</van-col>
            </van-row>
          </template>
          <template #value>
            <div>
              <van-tag v-if="item.payType == '1'" plain :color="ROrderApprovalStateEnumColor[item.approvalState]" size="medium">{{
                ROrderApprovalStateEnum[item.approvalState] || ''
              }}</van-tag>

              <van-tag v-if="item.payType == '1'" plain :color="ROrderPayMentStateEnumColor[item.paymentState]" size="medium">{{
                ROrderPayMentStateEnum[item.paymentState] || ''
              }}</van-tag>

              <van-tag plain :color="ROrderStateEnumColor[item.state]" size="medium">{{
                ROrderStateEnum[item.state] || ''
              }}</van-tag>
            </div>
          </template>
        </van-cell>

        <van-cell>
          <template #value>
            <div>
              <!-- //去支付
            //支付类型为挂账 && 已保存 && 未支付 -->
              <van-button :loading="payIndex == index" class="mr-10" v-if="item.payType == '1' && item.state =='10' &&item.paymentState=='0'" type="primary" size="small" @click.stop="goPay(item.code, index)">去支付</van-button>
              <!-- //撤销订单 
                //锁定人为空 && 已保存 && 未支付 -->
              <van-button type="danger" v-if="!item.lockerId && item.state== '10' && item.paymentState == '0'" size="small" @click.stop="cancelOrder(item.code)">撤销订单</van-button>
            </div>
          </template>
        </van-cell>
      </van-cell-group>
    </van-list>

    <van-empty v-if="!orderLoading && orderList.length == 0" description="暂无数据" />


    <!-- 时间选择 -->
    <van-popup @click.stop v-model:show="showTimePicker" :close-on-click-overlay="false" position="bottom">
      <van-date-picker
        title="选择日期"
        v-model="currentDate"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="confirmTime"
        @cancel="showTimePicker = false"
      />
    </van-popup>

    <van-tabbar v-model="active" route>
      <van-tabbar-item replace to="/restaurant/hotelList">
        <span>餐厅预订</span>
        <template #icon="props">
          <img :src="props.active ? bookIcon.active : bookIcon.inactive" />
        </template>
      </van-tabbar-item>

      <van-tabbar-item replace to="/restaurant/orderList">
        <span>订单中心</span>
        <template #icon="props">
          <img :src="props.active ? orderIcon.active : orderIcon.inactive" />
        </template>
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch,onActivated } from 'vue';

import {
  IUserListRequest,
  ROrderPayMentStateEnum,
  ROrderPayMentStateEnumColor,
  ROrderApprovalStateEnum,
  ROrderApprovalStateEnumColor,
  ROrderStateEnum,
  ROrderStateEnumColor,
} from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { restaurantApi } from '@haierbusiness-front/apis';
import { ROrderParams } from '@haierbusiness-front/common-libs';
import type { Ref } from 'vue';

import dayjs from 'dayjs';

import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { showConfirmDialog, showSuccessToast } from 'vant';
const store = applicationStore();

const { loginUser } = storeToRefs(store);

const router = getCurrentRouter();
const route = ref();


const active = ref(0);

const bookIcon = {
  active: new URL('@/assets/image/restaurant/tab_book_active.png', import.meta.url).href,
  inactive: new URL('@/assets/image/restaurant/tab_book.png', import.meta.url).href,
};

const orderIcon = {
  active: new URL('@/assets/image/restaurant/tab_order_active.png', import.meta.url).href,
  inactive: new URL('@/assets/image/restaurant/tab_order.png', import.meta.url).href,
};

const payIndex = ref(-1);

// tab
const tabList = [
  {
    label: '全部订单',
    value: '',
  },
  {
    label: '已取消',
    value: '0',
  },
  {
    label: '已保存',
    value: '10',
  },
  {
    label: '已提交',
    value: '20',
  },
  {
    label: '预约完成',
    value: '30',
  },
  {
    label: '已退回',
    value: '40',
  },
];

// 订单列表
const orderLoading = ref<boolean>(false);
const orderFinished = ref<boolean>(false);
const orderList = ref([])
const orderTotal = ref<number>(0);
const defaultParams = ref<ROrderParams>({
  timeType: '',
  keyword: '',
  ownerIsOwn: loginUser.value?.username, //联系人工号
  pageNum: 0,
  pageSize: 20,
  orderStatusNew: '', // 订单状态
  payType: null, // 支付方式
  startTime: '',
  endTime: '',
});

// 
const goPay =(code: string, index:number) => {
  payIndex.value = index

  const payParam = {
      orderNo: code
    }
  restaurantApi.prePay(payParam).then(url => {
      const href =url.data

      // const href = payUrl + "&iam_token=" + encodeURIComponent(token) + "&redirect_url=" + encodeURIComponent(url.data)
      console.log(99999, href)
      // 跳转到支付中台
      const a = document.createElement("a");
      a.setAttribute("href", href);
      a.setAttribute("style", "display:none");
      document.body.appendChild(a);
      a.click();
      a.parentNode.removeChild(a);
      setTimeout(() => {
        payIndex.value = -1
      }, 2000);
    }).catch(err => {
      payIndex.value = -1
    })
}

const loadorderList = () => {
  defaultParams.value.pageNum++;
  restaurantApi.orderList(defaultParams.value).then((res) => {
    // 加载状态结束
    orderLoading.value = false;
    orderList.value = [...orderList.value, ...res.data];
    orderTotal.value = res.total ?? 0;
    // 数据全部加载完成
    if (orderList.value.length >= orderTotal.value) {
      orderFinished.value = true;
    }
  });
};

onActivated(() => {
  route.value = getCurrentRoute()
  const type = route.value?.query?.reload
  if ( type == '1') {
    console.log('reload', type)
    reSet()
    reSearch();
  }
})

watch(
  () => defaultParams.value.timeType,
  (val: string | undefined) => {
    console.log('9999', val);
    switch (val) {
      case 'week':
        defaultParams.value.endTime = dayjs().format('YYYY-MM-DD');
        defaultParams.value.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD');
        break;

      case 'month':
        defaultParams.value.endTime= dayjs().format('YYYY-MM-DD');
        defaultParams.value.startTime= dayjs().subtract(1, 'month').format('YYYY-MM-DD');
        break;
      case 'year':
        defaultParams.value.endTime= dayjs().format('YYYY-MM-DD');
        defaultParams.value.startTime = dayjs().subtract(1, 'year').format('YYYY-MM-DD');
        break;

      default:
        break;
    }
  },
);

watch(
  () => defaultParams.value.orderStatusNew,
  () => {
    reSearch();
  },
);

// 清空
const reSet = () => {
  defaultParams.value = {
    hotelName: '',
    timeType: '',
    ownerIsOwn: loginUser.value?.username, //联系人工号
    pageNum: 0,
    pageSize: 20,
    keyword:'',
    orderStatusNew: '', // 订单状态
    payType: null, // 支付方式
    startTime: '',
    endTime: '',
  };
};

// 搜索
const menuRef = ref(null);
const reSearch = () => {
  orderLoading.value=true
  orderFinished.value = false
  defaultParams.value.pageNum = 0;
  orderList.value = []
  loadorderList();
  menuRef.value.close();
};

// 时间选择相关
const showTimePicker = ref<boolean>(false);

const minDate = ref(new Date(2021,0,1));
const maxDate = ref(new Date(2034, 0, 1));
const choseTimeType = ref('');
const currentDate = ref<Array<string>>([]);

const openTimePicker = (type: string) => {
  choseTimeType.value = type;
  currentDate.value = [];
  if (defaultParams.value.startTime) {
    const minDateArr = defaultParams.value.startTime.split('-');
    minDate.value = new Date(parseInt(minDateArr[0]), parseInt(minDateArr[1]) - 1, parseInt(minDateArr[2]));
  }
  if (defaultParams.value.endTime) {
    const maxDateArr = defaultParams.value.endTime.split('-');
    maxDate.value = new Date(parseInt(maxDateArr[0]), parseInt(maxDateArr[1]) - 1, parseInt(maxDateArr[2]));
  }

  if (type == 'begin') {
    minDate.value = new Date(2021,0,1);
    currentDate.value = defaultParams.value.startTime?.split('-');
  } else {
    maxDate.value = new Date(2034, 0, 1);
    currentDate.value = defaultParams.value.endTime?.split('-');
  }

  showTimePicker.value = true;
};

const confirmTime = ({ selectedValues }: any) => {
  if (choseTimeType.value == 'begin') {
    defaultParams.value.startTime = selectedValues.join('-');
  } else {
    defaultParams.value.endTime = selectedValues.join('-');
  }

  if (defaultParams.value.startTime && defaultParams.value.endTime) {
    defaultParams.value.timeType = '';
  }

  showTimePicker.value = false;
};

const goToDetail = (orderCode: string) => {
  router.push({ path: '/restaurant/order/orderDetail', query: { orderCode: orderCode } });
};

// 撤销订单
const cancelOrder = (orderCode: string) => {
  const params = {
    orderCode,
  };

  showConfirmDialog({
    title: '提示',
    message: '确定撤销此订单吗?',
  })
    .then(() => {
      restaurantApi.cancel(params).then((res) => {
        showSuccessToast('撤销成功!');

        reSearch();
      });
    })
    .catch(() => {
      // on cancel
    });
};
</script>

<style lang='less' scoped>
@import url(./common.less);
:deep(.van-dropdown-menu__bar) {
  //  background: var(--van-dropdown-menu-background);
  box-shadow: none;
}
.list-search {
  :deep(.van-search__action) {
    // padding: 0;
  }
}
.list-search-btn {
  width: 110px;
}
.my-radio {
  :deep(.van-radio__icon) {
    height: auto !important;
  }
}
.btn-com {
  width: 70px;
}
.input-border {
  border: 1px solid #dcdee0;
  border-radius: 20px;
  padding: 2px 10px;
}
</style>