<template>
  <div class="book-list">
    <van-sticky>
      <van-search
        v-model="defaultParams.keyword"
        shape="round"
        placeholder="请输入酒店名称/菜系/区域"
        @search="onSearch"
        readonly
        @click="goToSearch"
        class="top-search"
      >
        <template #left-icon>
          <van-icon name="search" class="search-icon-color" color="#2681FF" @click="onSearch" />
        </template>
        <template #right-icon>
          <van-button class="search-btn font-size-vant ml-10" size="small" @click="onSearch" round>搜索</van-button>
        </template>
      </van-search>

      <van-tabs v-model:active="cateClassActive">
        <van-tab v-for="item, index in cateClassList" :key="index" :name="item.id" :title="item.name"></van-tab>
      </van-tabs>

      <!-- 下拉筛选菜单 -->
      <van-dropdown-menu ref="menuRef" class="my-dropdown" :close-on-click-outside="false">
        <van-dropdown-item :title-class="defaultParams.regionIds?.length> 0 ? 'active-dropdown-item' : ''">
          <template #title>
            <div class="mr-5 font-size-vant">区域</div>
            <div class="default-down-icon"></div>
          </template>
          <van-tree-select @click-nav="defaultParams.regionIds =[]" v-model:main-active-index="defaultParams.regionType" @click-item="changeRegin" v-model:active-id="defaultParams.regionIds"  :items="areaList" />
        </van-dropdown-item>
        <!-- 餐类 -->
        <van-dropdown-item :title-class="defaultParams.cateTypeIds?.length> 0 ? 'active-dropdown-item' : ''">
          <template #title>
            <div class="mr-5 font-size-vant">餐类</div>
            <div class="default-down-icon"></div>
          </template>

          <van-cell-group>
            <van-cell title="全选" clickable>
              <template #right-icon>
                <van-checkbox v-model="isCheckAllCl" :indeterminate="isIndeterminateCl" @change="checkAllChangeCl">
                </van-checkbox>
              </template>
            </van-cell>
          </van-cell-group>

          <van-checkbox-group v-model="defaultParams.cateTypeIds" @change="checkedResultChangeCl">
            <van-cell-group>
              <van-cell
                v-for="(item, index) in foodTypeList"
                clickable
                :key="index"
                :title="item.name"
                @click="toggle(index)"
              >
                <template #right-icon>
                  <van-checkbox :name="item.id" :ref="(el) => (checkboxRefsCl[index] = el)" @click.stop />
                </template>
              </van-cell>
            </van-cell-group>
          </van-checkbox-group>
          <a-affix :offset-bottom="0">
                <van-cell>
                  <van-row class="width100" justify="space-between">
                    <van-button round style="width: 48%" @click="defaultParams.cateTypeIds = []">重置</van-button>
                    <van-button round style="width: 48%" type="primary" @click="onSearch">确定</van-button>
                  </van-row>
                </van-cell>
              </a-affix>
        </van-dropdown-item>
        <!-- 价格 -->
        <van-dropdown-item :title-class="defaultParams.consumptionPerStartY || defaultParams.consumptionPerEndY  ? 'active-dropdown-item' : ''">
          <template #title>
            <div class="mr-5 font-size-vant">价格</div>
            <div class="default-down-icon"></div>
          </template>
          <van-cell-group class="mb-10">
            <van-field label-align="top">
              <template #label>
                <div class="flex width100 justify-content-between align-items-center">
                  <div class="left">
                    <span class="color-main">人均价格</span>
                  </div>
                  <div class="right color-main" @click="reSetMoney">重置</div>
                </div>
              </template>

              <template #input>
                <div class="flex justify-content-between align-items-center">
                  <div class="left flex">
                    <van-field autocomplete="off" v-model="defaultParams.consumptionPerStartY" type="digit" placeholder="最低价" />
                    <span class="flex align-items-center">—</span>
                    <van-field autocomplete="off" v-model="defaultParams.consumptionPerEndY" type="digit" placeholder="最高价" />
                  </div>
                  <van-button type="primary" size="small" style="width: 80px" @click="onSearch">查询</van-button>
                </div>
              </template>
            </van-field>
          </van-cell-group>
        </van-dropdown-item>
        <!-- 星级 -->
        <van-dropdown-item :title-class="defaultParams.starLevel?.length> 0 ? 'active-dropdown-item' : ''">
          <template #title>
            <div class="mr-5 font-size-vant">星级</div>
            <div class="default-down-icon"></div>
          </template>

          <van-cell-group>
            <van-cell title="全选" clickable >
              <template #right-icon>
                <van-checkbox v-model="isCheckAllXj" :indeterminate="isIndeterminateXj" @change="checkAllChangeXj">
                </van-checkbox>
              </template>
            </van-cell>
          </van-cell-group>

          <van-checkbox-group v-model="defaultParams.starLevel" @change="checkedResultChangeXj">
            <van-cell-group>
              <van-cell
                v-for="(item, index) in hotalStarList"
                clickable
                :key="index"
                :title="item.text"
                @click="toggleXj(index)"
              >
                <template #right-icon>
                  <van-checkbox :name="item.value" :ref="(el) => (checkboxRefsXj[index] = el)" @click.stop />
                </template>
              </van-cell>
              <van-cell>
                <van-row class="width100" justify="space-between">
                  <van-button round style="width: 48%" @click="defaultParams.starLevel = []">重置</van-button>
                  <van-button round style="width: 48%" type="primary" @click="onSearch">确定</van-button>
                </van-row>
              </van-cell>
            </van-cell-group>
          </van-checkbox-group>
        </van-dropdown-item>
      </van-dropdown-menu>
    </van-sticky>

    <van-list
      v-model:loading="hotelLoading"
      :finished="hotelFinished"
      :finished-text="hotelList.length ? '没有更多了' : ''"
      @load="loadHotelList"
      :immediate-check="false"
    >

        <van-cell v-for="(item, index) in hotelList" :key="index" @click="goToDetail(item.id)">
          <div class="flex align-items-center">
            <div class="hotel-img mr-10">
              <img style="width: 100%; height:100%;;" :src="getImgUrl(item.album)" alt="">
            </div>
            <div class="main flex-1">
              <div>
                <van-text-ellipsis class="color-main hotel-item-title" :content="item.fullname" />
              </div>

              <van-text-ellipsis class="hotel-item-foods" :content="getFoodTypeStr(item.cateType)" />

              <van-text-ellipsis class="hotel-item-address" rows="2" :content="item.address" />

              <div>
                <van-tag
                  class="mr-5"
                  plain
                  type="primary"
                  v-for="(mark, markI) in item.landMark"
                  v-show="markI < 2"
                  :key="markI"
                  >{{ mark.name }}</van-tag
                >
              </div>

              <div class="hotel-item-consumptionPer">{{ `人均 ¥${item.consumptionPer / 100}` }}</div>
            </div>
          </div>
        </van-cell>
    </van-list>
    <van-empty v-if="!hotelLoading && hotelList.length == 0" description="暂无数据" />

    

    <!-- 订餐折扣政策 -->
    <van-dialog v-model:show="discountDialog" :close-on-click-overlay="true" title="折扣政策" >
      <div style="padding:0 20px; height: 400px; overflow-y: auto;" @scroll="handleScroll">
      
        <div style="font-size: 12px; font-weight: bold; text-align: center; margin-bottom: 10px;">2024年青岛市协议酒店折扣政策</div>
        <table border="1" cellspacing="0" cellpadding="5" style="text-align: center; font-size: 8px;">
          <tbody>
            <tr>
              <td rowspan="2">区域</td>
              <td rowspan="2">酒店名称</td>
              <td colspan="3">资源政策</td>
            </tr>
            <tr>
              <td>服务费</td>
              <td>中餐折扣</td>
              <td>自助餐价格</td>
            </tr>
            <tr v-for="(item,index) in hotelsData" :key="index">
              <td style="width:100px;">{{item.regionName}}</td>
          <td style="width:200px;">{{item.fullname}}</td>
          <td style="width:100px;">{{item.serviceFee}}%</td>
          <td style="width:300px;">{{item.discountPolicy ||'-'}}</td>
          <td style="width:100px;">{{item.buffetPrices ? item.buffetPrices + '元/人':'-' }}</td>
            </tr>
          </tbody>
        </table>

      </div>
      <template #footer>
        <van-row class="width100 " style="padding: 10px 0;" justify="center" >
          
          <van-button type="primary" size="small"  @click="discountDialog = false" >
            <span>已阅读</span>
          </van-button>
        </van-row>
      </template>
    </van-dialog>

    <!-- 折扣政策  -->
    <van-floating-bubble v-model:offset="offset" position="left" @click="discountDialog = true" axis="y" :gap="4" >
      <template #default>
        <div>
          折扣
          <br />
          政策
        </div>
      </template>
    </van-floating-bubble>


    <van-tabbar v-model="active" route>
      <van-tabbar-item replace to="/restaurant/hotelList">
        <span>餐厅预订</span>
        <template #icon="props">
          <img :src="props.active ? bookIcon.active : bookIcon.inactive" />
        </template>
      </van-tabbar-item>

      <van-tabbar-item replace to="/restaurant/orderList">
        <span>订单中心</span>
        <template #icon="props">
          <img :src="props.active ? orderIcon.active : orderIcon.inactive" />
        </template>
      </van-tabbar-item>
    </van-tabbar>
    
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, onBeforeUpdate,watch } from 'vue';
import type { Ref } from 'vue';
import { restaurantApi } from '@haierbusiness-front/apis';

import { RHotelParams } from '@haierbusiness-front/common-libs';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';
import { vShow } from 'vue';

const router = getCurrentRouter();

const value = ref('');

// 
const active = ref(0);

const bookIcon = {
  active: new URL('@/assets/image/restaurant/tab_book_active.png', import.meta.url).href,
  inactive: new URL('@/assets/image/restaurant/tab_book.png', import.meta.url).href,
};

const orderIcon = {
  active: new URL('@/assets/image/restaurant/tab_order_active.png', import.meta.url).href,
  inactive: new URL('@/assets/image/restaurant/tab_order.png', import.meta.url).href,
};

const onChange = () => {};
// 查询订餐折扣政策
const hotelsData = ref([])
const discountDialog= ref<boolean>(false);

const offset = ref({ x: 5, y: 400 });

const dataList = () => {
  const params = {}
  restaurantApi.dataList(params).then(res => {
    hotelsData.value = res.data
  })
}

const goToSearch = () => {
  router.push({ path: '/restaurant/search'});
}

// 下拉菜单相关
const activeIds = ref([1, 2]);
const menuRef = ref()
const activeIndex = ref(0);
const areaList = ref([]);
const clickTreeItem = (val) => {
console.log(999, val)
}
interface ComObj {
  id?: string;
  name?: string;
}

const changeRegin = (val) => {
  console.log(999, val)
  if (val.id == 999) {
    if (defaultParams.value.regionIds.length == areaList.value[0].children.length -1) {
      defaultParams.value.regionIds = []
    }else {
      const idList = areaList.value[0].children.map(item => item.id)
      defaultParams.value.regionIds = idList
    }
  }else {
    if (defaultParams.value.regionIds.length == areaList.value[0].children.length -2) {
      const idList = areaList.value[0].children.map(item => item.id)
      defaultParams.value.regionIds = idList
    }else {
      defaultParams.value.regionIds = defaultParams.value.regionIds.filter(item => item != 999); // 过滤掉等于999
    }
  }
  
  defaultParams.value.pageNum= 0;
  hotelList.value = []
  loadHotelList()
}
//  --------餐类选择相关
const isCheckAllCl = ref(false);
const foodTypeList = ref<Array<ComObj>>([]);

const isIndeterminateCl = ref(false);
const checkboxRefsCl = ref([]);
const toggle = (index) => {
  checkboxRefsCl.value[index].toggle();
};
onBeforeUpdate(() => {
  checkboxRefsCl.value = [];
});

const checkAllChangeCl = (val: boolean) => {
  let checkedCount = defaultParams.value.cateTypeIds.length;
  if (val) {
    defaultParams.value.cateTypeIds = foodTypeList.value.map((item) => item.id);
  } else if (checkedCount === foodTypeList.value.length) {
    defaultParams.value.cateTypeIds = [];
  }
  isIndeterminateCl.value = false;
};

const checkedResultChangeCl = (value: string[]) => {
  const checkedCount = value.length;
  isCheckAllCl.value = checkedCount === foodTypeList.value.length;
  isIndeterminateCl.value = checkedCount > 0 && checkedCount < foodTypeList.value.length;
};
//  --------餐类选择相关

// --------星级选择相关

const hotalStarList = [
  { text: '白金五星级', value: 55 },
  { text: '五星级', value: 50 },
  { text: '准五星', value: 45 },
  { text: '四星级', value: 40 },
  { text: '准四星', value: 35 },

  { text: '三星级', value: 30 },
  { text: '准三星', value: 25 },
  { text: '二星级', value: 20 },
  { text: '准二星', value: 15 },

  { text: '一星级', value: 10 },
  { text: '准一星', value: 5 },
];
const checkboxRefsXj = ref([]);
const toggleXj = (index) => {
  checkboxRefsXj.value[index].toggle();
};

const isCheckAllXj = ref(false);
const isIndeterminateXj = ref(false);

const checkAllChangeXj = (val: boolean) => {
  let checkedCount = defaultParams.value.starLevel.length;
  if (val) {
    defaultParams.value.starLevel = hotalStarList.map((item) => item.value);
  } else if (checkedCount === hotalStarList.length) {
    defaultParams.value.starLevel = [];
  }
  isIndeterminateXj.value = false;
};

const checkedResultChangeXj = (value: string[]) => {
  const checkedCount = value.length;
  isCheckAllXj.value = checkedCount === hotalStarList.length;
  isIndeterminateXj.value = checkedCount > 0 && checkedCount < hotalStarList.length;
};

// --------------星级选择相关


const hotelLoading = ref<boolean>(false);
const hotelFinished = ref<boolean>(false);
const hotelList = ref([]);
const hotelTotal = ref<number>(0);
const defaultParams = ref<RHotelParams>({
  regionType: 0, // 0
  pageNum: 0,
  pageSize: 20,
  keyword: null, // 关键字查询
  cateClassIds: [], // 菜系查询
  regionIds: [], // 区域查询
  starLevel: [], // 星级

  consumptionPerStart: null, //人均消费 分
  consumptionPerEnd: null, //人均消费 分
  cateTypeIds: [], // 餐类
});



const loadHotelList = () => {
  if(!defaultParams.value.keyword) {
    defaultParams.value.keyword = null
  }
  defaultParams.value.pageNum++;
  restaurantApi.hotelList(defaultParams.value).then((res) => {
    // 加载状态结束
    hotelLoading.value = false;
    hotelList.value = [...hotelList.value, ...res.data];
    hotelTotal.value = res.total ?? 0;
    // 数据全部加载完成
    if (hotelList.value.length >= hotelTotal.value) {
      hotelFinished.value = true;
    }
  });
};

interface cateType {
  id: string;
  name: string;
}
const getFoodTypeStr = (list: Array<cateType>) => {
  if (list && list.length > 0) {
    return list.map((item) => item.name).join(' / ');
  }
};

const goToDetail = (id: string) => {
  router.push({ path: '/restaurant/hotelDetail', query: { hotelId: id } });
};

const businessList = import.meta.env.VITE_BUSINESSTRAVEL_URL;
const getImgUrl = (imgList) => {
  const food = []
    const restaurant = []
    const room = []
  if (imgList && imgList.length > 0) {
    
    // var type: Short? = null  //0：普通图片； 1：大堂；  2： 外景； 3： 餐厅； 4： 客房
    imgList?.forEach((item) => {
      switch (item.type) {
        case 0:
          food.push(item);
          break;
        case 1:
          room.push(item);
          break;
        case 2:
          restaurant.push(item);
          break;
        case 3:
          room.push(item);
          break;
        case 4:
          room.push(item);
          break;

        default:
          break;
      }
    });
  }
  if (restaurant.length > 0) {
    return `${businessList}api/common/v1/file/download/${restaurant[0].id}`
  } else if (room.length > 0) {
    return `${businessList}api/common/v1/file/download/${room[0].id}`

  }else if(food.length >0) {
    return `${businessList}api/common/v1/file/download/${food[0].id}`

  }else {
    return ''
  }
}


const onSearch = () => {
  defaultParams.value.pageNum= 0;
  hotelList.value = []
    loadHotelList()
    menuRef.value.close()
}

const reSetMoney = () => {
  defaultParams.value.consumptionPerStartY = '';
   defaultParams.value.consumptionPerEndY = ''
}

// 菜系
const cateClassList = ref<Array<ComObj>>([])
const cateClassActive = ref<number|string>('');

watch(
  () => cateClassActive.value,
  (val: string|number) => {
    
    if (val == '0') {
      let arr = cateClassList.value.map(item => item.id)
      arr.shift()
      defaultParams.value.cateClassIds = arr
    }else {
      defaultParams.value.cateClassIds = [val];
    }
  },
)

watch(
  () => defaultParams.value.consumptionPerStartY,
  (val: string|number) => {
    if (!val) {
      defaultParams.value.consumptionPerStart = null
    }else {
      defaultParams.value.consumptionPerStart = defaultParams.value.consumptionPerStartY * 100
    }
  },
)

watch(
() => defaultParams.value.consumptionPerEndY,
  (val: string|number) => {
    if (!val) {
      
      defaultParams.value.consumptionPerEnd = null
    }else {
      defaultParams.value.consumptionPerEnd = defaultParams.value.consumptionPerEndY * 100
    }
  },
)

watch(
  () => defaultParams.value.cateClassIds,
  (val) => {
    if (!val.length) {
      return
    }
    
    defaultParams.value.pageNum= 0;
    hotelList.value = []
    loadHotelList()
  },
)

// 查询酒店菜系数据
const getCateClass = () => {
  return new Promise((resolve, reject)=>{

      const params = {}
      restaurantApi.findCateClass(params).then(res => {
        cateClassList.value = res.data
        cateClassList.value.unshift(
          {
            name: '全部',
            id: '0'
          }
        )
        cateClassActive.value = res.data[0].id
        resolve('success')
      })
      
    })

  
}

// 查询酒店餐类数据
const getCateType = () => {
  return new Promise((resolve, reject)=>{

    const params = {}
    restaurantApi.findCateType(params).then(res => {
      foodTypeList.value = res.data
      // isCheckAllCl.value = true
      // defaultParams.value.cateTypeIds = res.data.map(item => item.id)
      resolve('success')
    })

  })
}

// 查询酒店行政区数据
const getRegin = () => {
  return new Promise((resolve, reject)=>{
    const params = {}
    restaurantApi.findRegionList(params).then(res => {
      res.data.forEach(item => {
        item.text = item.name
      })
      areaList.value = [...areaList.value, {
        text: '行政区域',
        children: [ {id: 999, text: '全青岛'},...res.data],
      }]

      restaurantApi.getBusinessCircle(params).then(circle => {
        circle.data.forEach(item => {
          item.text = item.circleName
        })
        areaList.value = [...areaList.value, {
          text: '热门商圈',
          children: circle.data,
        }]

        resolve('success')
      })

    })
  })
}

// 查询商圈数据
const getBusinessCircle = () => {
  return new Promise((resolve, reject)=>{
    const params = {}
    
  })
}

// 查询城市数据
const getCityList = () => {
  const params = {}
  restaurantApi.findCityList(params).then(res => {

  })
}

// 查询列表
const getList = () => {
    Promise.all([getRegin(),getCateType(),getCateClass() ]).then(res => {
      // 拿到两个参数后再进行查询操作
      loadHotelList()
	})
}



onMounted(() => {
  getList()
  dataList()

});
</script>



<script lang="ts">
export default {
  name: 'hotelList'
}
</script>

<style lang='less' scoped>
@import url(./common.less);
</style>