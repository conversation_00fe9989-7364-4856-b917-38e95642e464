<script setup lang="ts">
import {
  Badge as hBadge,
  Progress as hProgress,
  <PERSON><PERSON> as hButton,
  Col as hCol,
  DatePicker as hDatePicker,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Modal as hModal,
  Popconfirm as hPopconfirm,
  Popover as hPopover,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  message,
  TableProps
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { DownOutlined, ExclamationCircleOutlined, PlusOutlined, UploadOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons-vue';
import { enterpriseApi, reasonApi } from '@haierbusiness-front/apis';
import {
  IEnterpriseListRequest,
  IEnterprise
} from '@haierbusiness-front/common-libs';
import { errorModal, routerParam } from '@haierbusiness-front/utils';
import dayjs, { Dayjs } from 'dayjs';
import { computed, onMounted, ref, watch } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useRouter } from "vue-router";
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import EditDialog from './edit-dialog.vue'

const router = useRouter()
const columns: ColumnType[] = [
  {
    title: 'id',
    dataIndex: 'id',
    align: 'center',
  },
  {
    title: '原因类型',
    dataIndex: 'reasonType',
    align: 'center',
  },
  {
    title: '关联产品',
    dataIndex: 'productCode',
    align: 'center',
  },
  {
    title: '原因编码',
    dataIndex: 'reasonCode',
    align: 'center',
  },
  {
    title: '原因',
    dataIndex: 'reasonInfo',
    align: 'center',
  },
  
  {
    title: '最后一次修改人',
    dataIndex: 'lastModifiedName',
    align: 'center',
  },
 
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '180px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<IEnterpriseListRequest>({ })

const manageParams = ref()

const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(reasonApi.list, {
  defaultParams: [{}],
  manual: false
});

const reset = () => {
  searchParam.value = {}
}

const dataSource = computed(() => (data.value?.records?.filter(item => {
  if (item.iconUrl) {
     item.files = [{
    name: `${item.productName}`,
    thumbUrl: item.iconUrl
  }]
  }
  return item
}) || []));

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
 
};


// 新增表单相关
const { visible, editData, handleCreate, handleEdit, onDialogClose, handleOk } =
  useEditDialog<IEnterprise, IEnterprise>(reasonApi, "原因", () => listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum,
    pageSize: data.value?.pageSize,
  }))

  const { handleDelete } = useDelete(reasonApi, () => listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum,
    pageSize: data.value?.pageSize,
  }))


</script>

<template>

  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">

    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="productCode">原因类型：</label>
          </h-col>
          <h-col :span="4">
            <h-select ref="select" style="width: 200px;" v-model:value="searchParam.reasonType" allow-clear>
              <h-select-option :value="10">
                未预订原因
              </h-select-option>
              <h-select-option :value="20">
                超标原因
              </h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="productName">原因名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="productName" v-model:value="searchParam.reasonInfo" placeholder="" autocomplete="off"
              allow-clear />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: left;">
            <h-button type="primary" @click="handleCreate">
              <PlusOutlined /> 新增原因
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            
            <template v-if="column.dataIndex === 'reasonType'">
              <div v-if="record.reasonType == 10">未预定原因</div>
              <div v-if="record.reasonType == 20">超标原因</div>
            </template>

            <template v-if="column.dataIndex === 'productCode'">
              <div v-if="record.productCode == '01'">飞机</div>
              <div v-else-if="record.productCode == '02'">火车</div>
              <div v-else-if="record.productCode == '04'">酒店</div>
              <div v-else>-</div>
            </template>


            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link"  @click="handleEdit(record)">编辑</h-button>
              <h-button type="link"  @click="handleDelete(record.id)">删除</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

  </div>

  <div v-if="visible">
    <edit-dialog
        :show="visible"
        :data="editData"
        @cancel="onDialogClose"
        @ok="handleOk"
    >
    </edit-dialog>
  </div>
</template>

<style scoped lang="less">
.logo-div {
  text-align: center;
  width: 92%;

  .logo-title {
    line-height: 50px;
    color: rgb(255, 255, 255);
    font-size: 22px;
    font-weight: 600;
    text-shadow: 1px 0.5px 1px rgb(22, 18, 1);
  }

  .logo-second-title {
    line-height: 0px;
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-size: 4px;
    text-shadow: 1px 0.5px 1px rgb(2 58 255 / 72%);
  }
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color .3s;
}

.masking {
  background-color: #24232385;
  position: fixed;
  z-index: 10000;
  height: 100%;
  width: 100%;
}
</style>
