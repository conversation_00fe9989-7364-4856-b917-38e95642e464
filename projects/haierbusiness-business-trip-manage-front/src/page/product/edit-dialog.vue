<script lang="ts" setup>
import { Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem,
     Input as hInput, Textarea as hTextarea, Upload as hUpload, Button as hButton } from 'ant-design-vue';
import { computed, ref, watch } from "vue";
import type { Ref } from "vue";
import { UploadOutlined, UserOutlined, NotificationOutlined, AppstoreOutlined, MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons-vue';
import { fileApi } from '@haierbusiness-front/apis';
import { message,Upload } from 'ant-design-vue';
import type { UploadProps } from 'ant-design-vue';

import {
  IEnterprise
} from '@haierbusiness-front/common-libs';

interface Props {
    show: boolean;
    data: IEnterprise | null;
}

const props = withDefaults(defineProps<Props>(), {
show: false,
});

const from = ref();
const confirmLoading = ref(false);

const defaultData: IEnterprise = {
    name: '',
    code: '',
    state: 0,
    id: null,
    description: ''
};

const rules = {
    code: [{ required: true, message: "请输入企业编码！" }],
    name: [{ required: true, message: "请输入企业名称！" }],
    state: [{ required: true, message: "请选择状态！" }],
};

const enterprise: Ref<IEnterprise> = ref(
({ ...props.data } as IEnterprise) || defaultData
);

watch(props, (newValue) => {
    enterprise.value = ({ ...newValue.data } as IEnterprise) || defaultData;
});

const emit = defineEmits(["cancel", "ok"]);

const visible = computed(() => props.show);

const handleOk = () => {
confirmLoading.value = true;
from.value
    .validate()
    .then(() => {
    enterprise.value.files && enterprise.value.files.length > 0 ?enterprise.value.iconUrl  = enterprise.value.files[0].filePath || enterprise.value.files[0].thumbUrl : '';
    emit("ok", enterprise.value, () => {
        confirmLoading.value = false;
    });
    // 不管是否成功 延时一秒关闭loading
    setTimeout(() => {
      confirmLoading.value ? confirmLoading.value = false: '';
    }, 1000)
    })
    .catch(() => {
        confirmLoading.value = false;
    });
};
const uploadLoading = ref(false);

const baseUrl = import.meta.env.VITE_BUSINESS_URL;

const upload = (options: any) => {
  uploadLoading.value = true;
  const formData = new FormData();
  formData.append('file', options.file);
  fileApi
    .upload(formData)
    .then((it) => {

      options.file.filePath = baseUrl + it.path
      options.file.fileName = options.file.name

      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  
  if (!(file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/gif' || file.type === 'image/jpg')) {
   
    message.warning('只能上传 JPG, JPEG, PNG 和GIF文件哦！');
    return Upload.LIST_IGNORE;
  }
  return true;
}
</script>

<template>
    <h-modal
      v-model:open="visible"
      :title="enterprise.id ? '编辑产品' : '新增产品'"
      :width="600"
      @cancel="$emit('cancel')"
      :confirmLoading="confirmLoading"
      @ok="handleOk"
    >
      <h-form
        ref="from"
        :model="enterprise"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
        :rules="rules"
      >
        <h-form-item label="产品编码" name="productCode" >
          <h-input v-model:value="enterprise.productCode" :disabled="enterprise.id" />
        </h-form-item>
        <h-form-item label="产品名称" name="productName">
          <h-input v-model:value="enterprise.productName" />
        </h-form-item>
        <h-form-item label="服务费" name="serviceAmount">
          <a-input-number v-model:value="enterprise.serviceAmount" prefix="￥" style="width: 100%" />
        </h-form-item>
        <h-form-item label="保险费" name="insuranceAmount">
           <a-input-number v-model:value="enterprise.insuranceAmount" prefix="￥" style="width: 100%" />
        </h-form-item>

        <h-form-item label="图标" name="files">
           <h-upload
              v-model:fileList="enterprise.files"
              name="logo"
              list-type="picture"
              :custom-request="upload"
              :before-upload="beforeUpload"
              :max-count="1"
            >
              <h-button>
                <template #icon><UploadOutlined /></template>
                上传图标
              </h-button>
              <div class="text" style="font-size:14px; color: #999;">上传支持的文件格式gif、jpg、jpeg、png..</div>

            </h-upload>
        </h-form-item>
      </h-form>
    </h-modal>
</template>


<style lang="less" scoped>
.important {
color: red;
}
</style>
  