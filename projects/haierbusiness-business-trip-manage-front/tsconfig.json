{"compilerOptions": {"forceConsistentCasingInFileNames": true, "target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "Node", "strict": true, "jsx": "preserve", "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["ESNext", "DOM"], "skipLibCheck": true, "noEmit": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "../../utils/storageUtil.ts", "../../common-libs/src/pay/model/basicModel.ts", "../../common-libs/src/pay/model/compositionPayModel.ts", "../../common-libs/src/pay/model/payModel.ts", "../../common-libs/src/pay/constant/payTypeConstant.ts", "../../common-libs/src/pay/constant/headerConstant.ts"], "references": [{"path": "./tsconfig.node.json"}]}