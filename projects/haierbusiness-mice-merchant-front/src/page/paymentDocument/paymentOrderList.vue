<!-- 付款单列表 -->
<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  InputNumber as hInputNumber,
  DatePicker as hDatePicker,
  Modal,
  message,
  TableProps,
  Table as ATable,
  Tabs as ATabs,
  TabPane as ATabPane,
  Tooltip,
  Form,
  FormItem,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { paymentFromApi } from '@haierbusiness-front/apis';
import { IPaymentFromFilter, IPaymentFrom } from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted, nextTick } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables';
import { resolveParam, routerParam } from '@haierbusiness-front/utils';
import router from '../../router';
import { InvoiceStatusEnum, InvoiceStatusMap, InvoiceStatusTagColorMap } from '@haierbusiness-front/common-libs';
import Actions from '@haierbusiness-front/components/actions/Actions.vue';
import type { MenuItemType, MenuInfo } from 'ant-design-vue/lib/menu/src/interface';

const currentRouter = ref();

onMounted(async () => {
  currentRouter.value = await router;
  // 页面初始化时调用列表接口
  listApiRun({
    pageNum: 1,
    pageSize: 10,
  });
});

const columns: ColumnType[] = [
  {
    title: '付款单号',
    dataIndex: 'receivePaymentCode',
    width: '250px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '服务商名称',
    dataIndex: 'merchantName',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '账单总金额',
    dataIndex: 'totalAmount',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '结算比例',
    dataIndex: 'settlementRate',
    width: '80px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text}%` : ''),
  },
  {
    title: '付款金额',
    dataIndex: 'receivePaymentAmount',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: '130px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center',
  },
];
const searchParam = ref<IPaymentFromFilter>({});

const { data, run: listApiRun, loading, current, pageSize } = usePagination(paymentFromApi.getMerchantPayMentList);

const reset = () => {
  searchParam.value = {};
  beginAndEnd.value = undefined;
};

const dataSource = computed(() => data.value?.records || []);

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total || 0,
  current: data.value?.pageNum || 1,
  pageSize: data.value?.pageSize || 10,
  style: { justifyContent: 'center' },
}));

const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};

const detailVisible = ref(false);
const currentDetailRecord = ref<any>(null);
const detailLoading = ref(false);

//查看
const handleView = (record?: any) => {
  if (record && record.id) {
    // 有传入记录，获取详情数据
    detailLoading.value = true;
    // 这里可以调用详情接口获取完整数据，暂时直接使用传入的record
    currentDetailRecord.value = record;
    detailVisible.value = true;
    detailLoading.value = false;

    // 如果需要调用详情接口，可以取消下面的注释
    // paymentFromApi.getDetails(record.id).then((res) => {
    //   currentDetailRecord.value = res
    //   detailVisible.value = true
    // }).catch((error) => {
    //   console.error('获取详情失败：', error)
    //   message.error('获取详情失败，请重试')
    // }).finally(() => {
    //   detailLoading.value = false
    // })
  } else {
    // 关闭详情弹窗
    detailVisible.value = false;
    currentDetailRecord.value = null;
  }
};

const beginAndEnd = ref<[Dayjs, Dayjs]>();
watch(
  () => beginAndEnd.value,
  (n: any, o: any) => {
    if (n) {
      searchParam.value.startTime = dayjs(n[0]).format('YYYY-MM-DD 00:00:00');
      searchParam.value.endTime = dayjs(n[1]).format('YYYY-MM-DD 23:59:59');
    } else {
      searchParam.value.startTime = undefined;
      searchParam.value.endTime = undefined;
    }
  },
);

// 详情弹窗相关
const activeKey = ref('1');

// 上传发票相关
const invoiceModalVisible = ref(false);
const invoiceLoading = ref(false);
const currentInvoiceRecord = ref<any>(null);
const invoiceForm = ref({
  invoiceType: 1, // 默认选择国旅
  invoiceDate: '',
  invoiceNumber: '',
  invoiceAmount: undefined,
});
const invoiceFormRef = ref();
const invoiceRules = {
  invoiceDate: [
    { required: true, message: '请选择发票日期', trigger: 'change' }
  ],
  invoiceNumber: [
    { required: true, message: '请输入发票号', trigger: 'blur' }
  ],
  invoiceAmount: [
    { required: true, message: '请输入发票金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '发票金额必须大于0', trigger: 'blur' }
  ]
};

// 打开上传发票弹窗
const openInvoiceModal = (record: any) => {
  currentInvoiceRecord.value = record;
  invoiceModalVisible.value = true;
  // 重置表单
  invoiceForm.value = {
    invoiceType: 1,
    invoiceDate: '',
    invoiceNumber: '',
    invoiceAmount: undefined,
  };
};

// 关闭上传发票弹窗
const closeInvoiceModal = () => {
  Modal.confirm({
    title: '确认取消',
    content: '确定要取消上传发票吗？已填写的内容将不会保存。',
    okText: '确定',
    cancelText: '取消',
    onOk() {
      invoiceModalVisible.value = false;
      currentInvoiceRecord.value = null;
      invoiceForm.value = {
        invoiceType: 1,
        invoiceDate: '',
        invoiceNumber: '',
        invoiceAmount: undefined,
      };
      invoiceFormRef.value?.clearValidate();
    }
  });
};

// 提交上传发票
const submitInvoice = () => {
  if (invoiceLoading.value) return;
  invoiceFormRef.value?.validate().then(() => {
    if (!currentInvoiceRecord.value?.id) {
      message.error('记录ID不存在');
      return;
    }

    invoiceLoading.value = true;

    // 调用uploadPaymentInvoice接口
    paymentFromApi
      .uploadPaymentInvoice({
        paymentId: currentInvoiceRecord.value.id,
        paymentCode: currentInvoiceRecord.value.paymentCode,
        invoiceDate: invoiceForm.value.invoiceDate,
        invoiceNumber: invoiceForm.value.invoiceNumber,
        invoiceAmount: invoiceForm.value.invoiceAmount,
      })
      .then(() => {
        message.success('发票上传成功');
        closeInvoiceModal();
        // 刷新列表
        listApiRun({
          ...searchParam.value,
          pageNum: data.value?.pageNum || 1,
          pageSize: data.value?.pageSize || 10,
        });
      })
      .catch((error) => {
        console.error('上传发票失败:', error);
        message.error('上传发票失败，请重试');
      })
      .finally(() => {
        invoiceLoading.value = false;
      });
  }).catch(() => {
    // 表单验证失败，不做额外处理，Ant Design会自动显示错误信息
  });
};

// 注释：打开上传付款凭证弹窗 - 已改为路由跳转，此函数不再使用
// const openUploadModal = (record: any) => {
//   detailLoading.value = true;
//   viewMode.value = 'upload';
//   // 获取详情数据
//   currentDetailRecord.value = record;
//   detailVisible.value = true;
//   fileList.value = [];
//   ReasonsRejection.value = '';
//   detailLoading.value = false;
// };

//发票
const invoiceColumns: ColumnType<DataType>[] = [
  {
    title: '发票号',
    dataIndex: 'name',
  },
  {
    title: '发票日期',
    dataIndex: 'age',
  },
  {
    title: '发票金额',
    dataIndex: '',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
];

// 详情页订单表格列
const detailOrderColumns: ColumnType<DataType>[] = [
  {
    title: '会议单号',
    dataIndex: 'mainCode',
    width: '150px',
    align: 'center',
  },
  {
    title: '会议时间',
    width: '200px',
    align: 'center',
    customRender: ({ record }) => {
      if (record.startDate && record.endDate) {
        return `${record.startDate} 至 ${record.endDate}`;
      }
      return '';
    },
  },
  {
    title: '会议负责人',
    dataIndex: 'operatorName',
    width: '120px',
    align: 'center',
  },
  {
    title: '账单金额',
    dataIndex: 'billAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '付款比例',
    dataIndex: 'feeRate',
    width: '100px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}%` : ''),
  },
  {
    title: '付款金额',
    dataIndex: 'paymentAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
];

// 详情页发票表格列
const detailInvoiceColumns: ColumnType<DataType>[] = [
  {
    title: '发票号',
    dataIndex: 'invoiceNumber',
    align: 'center',
  },
  {
    title: '发票日期',
    dataIndex: 'invoiceDate',
    align: 'center',
  },
  {
    title: '发票金额',
    dataIndex: 'invoiceAmount',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
];

// 计算发票金额合计
const calculateInvoiceTotal = () => {
  if (!currentDetailRecord.value) return '0';
  const invoiceData = getInvoiceData();
  const total = invoiceData.reduce((sum: number, item: any) => sum + (item.invoiceAmount || 0), 0);
  return `${total}元`;
};

// 获取发票数据
const getInvoiceData = () => {
  if (!currentDetailRecord.value || !currentDetailRecord.value.attachmentFiles) {
    return [];
  }
  return currentDetailRecord.value.attachmentFiles
    .filter((file: any) => file.type === 2) // 假设type=2为发票类型
    .map((file: any, index: number) => ({
      key: index,
      invoiceNumber: file.invoiceNumber || `85645433${index}`,
      invoiceDate: file.invoiceDate || '2025.1.21',
      invoiceAmount: file.invoiceAmount || 15000,
    }));
};

// 处理菜单点击事件
const handleMenuClick = (record: any, e: MenuInfo) => {
  const key = e.key as string;
  switch (key) {
    case 'upload':
      // 跳转到付款凭证上传页面
      const params = {
        paymentId: record.id,
        paymentCode: record.paymentCode,
      };
      currentRouter.value.push({
        path: '/mice-merchant/paymentDocument/paymentDetails',
        query: {
          record: routerParam(params),
        },
      });
      break;
    case 'uploadInvoice':
      // 打开上传发票弹窗
      openInvoiceModal(record);
      break;
    default:
      break;
  }
};

// 计算菜单选项
const getMenuOptions = (record: any) => {
  const options: MenuItemType[] = [];

  // 根据状态添加不同的操作选项
  if (record.status == InvoiceStatusEnum.PENDING_SERVICE_PROVIDER_UPLOAD) {
    // 等待服务商上传发票：显示上传发票按钮
    options.push({
      key: 'uploadInvoice',
      label: '上传发票',
    });
  }
  // 已完成状态只显示查看，不需要额外的菜单选项

  return options;
};
</script>

<template>
  <div class="main-container">
    <h-row :align="'middle'">
      <h-col :span="24" class="margin-bottom-10">
        <h-row :align="'middle'" class="padding-standard">
          <h-col :span="2" class="text-right-padding">
            <label for="serviceProvider">服务商：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.merchantCode" placeholder="请输入服务商" allow-clear maxlength="50" />
          </h-col>

          <h-col :span="3" class="text-right-padding">
            <label for="createTime">付款单创建时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="beginAndEnd" value-format="YYYY-MM-DD" class="width-full" allow-clear />
          </h-col>

          <h-col :span="2" class="text-right-padding">
            <label for="status">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select v-model:value="searchParam.status" placeholder="请选择状态" allow-clear class="width-full">
              <h-select-option :value="InvoiceStatusEnum.PENDING_SERVICE_PROVIDER_UPLOAD">
                {{ InvoiceStatusMap[InvoiceStatusEnum.PENDING_SERVICE_PROVIDER_UPLOAD] }}
              </h-select-option>
              <h-select-option :value="InvoiceStatusEnum.PENDING_FINANCIAL_CONFIRM">
                {{ InvoiceStatusMap[InvoiceStatusEnum.PENDING_FINANCIAL_CONFIRM] }}
              </h-select-option>
              <h-select-option :value="InvoiceStatusEnum.FINANCIAL_REJECTED">
                {{ InvoiceStatusMap[InvoiceStatusEnum.FINANCIAL_REJECTED] }}
              </h-select-option>
              <h-select-option :value="InvoiceStatusEnum.COMPLETED">
                {{ InvoiceStatusMap[InvoiceStatusEnum.COMPLETED] }}
              </h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" class="padding-standard">
          <h-col :span="24" class="text-right">
            <h-button class="margin-right-10" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })" :loading="loading" :disabled="loading">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :row-key="(record) => record.id"
          :size="'small'"
          :data-source="dataSource"
          :pagination="pagination"
          :scroll="{ y: 550 }"
          :loading="loading"
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'paymentCode'">
              <Tooltip :title="record.paymentCode">
                <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
                  {{ record.paymentCode }}
                </div>
              </Tooltip>
            </template>
            <template v-if="column.dataIndex === 'merchantName'">
              <Tooltip :title="record.merchantName">
                <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
                  {{ record.merchantName }}
                </div>
              </Tooltip>
            </template>
            <template v-if="column.dataIndex === 'status'">
              {{ InvoiceStatusMap[record.status as keyof typeof InvoiceStatusMap] || '未知状态' }}
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <div class="operator-buttons">
                <h-button type="link" @click="handleView(record)">查看</h-button>
                <Actions
                  v-if="getMenuOptions(record).length > 0"
                  :menu-options="getMenuOptions(record)"
                  :on-menu-click="(e) => handleMenuClick(record, e)"
                >
                </Actions>
              </div>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

    <!-- 删除了独立的上传付款凭证弹窗，已合并到详情弹窗中 -->

    <!-- 付款单详情弹窗 -->
    <Modal
      v-model:open="detailVisible"
      title="付款单详情"
      :footer="null"
      @cancel="handleView()"
      width="800px"
      :loading="detailLoading"
    >
      <div v-if="currentDetailRecord" class="modal-padding">
        <!-- 基本信息 -->
        <div class="info-section">
          <div class="info-item-12"><strong>付款单号：</strong>{{ currentDetailRecord.paymentCode }}</div>
          <div class="info-item-12"><strong>服务商名称：</strong>{{ currentDetailRecord.merchantName }}</div>
          <div class="info-item-12"><strong>付款总金额：</strong>{{ currentDetailRecord.totalAmount }}元</div>
          <!-- 显示已上传的付款凭证 -->
          <div class="info-item-12">
            <strong>付款凭证：</strong>
            <template v-if="currentDetailRecord.attachmentFiles && currentDetailRecord.attachmentFiles.length > 0">
              <template v-for="(file, index) in currentDetailRecord.attachmentFiles" :key="index">
                <a :href="file.path" target="_blank" class="file-link">
                  {{ file.path ? file.path.split('/').pop() || `付款凭证${index + 1}` : `付款凭证${index + 1}` }}
                </a>
              </template>
            </template>
            <span v-else>无</span>
          </div>
        </div>

        <!-- Tab页 -->
        <a-tabs v-model:activeKey="activeKey">
          <a-tab-pane key="1" tab="订单">
            <h-table
              :columns="detailOrderColumns"
              :data-source="currentDetailRecord.paymentRecordsDetails || []"
              :pagination="false"
              size="small"
              bordered
              class="table-margin"
            >
            </h-table>
          </a-tab-pane>
          <a-tab-pane key="2" tab="发票">
            <div class="info-item"><strong>发票金额合计：</strong>{{ calculateInvoiceTotal() }}</div>
            <h-table
              :columns="detailInvoiceColumns"
              :data-source="getInvoiceData()"
              :pagination="false"
              size="small"
              bordered
              class="table-margin"
            >
            </h-table>
          </a-tab-pane>
        </a-tabs>

        <!-- 底部按钮 -->
        <div class="footer-buttons">
          <h-button class="margin-right-10" @click="handleView()">取消</h-button>
          <h-button type="primary" @click="handleView()">确定</h-button>
        </div>
      </div>
    </Modal>

    <!-- 上传发票弹窗 -->
    <Modal v-model:open="invoiceModalVisible" title="上传发票" :footer="null" @cancel="closeInvoiceModal" width="500px">
      <div class="modal-content">
        <div class="modal-info-item"><strong>付款单号：</strong>{{ currentInvoiceRecord?.paymentCode }}</div>
        <div class="modal-info-item"><strong>服务商名称：</strong>{{ currentInvoiceRecord?.merchantName }}</div>

        <Form
          ref="invoiceFormRef"
          :model="invoiceForm"
          :rules="invoiceRules"
          layout="vertical"
          :hideRequiredMark="true"
        >
          <FormItem name="invoiceDate" label="发票日期：">
            <h-date-picker
              v-model:value="invoiceForm.invoiceDate"
              class="width-full"
              placeholder="请选择发票日期"
              value-format="YYYY-MM-DD"
            />
          </FormItem>

          <FormItem name="invoiceNumber" label="发票号：">
            <h-input v-model:value="invoiceForm.invoiceNumber" placeholder="请输入发票号" maxlength="50" />
          </FormItem>

          <FormItem name="invoiceAmount" label="发票金额：">
            <h-input-number
              v-model:value="invoiceForm.invoiceAmount"
              placeholder="请输入发票金额（元）"
              class="width-full"
              :min="0"
              :precision="2"
              :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
              :parser="value => value!.replace(/\D/g, '').replace(/\B(?=(\d{2})+(?!\d))/g, '.')"
            />
          </FormItem>
        </Form>

        <div class="modal-footer">
          <h-button class="button-margin" @click="closeInvoiceModal">取消</h-button>
          <h-button type="primary" @click="submitInvoice" :loading="invoiceLoading" :disabled="invoiceLoading">确定</h-button>
        </div>
      </div>
    </Modal>
  </div>
</template>

<style scoped lang="less">
.main-container {
  background-color: #ffff;
  height: 100%;
  width: 100%;
  padding: 10px 10px 0px 10px;
  overflow: auto;
}

.margin-bottom-10 {
  margin-bottom: 10px;
}

.padding-standard {
  padding: 10px 10px 0px 10px;
}

.text-right-padding {
  text-align: right;
  padding-right: 10px;
}

.width-full {
  width: 100%;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.margin-right-10 {
  margin-right: 10px;
}

.modal-padding {
  padding: 20px 0;
}

.info-item {
  margin-bottom: 16px;
}

.info-item-12 {
  margin-bottom: 12px;
}

.upload-section {
  margin-bottom: 16px;
}

.textarea-section {
  margin-bottom: 16px;
}

.footer-buttons {
  text-align: right;
  margin-top: 20px;
}

.table-margin {
  margin-top: 10px;
}

.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.operator-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.info-section {
  margin-bottom: 20px;
}

.upload-wrapper {
  margin-bottom: 20px;
}

.label-bold {
  font-weight: bold;
}

.textarea-margin {
  margin-top: 8px;
}

.file-link {
  margin-right: 10px;
  color: #1890ff;
}

.operator-buttons :deep(.ant-btn) {
  padding: 0 4px;
  font-size: 14px;
}

// 上传发票弹框样式
.modal-content {
  padding: 20px 0;
}

.modal-info-item {
  margin-bottom: 12px;
}

.modal-footer {
  text-align: right;
  margin-top: 20px;
}

.button-margin {
  margin-right: 10px;
}
</style>
