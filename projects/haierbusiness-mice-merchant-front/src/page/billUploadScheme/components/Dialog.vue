<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import {
  SearchParams,
  DialogProps,
  DialogEmits,
  RegionOption,
  OrderListConstant,
  MiceTypeConstantO,
  MiceDistrictTypeConstant,
  MiceItemConstant,
} from '@haierbusiness-front/common-libs';
import { miceBidManOrderListApi } from '@haierbusiness-front/apis';

// 定义组件属性
const props = defineProps({
  title: '高级搜索',
  width: 500,
  user1: {
    type: Object,
    default: () => ({}),
  },
});
// 定义组件事件
const emit = defineEmits();

// 抽屉显示状态
const visible = ref(false);

// 表单数据
const formState = reactive<SearchParams>({
  states: [],
  statesName: [],
  keyword: undefined,
  startDateStart: undefined,
  startDateEnd: undefined,
  winTheBidMerchantName: undefined,
  winTheBidMerchantCode: undefined,
  isSpecialPowers: undefined,
  isUrgent: undefined,
  isAppoint: undefined,
  demandProvince: undefined,
  demandCity: undefined,
  demandDistrict: undefined,
  isConsignment: undefined,
  isPlaceOrder: undefined,
  isChange: undefined,
  isBidFailure: undefined,
  demandItem: undefined,
  mainCode: undefined,
  miceName: undefined,
  miceType: undefined,
  operatorCode: undefined,
  operatorName: undefined,
  operatorPhone: undefined,
  consultantUserCode: undefined,
  consultantUserName: undefined,
  contactUserCode: undefined,
  contactUserName: undefined,
  contactUserPhone: undefined,
  districtType: undefined,
  pageNum: 1,
  pageSize: 10,
});

// 关闭抽屉
const onClose = () => {
  visible.value = false;
  emit('close');
};

// 重置表单
const resetForm = () => {
  Object.assign(formState, {
    states: [],
    statesName: [],
    keyword: undefined,
    startDateStart: undefined,
    startDateEnd: undefined,
    winTheBidMerchantName: undefined,
    winTheBidMerchantCode: undefined,
    isSpecialPowers: undefined,
    isUrgent: undefined,
    isAppoint: undefined,
    demandProvince: undefined,
    demandCity: undefined,
    demandDistrict: undefined,
    isConsignment: undefined,
    isPlaceOrder: undefined,
    isChange: undefined,
    isBidFailure: undefined,
    demandItem: undefined,
    mainCode: undefined,
    miceName: undefined,
    miceType: undefined,
    operatorCode: undefined,
    operatorName: undefined,
    operatorPhone: undefined,
    consultantUserCode: undefined,
    consultantUserName: undefined,
    contactUserCode: undefined,
    contactUserName: undefined,
    contactUserPhone: undefined,
    districtType: undefined,
    pageNum: 1,
    pageSize: 10,
  });
  emit('resetSearch', formState);
};

// 搜索处理
const handleSearch = () => {
  if (formState.statesName.length === 0) formState.states = [];
  emit('search', formState);
  visible.value = false;
};

// 处理状态选择变化
const handleStateChange = (values: string[]) => {
  formState.statesName = values;
  OrderListConstant.toArray().forEach((item) => {
    if (values.includes(item.desc)) formState.states.push(item.code);
  });
};
const demandProvince = ref([]);
const demandCity = ref([]);
const demandDistrict = ref([]);
const handleDemandProvinceChange = (value: number) => {
  if (!value) return;
  miceBidManOrderListApi
    .districtTree({
      id: formState.demandProvince,
      providerCode: 'MT',
      subdistrict: 1,
    })
    .then((res) => {
      demandCity.value = res.children;
    });
};
const handleDemandCityChange = (value: number) => {
  if (!value) return;
  miceBidManOrderListApi
    .districtTree({
      id: formState.demandCity,
      providerCode: 'MT',
      subdistrict: 1,
    })
    .then((res) => {
      demandDistrict.value = res.children;
    });
};
onMounted(() => {
  miceBidManOrderListApi
    .districtTree({
      id: 1,
      providerCode: 'MT',
      subdistrict: 1,
    })
    .then((res) => {
      demandProvince.value = res.children;
    });
});
// 暴露方法给父组件
defineExpose({
  show: (user) => {
    visible.value = true;
    Object.assign(formState, { ...user });
  },
  hide: () => (visible.value = false),
  reset: resetForm,
  formState,
});
</script>

<template>
  <!-- 高级搜索抽屉 -->
  <a-drawer :title="props.title" placement="right" :width="props.width" :visible="visible" @close="onClose">
    <a-form :model="formState" layout="vertical">
      <!-- 订单状态 -->
      <!-- <a-form-item label="订单状态">
        <a-select
          v-model:value="formState.statesName"
          mode="multiple"
          allowClear
          :options="OrderListConstant.toArray()"
          :field-names="{ label: 'desc', value: 'desc' }"
          placeholder="请选择订单状态"
          @change="handleStateChange"
        >
        </a-select>
      </a-form-item> -->
      <a-form-item label="会议单号">
        <a-input :maxlength="200" allowClear v-model:value="formState.mainCode" placeholder="请输入主编码" />
      </a-form-item>
      <!-- 会议名称 -->
      <a-form-item label="会议名称">
        <a-input :maxlength="200" allowClear v-model:value="formState.miceName" placeholder="请输入会议名称" />
      </a-form-item>
      <!-- 地区类型 -->
      <a-form-item label="会议地点">
        <a-select allowClear v-model:value="formState.districtType" placeholder="请选择">
          <a-select-option v-for="item in MiceDistrictTypeConstant.toArray()" :key="item.code" :value="item.code">{{
            item.desc
          }}</a-select-option>
        </a-select>
      </a-form-item>
      <!-- <a-form-item label="经办人姓名">
        <a-input allowClear :maxlength="200" v-model:value="formState.operatorName" placeholder="请输入经办人姓名" />
      </a-form-item>
      <a-form-item label="经办人工号">
        <a-input allowClear :maxlength="200" v-model:value="formState.operatorCode" placeholder="请输入经办人工号" />
      </a-form-item>
      <a-form-item label="对接人姓名">
        <a-input allowClear :maxlength="200" v-model:value="formState.contactUserName" placeholder="请输入对接人姓名" />
      </a-form-item>
      <a-form-item label="对接人工号">
        <a-input allowClear :maxlength="200" v-model:value="formState.contactUserCode" placeholder="请输入对接人工号" />
      </a-form-item>
      <a-form-item label="顾问姓名">
        <a-input
          allowClear
          :maxlength="200"
          v-model:value="formState.consultantUserName"
          placeholder="请输入顾问姓名"
        />
      </a-form-item>
      <a-form-item label="顾问工号">
        <a-input
          allowClear
          :maxlength="200"
          v-model:value="formState.consultantUserCode"
          placeholder="请输入顾问工号"
        />
      </a-form-item> -->

      <a-form-item label="会议开始时间">
        <a-date-picker v-model:value="formState.startDateStart" placeholder="请选择会议开始时间起" />
        <a-date-picker
          style="margin-left: 10px"
          v-model:value="formState.startDateEnd"
          placeholder="请选择会议开始时间止"
        />
      </a-form-item>
      <!-- <a-form-item label="中标服务商名称">
        <a-input :maxlength="200" allowClear v-model:value="formState.winTheBidMerchantName" placeholder="中标服务商" />
      </a-form-item>
      <a-form-item label="中标服务商编码">
        <a-input
          :maxlength="200"
          allowClear
          v-model:value="formState.winTheBidMerchantCode"
          placeholder="中标服务商编码"
        />
      </a-form-item> -->
      <!-- <a-form-item label="流程类型">
        <a-input
          :maxlength="200"
          allowClear
          v-model:value="formState.winTheBidMerchantCode"
          placeholder="中标服务商编码"
        />
      </a-form-item> -->
      <!-- 需求项 -->
      <!-- <a-form-item label="包含需求">
        <a-select v-model:value="formState.demandItem" allowClear placeholder="请选择需求项" style="width: 100%">
          <a-select-option v-for="item in MiceItemConstant.toArray()" :key="item.code" :value="item.code">
            {{ item.desc }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="是否开通特殊权限">
        <a-select allowClear v-model:value="formState.isSpecialPowers" placeholder="请选择">
          <a-select-option
            v-for="item in [
              { code: '0', desc: '否' },
              { code: '1', desc: '是' },
            ]"
            :key="item.code"
            :value="item.code"
            >{{ item.desc }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item label="是否开通加急">
        <a-select allowClear v-model:value="formState.isUrgent" placeholder="请选择">
          <a-select-option
            v-for="item in [
              { code: '0', desc: '否' },
              { code: '1', desc: '是' },
            ]"
            :key="item.code"
            :value="item.code"
            >{{ item.desc }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item label="是否指定招标搜索">
        <a-select allowClear v-model:value="formState.isAppoint" placeholder="请选择">
          <a-select-option
            v-for="item in [
              { code: '0', desc: '否' },
              { code: '1', desc: '是' },
            ]"
            :key="item.code"
            :value="item.code"
            >{{ item.desc }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item label="需求省">
        <a-select
          @change="handleDemandProvinceChange"
          allowClear
          v-model:value="formState.demandProvince"
          placeholder="请选择"
        >
          <a-select-option v-for="item in demandProvince" :key="item.id" :value="item.id">{{
            item.name
          }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="需求市" v-show="demandCity.length > 0">
        <a-select @change="handleDemandCityChange" allowClear v-model:value="formState.demandCity" placeholder="请选择">
          <a-select-option v-for="item in demandCity" :key="item.id" :value="item.id">{{ item.name }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="需求区" v-show="demandDistrict.length > 0">
        <a-select allowClear v-model:value="formState.demandDistrict" placeholder="请选择">
          <a-select-option v-for="item in demandDistrict" :key="item.id" :value="item.id">{{
            item.name
          }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="礼品是否发货">
        <a-select allowClear v-model:value="formState.isConsignment" placeholder="请选择">
          <a-select-option
            v-for="item in [
              { code: '0', desc: '否' },
              { code: '1', desc: '是' },
            ]"
            :key="item.code"
            :value="item.code"
            >{{ item.desc }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item label="礼品是否下单">
        <a-select allowClear v-model:value="formState.isPlaceOrder" placeholder="请选择">
          <a-select-option
            v-for="item in [
              { code: '0', desc: '否' },
              { code: '1', desc: '是' },
            ]"
            :key="item.code"
            :value="item.code"
            >{{ item.desc }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item label="执行方案是否变更过">
        <a-select allowClear v-model:value="formState.isChange" placeholder="请选择">
          <a-select-option
            v-for="item in [
              { code: '0', desc: '否' },
              { code: '1', desc: '是' },
            ]"
            :key="item.code"
            :value="item.code"
            >{{ item.desc }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item label="是否流标">
        <a-select allowClear v-model:value="formState.isBidFailure" placeholder="请选择">
          <a-select-option
            v-for="item in [
              { code: '0', desc: '否' },
              { code: '1', desc: '是' },
            ]"
            :key="item.code"
            :value="item.code"
            >{{ item.desc }}</a-select-option
          >
        </a-select>
      </a-form-item> -->
      <!-- <a-form-item label="关键词搜索">
        <a-input
          :maxlength="200"
          allowClear
          v-model:value="formState.keyword"
          placeholder="单号/名称/经办人/地点搜索"
        />
      </a-form-item>
      <a-form-item label="会议类型">
        <a-select v-model:value="formState.miceType" allowClear placeholder="请选择会议类型" style="width: 100%">
          <a-select-option v-for="item in MiceTypeConstantO.toArray()" :key="item.code" :value="item.code">
            {{ item.desc }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="经办人姓名">
        <a-input allowClear :maxlength="200" v-model:value="formState.operatorName" placeholder="请输入经办人姓名" />
      </a-form-item>
      <a-form-item label="经办人电话">
        <a-input allowClear :maxlength="200" v-model:value="formState.operatorPhone" placeholder="请输入经办人电话" />
      </a-form-item>
         <a-form-item label="联系人工号">
        <a-input allowClear :maxlength="200" v-model:value="formState.contactUserCode" placeholder="请输入联系人工号" />
      </a-form-item>
      <a-form-item label="联系人姓名">
        <a-input allowClear :maxlength="200" v-model:value="formState.contactUserName" placeholder="请输入联系人姓名" />
      </a-form-item>
      <a-form-item allowClear label="联系人电话">
        <a-input
          allowClear
          :maxlength="200"
          v-model:value="formState.contactUserPhone"
          placeholder="请输入联系人电话"
        />
      </a-form-item> -->
      <a-affix :offset-bottom="0">
        <a-space class="search_content">
          <a-button type="primary" @click="handleSearch">搜索</a-button>
          <a-button @click="resetForm">重置</a-button>
        </a-space>
      </a-affix>
    </a-form>
  </a-drawer>
</template>

<style scoped lang="less">
.ant-form-item {
  margin-bottom: 16px;
}
.search_content {
  width: 100%;
  padding: 12px 0;
  background: #fff;
}
</style>
