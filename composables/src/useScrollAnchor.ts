import { ref, onMounted, onUnmounted } from 'vue';

export function useScrollAnchor() {
  const activeLink = ref('');
  let observer: IntersectionObserver;
  let sectionIds: string[] = [];

  const initObserver = () => {
    const sections = Array.from(document.querySelectorAll<HTMLElement>('section[id]')).filter(
      (el) => el.offsetParent !== null,
    );

    sectionIds = sections.map((s) => s.id);

    observer = new IntersectionObserver(
      (entries) => {
        const activeIndex = sectionIds.findIndex((id) => `#${id}` === activeLink.value);

        // 向上滚动检测逻辑
        if (activeIndex > 0) {
          const prevSection = document.getElementById(sectionIds[activeIndex - 1]);
          if (prevSection) {
            const root = document.querySelector('.ant-layout-content');
            const rect = prevSection.getBoundingClientRect();
            const rootRect = root.getBoundingClientRect();

            // 使用root容器的尺寸代替window
            const isFullyVisible = rect.top >= rootRect.top && rect.bottom <= rootRect.bottom;

            if (isFullyVisible && rect.top - rootRect.top <= 300) {
              activeLink.value = `#${sectionIds[activeIndex - 1]}`;
              return;
            }
          }
        }

        // 向下滚动检测逻辑
        const nextSectionId = sectionIds[activeIndex + 1];
        if (nextSectionId) {
          const nextSection = document.getElementById(nextSectionId);
          if (nextSection && nextSection.getBoundingClientRect().top <= 500) {
            activeLink.value = `#${nextSectionId}`;
            return;
          }
        }

        // 默认可见区域检测
        const visibleEntries = entries
          .filter((entry) => entry.isIntersecting)
          .sort((a, b) => a.boundingClientRect.top - b.boundingClientRect.top);

        if (visibleEntries.length > 0) {
          activeLink.value = `#${(visibleEntries[0].target as HTMLElement).id}`;
        }
      },
      {
        root: document.querySelector('.ant-layout-content'),
        threshold: 0.2,
        rootMargin: '0px',
      },
    );

    sections.forEach((section) => observer.observe(section));
  };

  onMounted(initObserver);
  onUnmounted(() => observer?.disconnect());

  return { activeLink };
}
