import { IPageRequest } from "../../basic";

export class IPascalCase<PERSON>ilter extends IPageRequest {
    begin?:string
    end?:string
    presentName?: string
    state?: boolean
}

// 规格配置类型
export interface ISpecConfig {
    keyName: string;
    listValueName: string[];
}

// 价格配置类型
export interface IPriceConfig {
    valueName: string[] | string;
    skuId: number;
    marketPrice: number;
    costPrice: number;
    salePrice: number;
    profit: number;
    state: number | boolean;
    path: Array<{
        type: string;
        path: string;
    }>;
}

// 价格配置表格数据类型
export interface IPriceTableItem {
    key: number;
    specName: string;
    specCombination: string[];
    imageUrls: string[];
    marketPrice: number;
    costPrice: number;
    salePrice: number;
    profitRate: number;
    skuId: string;
    state: number | boolean;
}

// 规格类型配置
export interface ISpecType {
    id: number;
    type: string;
    values: string[];
}

// 保险描述项类型
export interface IInsuranceDescriptionItem {
    key: string;
    value: string;
}

// 保险描述对象类型 (用于表单)
export interface IInsuranceDescription {
    ageRequirement?: string;
    waitingPeriod?: string;
    insuranceNotice?: string;
    claimProcess?: string;
    warmTips?: string;
    faq?: string;
    exemptionTerms?: string;
}

// 保险表单数据类型
export interface InsuranceFormData {
    insuranceName: string;
    premium: string;
    ageRequirement: string;
    waitingPeriod: string;
    productFeatures: string[];
    insuranceNotice: string;
    claimProcess: string;
    warmTips: string;
    faq: string;
    exemptionTerms: string;
    productTerms: string | string[]; // 支持单个文件或多个文件
}

export class IPascalCase {
    id?: number | null
    creator?:string
    createTime?: string
    updater?: string
    updateTime?: string
    presentName?: string
    insuranceName?: string
    presentDesc?: string
    description?: string | IInsuranceDescription | IInsuranceDescriptionItem[]
    // 新增的独立字段
    ageRequire?: string
    waitingPeriod?: string
    notice?: string
    claimProcess?: string
    hintMessage?: string
    question?: string
    imgUrl?: string
    spuId?: string
    images?: Array<{
        caId?: number
        path: string
        type: string
    }>
    path?: Array<{
        caId?: number
        path: string
        type: string
    }>
    pathList?: Array<{
        caId?: number
        path: string
        type: number
    }>
    specComb?: string
    state?: number
    marketPrice?: number
    price?: number
    costPrice?: number
    salePrice?: number
    profitRate?: number
    config?: ISpecConfig[]
    priceConfig?: IPriceConfig[]
}