import { IPageRequest } from "../../basic";


export class ITriveFilter {
    travelCount?:string | number
    planeCount?: string| number
    hotelCount?: string| number

    taxiCount?:string | number
    trainCount?: string| number
    provinceCount?: string| number

    countryCount?:string | number
    saveAmount?: string| number
    year?: string| number

    travelHabit?:string | number
    advanceRate?: string| number
    userSavedAmountAll?: string| number

    userSavedAmountHotel?:string | number
    userSavedAmountPlane?: string| number
    userSavingAmountAll?: string| number

    userSavingAmountPlaneAdvance?:string | number
    userSavingAmountHotelModify?: string| number
    userSavingAmountPlaneHour?: string| number

    username?:string | number
    travelSumReportDetailId?: string | number
    isShowOtherData?: string | number
}

export interface TravelSumReportPageReq {
    travelSumReportId?: string | number	
    reportTitle?: string | number		
    reportScope?: string | number	
    sendTimeStart?: string | number	
    sendTimeEnd?: string | number	
    cycleTimeStart?: string | number	
    cycleTimeEnd?: string | number	
    status?: string | number	
    createBy?: string | number	
    createName?: string | number	
}
export interface TravelSumReportPageRes {
    /* */
    data: {
      /* */
      pageNum: number;
  
      /* */
      pageSize: number;
  
      /* */
      total: number;
  
      /* */
      totalPage: number;
  
      /* */
      records: {
        /* */
        createBy: string;
  
        /* */
        createName: string;
  
        /* */
        lastModifiedBy: string;
  
        /* */
        lastModifiedName: string;
  
        /* */
        gmtCreate: Record<string, unknown>;
  
        /* */
        gmtModified: Record<string, unknown>;
  
        /*季度报告id */
        travelSumReportId: number;
  
        /*时间周期开始时间 */
        cycleTimeStart: Record<string, unknown>;
  
        /*时间周期结束时间 */
        cycleTimeEnd: Record<string, unknown>;
  
        /*报告标题 */
        reportTitle: string;
  
        /*报告范围,可用值:YEAR,FIRST_HALF_YEAR,SECOND_HALF_YEAR,FIRST_QUARTER,SECOND_QUARTER,THIRD_QUARTER,FOURTH_QUARTER,January,February,March,April,May,June,July,August,September,October,November,December */
        reportScope: string;
  
        /*报告范围名称 */
        reportScopeName: string;
  
        /*发送方式  1 立即发送  2定时发送 */
        sendMethod: number;
  
        /*发送时间 */
        sendTime: Record<string, unknown>;
  
        /*状态 */
        status: number;
  
        /*实际发送时间 */
        actualSendTime: Record<string, unknown>;
  
        /*总数量 */
        totalCount: number;
  
        /*阅读数量 */
        readCount: number;
      }[];
    };
  
    /* */
    code: string;
  
    /* */
    message: string;
  
    /* */
    success: boolean;
  }

  export interface CreateTravelSumReportParams {
    /* */
    pageNum?: number;
  
    /* */
    pageSize?: number;
  
    /*报告标题 */
    reportTitle: string;
  
    /*报告范围,可用值:YEAR,FIRST_HALF_YEAR,SECOND_HALF_YEAR,FIRST_QUARTER,SECOND_QUARTER,THIRD_QUARTER,FOURTH_QUARTER,January,February,March,April,May,June,July,August,September,October,November,December */
    reportScope: string;
  
    /*发送时间 */
    sendTime: Record<string, unknown>;
  
    /*发送方式 1 立即发送  2定时发送 */
    sendMethod: number;
  
    /*时间周期 */
    cycleTimeRange: Record<string, unknown>[];
  
    /* */
    needPage?: boolean;
  }
