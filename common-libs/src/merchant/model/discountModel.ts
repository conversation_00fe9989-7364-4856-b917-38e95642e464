import { IPageRequest } from "../../basic";

export class IDiscount<PERSON>ilter extends IPageRequest {
    merName?: string
    merCode?: string
    merType?: string
    isEnable?: number
    startTime?:string
    endTime?:string
}

export class IProductFilter {
    keyword?: string
    pageIndex?: number
    pageSize?: number
}

export class IDiscount {
    id?: number | null
    createBy?: string
    createName?: string
    lastModifiedBy?: string
    lastModifiedName?: string
    gmtCreate?: string
    gmtModified?: string
    merName?: string
    merCode?: string
    icon?: string
    merType?: number
    discountDesc?: string
    details?: string
    isEnable?: number
    discountValue?: string
    isDelete?: number
    startTime?: string
    endTime?: string
    userList?: Array<IChargeUser>
}

export class IChargeUser {
    name?: string
    code?: string
    email?: string
    phone?: string
}

export class IMerchantType {
    /* */
    id?: number;

    /* */
    type?: string;
}