// BillFileTypeConstant

type keys = 'LOCKED' | 'SUBMITTED' | 'EXPIRED_CANCEL' | 'MANUAL_CANCEL' | 'FLIGHT' | 'TRAIN';

export const lockStateConstant = {
  LOCKED: { code: 10, desc: '已锁定' },
  SUBMITTED: { code: 20, desc: '已提报' },
  EXPIRED_CANCEL: { code: 30, desc: '到期取消' },
  MANUAL_CANCEL: { code: 40, desc: '手动取消' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in lockStateConstant) {
      const item = lockStateConstant[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(lockStateConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return lockStateConstant[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
