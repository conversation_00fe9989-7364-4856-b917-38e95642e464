import { IPageRequest } from "../../basic";

export interface IHaierAccountBillInfo {
    id?: number;
    code?: string;
    cvpCode?: string;

    /**
     * cvp异常账单回调信息是否已读
     * 1: 已读
     */
    readCvpErrorMessage?:boolean;

    /**
     * cvp异常账单回调信息
     */
    cvpErrorMessage?: string;

    /**
     * 1: 不可撤回
     */
    cvpRollback?: boolean;

    /**
     * 付款流水单号
     */
    balancePaymentCode?: string;

    /**
     * 0: 未通知 1: 通知成功 2: 通知异常
     */
    notifiedReleaseCvp?: number;

    /**
     * 付款时间
     */
    balancePaymentTime?: string;

    /**
     * 记账号
     */
    bookVoucherCode?: string;

    /**
     * 账单所属年月yyyy-MM
     */
    periodYearMonth?: string;

    /**
     * 汇总周期-开始日期（含此日期）
     */
    periodBegin?: string;

    /**
     * 汇总周期-结束日期（含此日期）
     */
    periodEnd?: string;

    /**
     * 总结算金额(含税)
     */
    totalAmount?: number;

    /**
     * 总税额
     */
    totalTaxAmount?: number;

    /**
     * 总结算金额(不含税)
     */
    exTaxTotalAmount?: number;

    /**
     * 结算单状态：00：已取消汇总；10：已汇总；20：已确认；30：已结算；
     */
    state?: number;

    /**
     * 来源系统
     */
    applicationCode?: string;

    /**
     * 预算类型DEPT_BCC_1等
     */
    budgetType?: string;

    /**
     * 预算系统编码GEMS/BCC等
     */
    budgetSysCode?: string;


    /**
     * 供应商编码
     */
    providerCode?: string;

    providerName?: string;

    /**
     * 预算部门
     */
    departmentCode?: string;

    departmentName?: string;

    /**
     * 结算单位
     */
    accountCompanyCode?: string;

    accountCompanyName?: string;

    /**
     * 客户编码
     */
    customCode?: string;

    customName?: string;

    /**
     * WBS
     */
    wbsCode?: string;

    wbsName?: string;

    /**
     * 研发项目
     */
    projectCode?: string;

    projectName?: string;

    /**
     * 地产项目
     */
    dcProjectCode?: string;

    dcProjectName?: string;

    /**
     * 地产分期
     */
    dcItemCode?: string;

    dcItemName?: string;

    /**
     * 费用项目
     */
    feeItem?: string;
    feeItemName?: string;

    /**
     * 合同号
     */
    contractCode?: string;

    contractName?: string;

    createBy?: string;

    gmtCreate?: string;

    lastModifiedBy?: string;

    gmtModified?: string;

    vCode?: string
    receivingBank?: string
    payeeOpenBank?: string
    accNumOfRecBank?: string
}
export interface IHabDetailInfo {
    id?:number;

   /**
    * 结算单号
    */
    accountCode?:string;

   /**
    * 1：支付；2：退款；
    */
    type?:number;

   /**
    * 支付单号
    */
    paymentCode?:string;

   /**
    * 业务单号
    */
    businessCode?:string;

   /**
    * 退款支付单号
    */
    refundPaymentCode?:string;

   /**
    * 退款业务单号
    */
    refundBusinessCode?:string;

   /**
    * 预算系统单号
    */
    budgetCode?:string;

    /**
     * 订单实付金额
     */
    orderAmount?:number;
    
   /**
    * 结算金额(含税)
    * 1) 支付单 = 商品成交单价(含税)*数量
    * 2) 售后单 = -退款总额
    */
    amount?:number;

   /**
    * 不含税结算金额
    */
    exTaxAmount?:number;

   /**
    * 税额
    * 税额=含税金额/(1+税率)*税率，取两位小数。最终需满足 税额+含税金额/(1+税率)=含税金额
    */
    taxAmount?:number;

   /**
    * 应用编码
    */
    applicationCode?:string;

   /**
    * 成交时间
    */
    dealTime?:string;

   /**
    * 产品唯一id
    */
    productId?:string;

   /**
    * 产品名称
    */
    productName?:string;

   /**
    * 商品数量
    */
    num?:number;

   /**
    * 商品成交单价(含税)
    */
    unitPrice?:number;

   /**
    * 1: 商品 2: 运费
    */
    productType?:number;

   /**
    * 税收分类编码
    */
    taxCode?:string;

   /**
    * 税率
    */
    taxRate?:number;

   /**
    * 影像地址
    */
    attachmentUrl?:string;

   /**
    * 规格型号
    */
    specModel?:string;

   /**
    * 单位
    */
    unitCode?:string;

   /**
    * 1: 已确认
    * 0: 未确认
    */
    confirmed?:number;

   /**
    * 预算确认失败原因
    */
    confirmErrorMessage?:string;
    gmtCreate?:string;
    gmtModified?:string;
    createBy?:string;
    lastModifiedBy?:string;
}
