
type keys = 'NOT' | 'SUCCESS' | 'ERROR';

/**
 * 预算CVP通知状态
 * 0: 未通知 1: 通知成功 2: 通知异常
 */
export const BudgetNotifiedReleaseCvpStateConstant = {
  NOT: { "code": 0, "name": "未通知" },
  SUCCESS: { "code": 1, "name": "通知成功" },
  ERROR: { "code": 2, "name": "通知异常" },

  ofCode: (code?: number): { "code": number, "name": string } | null => {
    for (const key in BudgetNotifiedReleaseCvpStateConstant) {
      const item = BudgetNotifiedReleaseCvpStateConstant[key as keys];
      if (code === item.code) {
        return item;
      }
    }
    return null;
  }
}