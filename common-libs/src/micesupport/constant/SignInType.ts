export enum SignInType {
  ACTIVE_SIGN = 1,
  AGENT_SIGN = 2,
}

export enum SignInApproveStatus {
  WAIT_APPROVE = 10,
  APPROVE_PASS = 20,
  APPROVE_REJECT = 30,
}

export const SignInTypeMap = new Map<number, string>([
  [SignInType.ACTIVE_SIGN, "主动签到"],
  [SignInType.AGENT_SIGN, "代签到"],
]);

export const SignInApproveStatusMap = new Map<number, string>([
  [SignInApproveStatus.WAIT_APPROVE, "待审核"],
  [SignInApproveStatus.APPROVE_PASS, "审核通过"],
  [SignInApproveStatus.APPROVE_REJECT, "审核驳回"],
]);