export interface IGetLoginUrl {
    applicationCode?: string | null
    /**
     * 1: 本地用户名密码登录
     * 2: 海尔统一code登录
     * 3: 微信登录
     * 4: 手机号登录
     * 5: 海尔统一token登录
     * 6: 生态系统session登录
     */
    loginType?: number
    redirectUrl?: string | null
}

export interface IGetLoginUrlResponse {
    loginUrl?: string 
}

export interface IUsernameLogin {
    username?: string
    password?: string
}

export interface IHaierIamCodeLogin {
    code?: string
    applicationCode?: string
}

export interface IHaierIamTokenLogin {
    token?: string
}
export interface ITravelSessionLogin {
    session?: string
}
export interface IUsernameHworkLogin {
    userCode?: string
}
export interface ISupermarketTokenLogin {
    token?: string
}

export interface IWyyTokenLogin {
    token?: string
}

export interface ILoginSearchParams {
    urlSearch: URLSearchParams
}

export interface IIamLocalLogin {
    urlSearch: URLSearchParams
}

export interface IIamCodeLogin {
    urlSearch: URLSearchParams
}

export interface IIamTokenLogin {
    urlSearch: URLSearchParams
}

export interface ILoginResult {
    data: any
}

export interface IamLogoutRequest {
    token?: string
}