// 获取酒店列表
export interface RHotelParams {
  cityId?: number;
  pageNum: number;
  pageSize: number;
  consumptionPerStart?: number|string;
  consumptionPerEnd?: number|string;
  consumptionPerStartY?: number|string;
  consumptionPerEndY?: number|string;
  starLevel?: Array<number|string>;
  regionIds?: Array<number|string>;
  cateClassIds?: Array<number|string>;
  cateTypeIds?: Array<number|string>;
  keyword?: string;
  regionType?: number;
}

export interface RHotelSingleParams {
  hotelId: string
}

// 获取订餐订单列表
export interface ROrderParams {
  keyword?: string;
  ownerIsOwn?: string;
  pageNum: number;
  pageSize: number;
  orderState?: string;
  payType?: string;
  hotelName?: string;
  timeType?: string;
  orderStatusNew?: string; // 订单状态
  startTime?: string; // 入住开始日期
  endTime?: string; // 入住结束日期
}

export interface ROrderDetailParams {
  orderCode?: string;

}

// 酒店
// 餐类
export interface RCate {
  id?: string; // id
  name?: string; // name

}
// 菜品
export interface RDish {
  id?: string; // id
  name?: string; // 菜名
  material?: string; //原料
  description?: string; //描述
  health?: string; //功效
}
export interface RMark {
  id?: string; // id
  name?: string; // name
}

export interface RApplicant {
   email?: string;
      mobile?: string;
      name?: string;
      username?: string;
      departmentName?: string; //所属部门
      orderCode?: string;
      phone?: string;
}

export interface RBudget {
  paymentType?: string; //支付方式
  paymentCard?: string;//酒店储值卡
}

export interface  RTreatInfo {

  accompanyCount?:number; //陪同人数
  accompanyLeader?:string;
  guestCompany?:string; //来宾单位
  guestCount?:number; //来宾人数
  mainGuestNames?:string; //主宾姓名，可多个人
  mainGuestPosition?:string; //0, //主宾职务
  needParking?:number;//是否预留车位，0：否；1：是；
  needSeatCard?:number; //需要座牌
  seatOrderId?:number;
  signerMobile?:string; //签单人手机
  signerName?:string; //签单人姓名
  vehicleNo?:string;//车牌号，可多个
  needSeatCardBoolean?:string;
  orderCode?: string;
  seatOrderFileLoading?: boolean;
  seatOrderFileList?:string[];
}

export interface RCancelParams {
  orderCode: string;
}

export interface RHotelInfo {
  fullname?: string;
  discountPolicy?: string;
}

export interface RCateTypeInfo {
  name?: string;
}

export interface RHotel {
  owner: RApplicant;
  applicant: RApplicant;
  payType?: string | number;
  platType?: string;
  consumptionStandard?: string|number;
  budget?:RBudget;
  businessAim?: string; //业务目标
  applicantId?: string; // 业务申请人工号
  applyCause?: string; // 申请事由
  fullname?: string; // 酒店名称
  address?: string; // 地址
  hotelInfo:RHotelInfo;
  budgetAmount?: number;
  cateTypeInfo: RCateTypeInfo;

  workingLunchCount?: number;

  eatingTime?: string, //就餐时间
  eatingDay?: string;
  hotelId?: string | number;
  
  busLine?: string; // 公交车
  businessTime?: string; // 酒店地址

  contact?: string; // 联系人
  contactMobile?: string; // 联系人电话
  contactPhone?: string; // 酒店名称

  treatInfo: RTreatInfo;

  cateType: Array<RCate>; // 餐类

  cateClass?:Array<RCate>; // 餐类

  cateTypeId?: number;
  cateTypeText?: string;

  commentRating?:Array<RCate>; 

  consumptionPer?: number; // 人均 * 100
  consumptionSeat?: number; // 餐位费
  airportDistance?: number;
  serviceFee?: number; // 服务费

  dish?: Array<RDish>;


  decorateDate?: string; // 

  description?: string; // 简介
  discountPolicy?: string; // 

  email?: string; // 
  entertainment?: string; // 

  environment?: string; // 环境
  fax?: string; // 

  floors?: string; // 楼层
  gmtCreate?: string; // 

  gmtLastModified?: string; // 
  hallImage?: string; // 
  

  id?: string; // 
  isFrozen?: string; // 

  landMark?: Array<RMark>; // 标志
  provider?: string; // 

  railwayDistance?: number; // 
  rebate?: number; // 

  regionId?: number; // 
  restFacility?: string; // 提供免费早餐

  roomFacility?: string; // 大床、电视、24小时热水
  rooms?: number; //  房间数量

  service?: string; // 服务 "提供五星级住宿服务"
  simpleName?: string; // "青岛海尔国际旅行社有限公司海尔职业培训中心"


  stop?: string; // "地下停车场52个车位"
  starLevel?: number; // 星级

  state?: number; // 


}