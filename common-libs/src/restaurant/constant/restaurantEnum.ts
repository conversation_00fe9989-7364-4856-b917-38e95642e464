// 支付方式
export const RpayType = {
  "1": "挂账",
  "2": "自付",
  "3": "储值卡"
}



//支付状态
export const ROrderPayMentStateEnum = {
  UNPAID: 0,
  PREPAYMENT: 10,
  COMPLETE: 20,
  NOPAYMENT: 90,
  CANCEL: 99,

  "0": "未支付",
  "10": "预支付",
  "20": "支付完成",
  "90": "无需支付",
  "99": "取消支付",
}

export const ROrderPayMentStateEnumColor = {
  "0": "red",
  "10": "blue",
  "20": "green",
  "90": "green",
  "99": "red",
}

export const HOrderPayMentStateEnumColor = {
  "0": "#FFEDEE",
  "10": "#4EB7FF",
  "20": "#EDF9E8",
  "90": "#EDF9E8",
  "99": "#FFEDEE",
}


// 审批状态
export const ROrderApprovalStateEnum = {
  UNREVIEWED: -1,
  PROCESSING: 10,
  COMPLETED: 20,
  REJECT: 30,

  "-1": "未提交审批",
  "10": "审批中",
  "20": "审批通过",
  "30": "审批驳回",
}
export const ROrderApprovalStateEnumColor = {
  "-1": "",
  "10": "blue",
  "20": "green",
  "30": "red",
}


// 订单状态
export const ROrderStateEnum = {
  SAVED: 10,
  SUBMIT: 20,
  COMPLETE: 30,
  REFUND: 40,
  CANCLE: 0,

  "10": "已保存", // "已保存（可以支付）",
  "20": "已提交",
  "30": "预订完成",
  "40": "已退回", // "已退回（客服）",
  "0": "已取消", // "已取消（审批驳回）",
}

export const ROrderStateEnumColor = {
  "0": "",
  "10": "blue",
  "20": "blue",
  "30": "green",
  "40": "red",
}

export const HOrderStateEnumColor = {
  "0": "",
  "10": "#4EB7FF",
  "20": "#4EB7FF",
  "30": "#EDF9E8",
  "40": "#FFEDEE",
}

//订单来源, 提交订单的位置，header中添加 key:_BIZ_ORDER_SOURCE_
export const BizOrderSource = {
  "PC": 1,
  "WECHAT": 2,
  "APP": 3,
  "WEBAPP": 4,
  "offline": 5, //线下补单
  "iHaier": 6,
  "dingding": 7,

  "1": "PC",
  "2": "微信",
  "3": "APP",
  "4": "WEBAPP",
  "5": "线下补单",
  "6": "iHaier",
  "7": "钉钉",
}