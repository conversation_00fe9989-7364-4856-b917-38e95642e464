import { IPageRequest } from "../../basic";

export class IApplicationRequest extends IPageRequest {
    id?: number | null
    iconUrl?: string 
    jumpLinkApp?: string | null 
    jumpLinkPc?: string | null 
    showOrder?: number 
    showStatus?: number 
    menuName?: string
}

export class IAssignUserMenuType {
    userId?: string
    userName?: string
    menuIds?: number[]
    clint?: string
}

export class IApplicationAccount {
    id?: number | null 
    creator?: string 
    createTime?: string 
    updater?: string 
    updateTime?: string 
    iconUrl?: string 
    jumpLinkApp?: string | null  
    jumpLinkPc?: string | null 
    showOrder?: number 
    showStatus?: number 
    defaultShowStatus?: number 
    menuName?: string
    content?: string
    isTopping?: boolean
}

export class IApplicationResponse {
    id?: number | null
    creator?: string 
    createTime?: string 
    updater?: string 
    updateTime?: string 
    iconUrl?: string 
    jumpLinkApp?: string | null 
    jumpLinkPc?: string | null 
    showOrder?: number 
    showStatus?: number 
    defaultShowStatus?: number 
    menuName?: string
}
