export interface TCteateTeam {
  createName?: string;
  createBy?: string;
  id?: string;
  
  beginCityCode?: string;
  beginCityName?: string;

  beginDate?: string;
  endDate?: string;

  endCityCode?: string;
  endCityName?: string;

  destineNo?: string;
  gmtCreate?: string;

  // 出差类型0因公1因私
  evectionType?: number;

  // 附件地址
  travelerFileUrl?: string;
  travelerFileName?: string;
  dateRange?: Array<string>;

  // 产品类型
  destineInfo?:string;
  destineInfoArr?:  Array<number>;

  contactDeptName?: string;
  contactUserCode?: string;	//联系人工号
  contactUserName?: string;	//联系人名称
  contactUserPhone?: string;	//联系人电话
  contactUserMail?: string;	//联系人邮箱

  teamDestinePlaneTicket?:TTicket;  // 团队机票信息
  teamDestineHotel?:THotel;

  destineStatus?: string;

}

export interface TTicket {
  remarks?: string;

  //机票类型0国内1国际
  planeTicketType?: number;
   /*附件名称 */
   fileName?: string;

   /*附件预览地址 */
   filePath?: string;
  //航程类型0单程1往返
  voyageType?: number;
  voyageText?: string;
  travelerNum?: number;
  //出行时间段0上午1下午
  travelPriod?: number;
  travelPriodText?: string;
  //出差类型0因公1因私
  evectionType?: number;
  //其他需求
  otherInfo?: string;
  // 是否出票
  ticketFlag?: number;

  // 机票上传错误信息
  planeTicketDetailErrorList?: Array<TErrorMessage>;

  /*机票明细 */
  planeTicketDetailList?: Array<TTicketDetail>;
  /*附件明细 */

  fileMapList?: Array<TFileItem>;

}

export interface TTicketDetail {
  /*主键id */
  id?: Record<string, unknown>;

  /*预定申请单id */
  destineId?: Record<string, unknown>;

  /*团队机票表id */
  planeTicketId?: Record<string, unknown>;

  /*票号 */
  ticketNum?: string;

  /*起飞时间 */
  departureTime?: string;

  /*航班号 */
  flightNum?: string;

  /*订单号 */
  orderNum?: string;

  /*团队价格 */
  teamPrice?: number;

  /*非团队价格 */
  nonTeamPrice?: number;

  fileMapList?: Array<TFileItem>;
}

export interface THotel {
  remarks?: string;

   /*附件名称 */
   fileName?: string;

   /*附件预览地址 */
   filePath?: string;
  //大床房数
  kingRoomNum?: number;
  //双床房数
  doubleRoomNum?: number;

  travelerNum?: number;
  
  // 是否出票
  ticketFlag?: number;
  
  //其他需求
  otherInfo?: string;

  hotelDetailList?: Array<THotelDeatil>;

  // 酒店上传错误信息
  hotelDetailErrorList?: Array<TErrorMessage>;
  fileMapList?: Array<TFileItem>;

}

export interface TFileItem {
  fileName?: string;
  filePath?: string;

  name?: string;

  url?: string;

}
export interface THotelDeatil {
  /*主键id */
  id?: string;

  /*预定申请单id */
  destineId?: string;

  /*团队酒店表id */
  hotelInfoId?: string;

  /*房型 */
  roomType?: string;

  /*入住人 */
  occupant?: string;

  /*入住时间 */
  checkInTime?: string;

  /*离开时间 */
  checkOutTime?: string;

  /*居住天数 */
  liveDays?: Record<string, unknown>;

  /*酒店名称 */
  hotelName?: string;

  /*团队价格 */
  teamPrice?: number;

  /*非团队价格 */
  nonTeamPrice?: number;
}


// 获取团队票订单列表
export interface TGetListParams {
  // 平台类型0客户端1管理端
  platSource?: string; 
  // 联系人工号
  contactUserCode?: string; 
  // 是否出票0未出1已出
  ticketFlag?: string;
  // 申请人工号
  userName?: string;

  pageNum?: number;
  pageSize?: number;
  /*订单号 */
  destineNo?: string;

  /*订单状态10待提交20已提交30已接收40已取消90已完成 */
  destineStatus?: string;

  /*产品类型 */
  destineInfo?: string;

  /*出差类型0因公1因私 */
  evectionType?: string;

  /*订单负责人工号 */
  transactorCode?: string;

  /*出发地编码 */
  beginCityCode?: string;

  /*目的地编码 */
  endCityCode?: string;

  /*行程开始日期 */
  beginDate?: string;

  /*行程结束日期 */
  endDate?: string;

  /*创建开始日期 */
  createBeginDate?:string;

  /*创建结束日期 */
  createEndDate?:string;

  /* */
  needPage?: boolean;
}


export interface TErrorMessage {
  lineNumber?:string;
  errorMessage?: string;
}
