export interface MiceBidManNotice {
    /*主键id */
    id?: number;

    /*标题 */
    title?: string;

    /*内容形式 */
    contentForm?: number;

    /*公告内容 */
    informContent?: string;

    /*通知作用范围 */
    effectScope?: string;

    /*排序 */
    sort?: number;

    /*状态 */
    state?: string;

    /*是否弹窗 */
    isWindow?: boolean;

    /*创建人姓名 */
    createName?: string;

    /*创建时间 */
    gmtCreate?: string;
}
