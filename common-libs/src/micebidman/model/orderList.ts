import { Dayjs } from "dayjs";

// 搜索参数接口
export interface SearchParams {
  /** 订单状态数组 */
  states?: string[];
  statesName?: string[];
  /** 关键词搜索，支持单号/名称/经办人/地点 */
  keyword?: string;
  /** 需求项ID */
  demandItem?: number;
  /** 主编码 */
  mainCode?: string;
  /** 会议名称 */
  miceName?: string;
  /** 会议类型 */
  miceType?: number;
  /** 操作人工号 */
  operatorCode?: string;
  /** 操作人姓名 */
  operatorName?: string;
  /** 操作人电话 */
  operatorPhone?: string;
  /** 顾问工号 */
  consultantUserCode?: string;
  /** 顾问姓名 */
  consultantUserName?: string;
  /** 联系人工号 */
  contactUserCode?: string;
  /** 联系人姓名 */
  contactUserName?: string;
  /** 联系人电话 */
  contactUserPhone?: string;
  /** 地区类型 */
  districtType?: number;
  /** 当前页码 */
  pageNum?: number;
  /** 会议节点, 支持多节点查询 */
  processNodes?: string | string[];
  /** 每页条数 */
  pageSize?: number;
  /** 会议开始时间起 */
  startDateStart?: string;
  /** 会议开始时间止 */
  startDateEnd?: string;
  /** 会议结束时间起 */
  endDateStart?: string;
  /** 会议结束时间止 */
  endDateEnd?: string;
  /** 中标服务商名称 */
  winTheBidMerchantName?: string;
  /** 中标服务商编码 */
  winTheBidMerchantCode?: string;
  /** 中标服务商id */
  winTheBidMerchantId?: string;
  /** 需求发布服务商id */
  demandPushMerchantId?: string;
  /** 是否开通特殊权限 */
  isSpecialPowers?: string;
  /** 是否加急 */
  isUrgent?: string;
  /** 是否指定招标搜索 */
  isAppoint?: string;
  /** 需求省 */
  demandProvince?: string;
  /** 需求市 */
  demandCity?: string;
  /** 需求区 */
  demandDistrict?: string;
  /** 需求商圈 */
  demandCenterMarker?: string;
  /** 礼品是否发货 */
  isConsignment?: string;
  /** 礼品是否下单 */
  isPlaceOrder?: string;
  /** 执行方案是否变更过 */
  isChange?: string;
  /** 是否流标 */
  isBidFailure?: string;
}
export interface HotelSatisfyListRes {
  /* */
  data: {
    /*标识 */
    hotelFlag: string;

    /*酒店名称 */
    name: string;

    /*距离 */
    distance: string;
  }[];

  /* */
  code: string;

  /* */
  message: string;

  /* */
  success: boolean;
}
// 会议订单列表接口
export interface MiceBidManOrderList {
  id?:number
  /** 会议ID */
  miceId?: number;
  /** 主编码 */
  mainCode?: string;
  /** 会议名称 */
  miceName?: string;
  /** 城市ID列表，多个用逗号分隔 */
  cityIds?: string;
  /** 城市名称列表，多个用逗号分隔 */
  cityNames?: string;
  /** 开始日期 */
  startDate?: string;
  /** 结束日期 */
  endDate?: string;
  /** 是否加急 */
  isUrgent?: boolean;
  /** 操作人工号 */
  operatorCode?: string;
  /** 操作人姓名 */
  operatorName?: string;
  operatorPhone?: string;
  /** 顾问工号 */
  consultantUserCode?: string;
  /** 顾问姓名 */
  consultantUserName?: string;
  /** 需求项 */
  demandItem?: number;
  /** 状态 */
  state?: string;
  /** 状态修改时间 */
  stateGmtModified?: string;
  /** 方案截止时间 */
  schemeDeadline?: string;
  /** 投标截止时间 */
  biddingDeadline?: string;
}

export interface PlatformCount {
  /*总数量 */
  counts?: number;
  processNode?: string;
}

// 地区选项接口
export interface RegionOption {
  /** 地区值 */
  value: string;
  /** 地区标签 */
  label: string;
  /** 子地区列表 */
  children?: RegionOption[];
}

export interface MerchantContract {
  /*主键 */
  id?: number;

  /*供应商id */
  merchantId?: number;

  /*合同流水号 */
  contractNo?: string;

  /*合同号 */
  contractCode?: string;

  /*合同日期 */
  contractDate?: [Dayjs?, Dayjs?];

  /*合同开始日期 */
  contractStart?: Dayjs;

  /*合同结束日期 */
  contractEnd?: Dayjs;

  /*合同链接 */
  contractUrl?: string;

  /*状态 有效/失效 */
  state?: number | boolean;

  /*签订日期 */
  signDate?: Dayjs;

  /*来源 本地创建/haier法务同步 */
  resource?: string;

  /*创建人工号 */
  createBy?: string;

  /*创建人姓名 */
  createName?: string;

  /*创建时间 */
  gmtCreate?: string;

  /*最后修改人工号 */
  lastModifiedBy?: string;

  /*最后修改人姓名 */
  lastModifiedName?: string;

  /*最后修改时间 */
  gmtModified?: string;

  /* */
  merchantContractInfo?: {
    /*合同id */
    contractId?: number;

    /*甲方 */
    partner1?: string;

    /*乙方 */
    partner2?: string;

    /*丙方 */
    partner3?: string;

    /*丁方 */
    partner4?: string;
  };
}
export interface OrderListResponse {
  records?: MiceBidManOrderList[];
  data?: object;
  total?: number;
  size?: number;
  current?: number;
  code?: string;
  message?: string;
  success?: boolean;
}

export interface SelTabObj {
  selTab1?: number | string;
  selTab2?: number;
}

export interface CountSum {
  // 会议节点, 支持多节点查询
  processNodes?: string;
  // 会议状态, 支持多状态查询
  statesName?: string;
  states?: string;
  // 搜索键, 支持单号/名称/经办人/地点 聚合模糊搜索
  keyword?: string;
  // 包含的需求项目:bitmap
  demandItem?: string;
  // 订单号,模糊查询
  mainCode?: string;
  // 会议名称
  miceName?: string;
  // 会议类型 发布会等
  miceType?: string;
  // 会议经办人工号
  operatorCode?: string;
  // 会议经办人姓名
  operatorName?: string;
  // 会议经办人手机号
  operatorPhone?: string;
  // 会务顾问工号
  consultantUserCode?: string;
  // 会务顾问姓名
  consultantUserName?: string;
  // 会议对接人工号
  contactUserCode?: string;
  // 会议对接人姓名
  contactUserName?: string;
  // 会议对接人手机号
  contactUserPhone?: string;
  // 会议地点 国内/国际
  districtType?: string;
  // 会议开始时间起
  startDateStart?: string;
  // 会议开始时间止
  startDateEnd?: string;
  // 会议结束时间起
  endDateStart?: string;
  // 会议结束时间止
  endDateEnd?: string;
  // 中标服务商名称
  winTheBidMerchantName?: string;
  // 中标服务商编码
  winTheBidMerchantCode?: string;
  // 是否开通特殊权限
  isSpecialPowers?: string;
  // 是否加急
  isUrgent?: string;
  // 是否指定招标搜索
  isAppoint?: string;
  // 需求省
  demandProvince?: string;
  // 需求市
  demandCity?: string;
  // 需求区
  demandDistrict?: string;
  // 需求商圈
  demandCenterMarker?: string;
  // 礼品是否发货
  isConsignment?: string;
  // 礼品是否下单
  isPlaceOrder?: string;
  // 执行方案是否变更过
  isChange?: string;
  // 是否流标
  isBidFailure?: string;
  // )
  pageNum?: number;
  // )
  pageSize?: number;
}

export interface TabList {
  counts?: number;
  processNode?: string;
}

export interface ListRes {
  /* */
  data: {
    /*节点定义key */
    key: string;

    /*节点名称 */
    name: string;

    /*节点配置 */
    configs: {
      /*配置描述 */
      desc: string;

      /*配置参数是否必填 */
      required: boolean;

      /*配置项名称 */
      name: string;
    };

    /*下一节点 */
    nextNodes: {
      /*可用值:DEMAND_SUBMIT,DEMAND_RECEIVE,DEMAND_PRE_INTERACT,DEMAND_CONFIRM,DEMAND_APPROVAL,DEMAND_PUSH,DEMAND_RE_APPROVAL,SCHEME_SUBMIT,SCHEME_APPROVAL,SCHEME_RE_APPROVAL,SCHEME_CONFIRM,BID_PUSH,BIDDING,BID_RESULT_CONFIRM,COST_APPROVAL,MICE_EXECUTION,MICE_COMPLETED,BILL_CONFIRM,BILL_APPROVAL,BILL_RE_APPROVAL,PAYMENT_CONFIRM,PLATFORM_INVOICE_ENTRY,VENDOR_INVOICE_ENTRY,INVOICE_CONFIRM,PLATFORM_RECEIPT_UPLOAD,PLATFORM_INVOICE_CONFIRM,SETTLEMENT_PENDING,SETTLEMENT_RECORDED,END */
      nextNode: string;
    };
  }[];

  /* */
  code: string;

  /* */
  message: string;

  /* */
  success: boolean;
}

export interface MerchantListRes {
  /* */
  data: {
    /*主键 */
    id: number;

    /*服务商名称 */
    merchantName: string;

    /*服务商code */
    merchantCode: string;

    /*服务商类型 */
    merchantType: number;

    /*评分 */
    score: number;

    /*酒店名称, 如果有鼠标放上后上浮显示 */
    hotelName: string;
  }[];

  /* */
  code: string;

  /* */
  message: string;

  /* */
  success: boolean;
}

export interface QueryPoolIdsRes {
  /* */
  data: Record<string, unknown>[];

  /* */
  code: string;

  /* */
  message: string;

  /* */
  success: boolean;
}


export interface Details_3Res {
  /* */
  data: {
    /*流程定义id */
    id: number;

    /*流程定义id */
    verId: number;

    /*产品线id */
    pdProductLineId: number;

    /*流程名称 */
    name: string;

    /*描述 */
    description: string;

    /*订单前缀 */
    prefix: string;

    /*更新内容 */
    changeContent: string;

    /*来源版本id */
    sourceVerId: number;

    /*节点 */
    nodes: {
      /* */
      id: number;

      /*对应系统节点key */
      metaKey: string;

      /*节点名称，会显示在系统的流程图中，默认可取后端枚举name，也可用户定义 */
      nodeName: string;

      /*节点顺序 */
      seq: number;

      /*节点状态,可用值:DEMAND_SUBMIT_NORMAL,DEMAND_SUBMIT_REJECTED,DEMAND_SUBMIT_APPROVAL_REJECTED,DEMAND_RECEIVE_NORMAL,DEMAND_PRE_INTERACT_NORMAL,DEMAND_CONFIRM_NORMAL_REJECTED,DEMAND_CONFIRM_NORMAL,DEMAND_APPROVAL_NORMAL,DEMAND_PUSH,DEMAND_TO_BE_RELEASE_NORMAL,DEMAND_RELEASE_CANCEL,DEMAND_RE_APPROVAL_NORMAL,DEMAND_RE_APPROVAL_NORMAL_REJECTED,SCHEME_SUBMIT_NORMAL,SCHEME_APPROVAL_NORMAL,SCHEME_RE_APPROVAL_NORMAL,SCHEME_CONFIRM_NORMAL,BID_PUSH_NORMAL,BIDDING_NORMAL,BID_RESULT_CONFIRM_NORMAL,COST_APPROVAL_NORMAL,MICE_EXECUTION_NORMAL,MICE_COMPLETED_NORMAL,BILL_CONFIRM_NORMAL,BILL_APPROVAL_NORMAL,BILL_RE_APPROVAL_NORMAL,PAYMENT_CONFIRM_NORMAL,PLATFORM_INVOICE_ENTRY_NORMAL,VENDOR_INVOICE_ENTRY_NORMAL,INVOICE_CONFIRM_NORMAL,PLATFORM_RECEIPT_UPLOAD_NORMAL,PLATFORM_INVOICE_CONFIRM_NORMAL,SETTLEMENT_PENDING_NORMAL,SETTLEMENT_RECORDED_NORMAL,SETTLEMENT_COMPLETED_NORMAL,REPEAL */
      states: Record<string, unknown>[];

      /*节点配置 */
      configs: {
        /*对应系统配置key */
        metaKey: string;

        /*配置参数 */
        configParam: string;
      }[];

      /*节点触发器 */
      triggers: {
        /*触发器类型 计时/进入/结束 */
        triggerType: string;

        /*触发器执行内容key */
        triggerMetaKey: string;

        /*触发器执行name */
        triggerMetaName: string;

        /*触发等待时间 分钟 */
        triggerWaitingTime: number;

        /*触发器方法配置json */
        triggerMethodParamJson: string;
      }[];
    }[];

    /*流程用户 */
    consumers: {
      /*匹配规则代码key */
      ruleMetaKey: string;

      /*匹配规则名称 */
      ruleMetaName: string;

      /*匹配规则参数 */
      ruleMetaParamJson: string;
    }[];

    /*流程供应商组 */
    merchantGroups: {
      /* */
      id: number;

      /*关联资源池临时id, 一对多 */
      poolIds: Record<string, unknown>[];

      /*组名 */
      name: string;

      /*平台服务费率 */
      platformFeeRate: number;

      /*平台服务费率收取范围；bitmap */
      platformFeeRange: number;
    }[];

    /*流程供应商池 */
    merchantPools: {
      /* */
      id: number;

      /*资源池名称 */
      name: string;

      /*匹配规则 */
      matches: {
        /*匹配规则代码key */
        ruleMetaKey: string;

        /*匹配规则名称 */
        ruleMetaName: string;

        /*匹配规则json参数 */
        ruleMetaParamJson: string;
      }[];

      /*可承接项目范围 */
      itemRanges: {
        /*承接项目 */
        itemType: number;

        /*方案匹配规则 */
        schemeMatchRule: string;

        /*全单服务费费率上限 */
        fullServiceRangeRateLimit: number;

        /*全单服务费率收取范围；bitmap */
        fullServiceRange: number;
      }[];
    }[];
  };

  /* */
  code: string;

  /* */
  message: string;

  /* */
  success: boolean;
}

export interface DetailsRes {
  /* */
  data: {
    /*需求主表id */
    id: number;

    /*订单号(不做关联) */
    mainCode: string;

    /*主表id */
    miceId: number;

    /*会议名称 */
    miceName: string;

    /*会议类型 发布会等enum:[[{"code":1,"desc":"开盘会"},{"code":2,"desc":"客户会"},{"code":3,"desc":"培训会"},{"code":4,"desc":"发布会"},{"code":5,"desc":"展览会"},{"code":6,"desc":"内部沟通会"},{"code":7,"desc":"招商会"}]],可用值:1,2,3,4,5,6,7 */
    miceType: number;

    /*会议举行城市,逗号分隔 */
    cityIds: string;

    /*会议举行城市名称,逗号分隔 */
    cityNames: string;

    /*需求开始时间 */
    startDate: Record<string, unknown>;

    /*需求结束时间 */
    endDate: Record<string, unknown>;

    /*是否加急需求 */
    isUrgent: boolean;

    /*会议对接人工号 */
    contactUserCode: string;

    /*会议对接人姓名 */
    contactUserName: string;

    /*会议对接人手机号 */
    contactUserPhone: string;

    /*会议对接人邮箱 */
    contactUserEmail: string;

    /*总人数 */
    personTotal: number;

    /*会议地点 国内/国际enum:[[{"code":0,"desc":"国内"},{"code":1,"desc":"国际"}]],可用值:0,1 */
    districtType: number;

    /*包含的需求项目:bitmap */
    demandItem: number;

    /*自动预测总金额 */
    calcTotalPrice: number;

    /*需求总金额 */
    demandTotalPrice: number;

    /*需求备注 */
    remarks: string;

    /*需求提报类型 用户提报/顾问代提/ */
    demandType: number;

    /*需求状态 当前生效/失效版本.当前生效同类型下只存在一个 */
    demandState: number;

    /*需求驳回原因 */
    demandRejectReason: string;

    /*上一版本的id */
    sourceId: number;

    /*状态最后修改时间 */
    stateGmtModified: Record<string, unknown>;

    /*流程定义主表id */
    pdMainId: number;

    /*流程定义版本id */
    pdVerId: number;

    /*流程定义版本名 */
    pdVerName: string;

    /*主订单id,当多个会议直接存在从属关系时添加 */
    mainMiceId: number;

    /*会中服务订单id */
    miceServiceOrderId: number;

    /*会中服务订单号 */
    miceServiceOrderCode: string;

    /*会议经办人工号 */
    operatorCode: string;

    /*会议经办人姓名 */
    operatorName: string;

    /*会议经办人手机号 */
    operatorPhone: string;

    /*会议经办人邮箱 */
    operatorEmail: string;

    /*意向会务顾问工号 */
    intentionConsultantUserCode: string;

    /*会务顾问工号 */
    consultantUserCode: string;

    /*会务顾问姓名 */
    consultantUserName: string;

    /*会议状态 */
    state: string;

    /*支付状态 待支付/部分支付/支付完成 */
    paymentState: number;

    /*酒店 */
    hotels: {
      /*需求酒店id */
      id: number;

      /*酒店所在省级id */
      provinceId: string;

      /*酒店所在省级名称 */
      provinceName: string;

      /*酒店所在城市id */
      cityId: string;

      /*酒店所在城市名称 */
      cityName: string;

      /*酒店所在区域id,支持多区域,逗号分割 */
      districtIds: string;

      /*酒店所在区域名称,支持多区域,逗号分割 */
      districtNames: string;

      /*酒店等级:bitmap */
      level: number;

      /*需求中心的地标名称 */
      centerMarker: string;
    }[];

    /*住宿 */
    stays: {
      /*主键 */
      id: number;

      /*需求酒店id */
      miceDemandHotelId: number;

      /*需求日期 */
      demandDate: Record<string, unknown>;

      /*房型 大床/双床/套房 */
      roomType: number;

      /*早餐类型 无早/单早/双早 */
      breakfastType: number;

      /*人数 */
      personNum: number;

      /*入住房间数 */
      roomNum: number;

      /*人数与房间数不一致原因 */
      discrepancyReason: string;

      /*自动测算单价 */
      calcUnitPrice: number;
    }[];

    /*会场 */
    places: {
      /*主键 */
      id: number;

      /*需求酒店id */
      miceDemandHotelId: number;

      /*需求日期 */
      demandDate: Record<string, unknown>;

      /*使用时间 上午/下午/晚间 bitmap */
      usageTime: number;

      /*使用用途 会议举行/布展搭建/会议撤场 bitmap */
      usagePurpose: number;

      /*人数 */
      personNum: number;

      /*面积 */
      area: number;

      /*灯下层高 */
      underLightFloor: number;

      /*摆台形式 */
      tableType: number;

      /*是否需要led */
      hasLed: boolean;

      /*led数量 */
      ledNum: number;

      /*led规格说明 */
      ledSpecs: string;

      /*是否需要茶歇 */
      hasTea: boolean;

      /*茶歇标准/每人 */
      teaEachTotalPrice: number;

      /*茶歇说明 */
      teaDesc: string;

      /*自动测算会场单价 */
      calcUnitPlacePrice: number;

      /*自动测算led单价 */
      calcUnitLedPrice: number;

      /*自动测算茶歇单价 */
      calcUnitTeaPrice: number;
    }[];

    /*用餐 */
    caterings: {
      /*需求酒店id */
      miceDemandHotelId: number;

      /*主键 */
      id: number;

      /*是否酒店提供用餐 */
      isInsideHotel: boolean;

      /*需求日期 */
      demandDate: Record<string, unknown>;

      /*用餐类型 */
      cateringType: number;

      /*用餐时间 午餐/晚餐 */
      cateringTime: number;

      /*人数 */
      personNum: number;

      /*用餐标准 */
      demandUnitPrice: number;

      /*是否包含酒水 */
      isIncludeDrinks: boolean;

      /*自动测算单价 */
      calcUnitPrice: number;
    }[];

    /*用车 */
    vehicles: {
      /*主键 */
      id: number;

      /*需求日期 */
      demandDate: Record<string, unknown>;

      /*使用方式 单趟/包车 */
      usageType: number;

      /*使用时长 半天/全天 */
      usageTime: number;

      /*座位数 */
      seats: number;

      /*车辆数量 */
      vehicleNum: number;

      /*品牌 */
      brand: string;

      /*路线,多程逗号分隔 */
      route: string;

      /*自动测算单价 */
      calcUnitPrice: number;
    }[];

    /*服务人员 */
    attendants: {
      /*主键 */
      id: number;

      /*需求日期 */
      demandDate: Record<string, unknown>;

      /*人员类型 */
      type: number;

      /*人数 */
      personNum: number;

      /*工作范围 */
      duty: string;

      /*自动测算单价 */
      calcUnitPrice: number;
    }[];

    /*拓展活动 */
    activities: {
      /*主键 */
      id: number;

      /*需求日期 */
      demandDate: Record<string, unknown>;

      /*费用标准 */
      demandUnitPrice: number;

      /*人数 */
      personNum: number;

      /*活动说明 */
      description: string;

      /*自动测算单价 */
      calcUnitPrice: number;

      /*活动附件路径 */
      paths: Record<string, unknown>[];
    }[];

    /*保险 */
    insurances: {
      /*主键 */
      id: number;

      /*需求日期 */
      demandDate: Record<string, unknown>;

      /*单价 */
      demandUnitPrice: number;

      /*参保人数 */
      personNum: number;

      /*保险产品id(以互动时为准,需求时只为意向) */
      productId: number;

      /*产品所属商户id */
      productMerchantId: number;

      /*险种名称 */
      insuranceName: string;

      /*险种条目 */
      insuranceContent: string;

      /*自动测算单价 */
      calcUnitPrice: number;
    }[];

    /*布展物料 */
    material: {
      /*主键 */
      id: number;

      /*费用标准/总 */
      demandTotalPrice: number;

      /*自动测算总价 */
      calcTotalPrice: number;

      /*需求布展物料明细 */
      materialDetails: {
        /*主键 */
        id: number;

        /*需求布展物料表id */
        miceDemandMaterialId: number;

        /*物料类型 枚举 */
        type: number;

        /*规格说明 */
        specs: string;

        /*数量 */
        num: number;

        /*单价 */
        unitPrice: number;
      }[];
    }[];

    /*交通 */
    traffic: {
      /*主键 */
      id: number;

      /*需求总金额 */
      demandTotalPrice: number;

      /*自动测算总价 */
      calcTotalPrice: number;
    }[];

    /*礼品 */
    presents: {
      /*主键 */
      id: number;

      /*送达日期 */
      deliveryDate: Record<string, unknown>;

      /*费用标准 */
      demandTotalPrice: number;

      /*礼品数量 */
      personNum: number;

      /*礼品说明 */
      personSpecs: string;

      /*礼品产品id(以互动时为准,需求时只为意向) */
      productId: number;

      /*产品所属商户id */
      productMerchantId: number;

      /*产品名称,当未选择产品时可自由修改 */
      productName: string;

      /*自动测算单价 */
      calcUnitPrice: number;
    }[];

    /*其他 */
    others: {
      /*主键 */
      id: number;

      /*需求日期 */
      demandDate: Record<string, unknown>;

      /*数量 */
      num: number;

      /*单位 */
      unit: string;

      /*规格描述 */
      specs: string;

      /*费用标准 */
      demandTotalPrice: number;

      /*自动测算总价 */
      calcTotalPrice: number;
    }[];
  };

  /* */
  code: string;

  /* */
  message: string;

  /* */
  success: boolean;
}

export interface CardItem {
  title?: string;
  container?: string;
  person?: string;
  status?: string;
  line?: null | any;
  id?: number;
  hide?: Boolean;
}

export interface Details_1Res {
  /* */
  data: {
    /*需求主表id */
    id: number;

    /*订单号(不做关联) */
    mainCode: string;

    /*主表id */
    miceId: number;

    /*会议名称 */
    miceName: string;

    /*会议类型 发布会等enum:[[{"code":1,"desc":"开盘会"},{"code":2,"desc":"客户会"},{"code":3,"desc":"培训会"},{"code":4,"desc":"发布会"},{"code":5,"desc":"展览会"},{"code":6,"desc":"内部沟通会"},{"code":7,"desc":"招商会"}]],可用值:1,2,3,4,5,6,7 */
    miceType: number;

    /*会议举行城市,逗号分隔 */
    cityIds: string;

    /*会议举行城市名称,逗号分隔 */
    cityNames: string;

    /*需求开始时间 */
    startDate: Record<string, unknown>;

    /*需求结束时间 */
    endDate: Record<string, unknown>;

    /*是否加急需求 */
    isUrgent: boolean;

    /*会议对接人工号 */
    contactUserCode: string;

    /*会议对接人姓名 */
    contactUserName: string;

    /*会议对接人手机号 */
    contactUserPhone: string;

    /*会议对接人邮箱 */
    contactUserEmail: string;

    /*总人数 */
    personTotal: number;

    /*会议地点 国内/国际enum:[[{"code":0,"desc":"国内"},{"code":1,"desc":"国际"}]],可用值:0,1 */
    districtType: number;

    /*包含的需求项目:bitmap */
    demandItem: number;

    /*自动预测总金额 */
    calcTotalPrice: number;

    /*需求总金额 */
    demandTotalPrice: number;

    /*需求备注 */
    remarks: string;

    /*需求提报类型 用户提报/顾问代提/ */
    demandType: number;

    /*需求状态 当前生效/失效版本.当前生效同类型下只存在一个 */
    demandState: number;

    /*需求驳回原因 */
    demandRejectReason: string;

    /*上一版本的id */
    sourceId: number;

    /*状态最后修改时间 */
    stateGmtModified: Record<string, unknown>;

    /*流程定义主表id */
    pdMainId: number;

    /*流程定义版本id */
    pdVerId: number;

    /*流程定义版本名 */
    pdVerName: string;

    /*主订单id,当多个会议直接存在从属关系时添加 */
    mainMiceId: number;

    /*会中服务订单id */
    miceServiceOrderId: number;

    /*会中服务订单号 */
    miceServiceOrderCode: string;

    /*会议经办人工号 */
    operatorCode: string;

    /*会议经办人姓名 */
    operatorName: string;

    /*会议经办人手机号 */
    operatorPhone: string;

    /*会议经办人邮箱 */
    operatorEmail: string;

    /*意向会务顾问工号 */
    intentionConsultantUserCode: string;

    /*会务顾问工号 */
    consultantUserCode: string;

    /*会务顾问姓名 */
    consultantUserName: string;

    /*会议状态 */
    state: string;

    /*支付状态 待支付/部分支付/支付完成 */
    paymentState: number;

    /*酒店 */
    hotels: {
      /*需求酒店id */
      id: number;

      /*酒店所在省级id */
      provinceId: string;

      /*酒店所在省级名称 */
      provinceName: string;

      /*酒店所在城市id */
      cityId: string;

      /*酒店所在城市名称 */
      cityName: string;

      /*酒店所在区域id,支持多区域,逗号分割 */
      districtIds: string;

      /*酒店所在区域名称,支持多区域,逗号分割 */
      districtNames: string;

      /*酒店等级:bitmap */
      level: number;

      /*需求中心的地标名称 */
      centerMarker: string;
    }[];

    /*住宿 */
    stays: {
      /*主键 */
      id: number;

      /*需求酒店id */
      miceDemandHotelId: number;

      /*需求日期 */
      demandDate: Record<string, unknown>;

      /*房型 大床/双床/套房 */
      roomType: number;

      /*早餐类型 无早/单早/双早 */
      breakfastType: number;

      /*人数 */
      personNum: number;

      /*入住房间数 */
      roomNum: number;

      /*人数与房间数不一致原因 */
      discrepancyReason: string;

      /*自动测算单价 */
      calcUnitPrice: number;
    }[];

    /*会场 */
    places: {
      /*主键 */
      id: number;

      /*需求酒店id */
      miceDemandHotelId: number;

      /*需求日期 */
      demandDate: Record<string, unknown>;

      /*使用时间 上午/下午/晚间 bitmap */
      usageTime: number;

      /*使用用途 会议举行/布展搭建/会议撤场 bitmap */
      usagePurpose: number;

      /*人数 */
      personNum: number;

      /*面积 */
      area: number;

      /*灯下层高 */
      underLightFloor: number;

      /*摆台形式 */
      tableType: number;

      /*是否需要led */
      hasLed: boolean;

      /*led数量 */
      ledNum: number;

      /*led规格说明 */
      ledSpecs: string;

      /*是否需要茶歇 */
      hasTea: boolean;

      /*茶歇标准/每人 */
      teaEachTotalPrice: number;

      /*茶歇说明 */
      teaDesc: string;

      /*自动测算会场单价 */
      calcUnitPlacePrice: number;

      /*自动测算led单价 */
      calcUnitLedPrice: number;

      /*自动测算茶歇单价 */
      calcUnitTeaPrice: number;
    }[];

    /*用餐 */
    caterings: {
      /*需求酒店id */
      miceDemandHotelId: number;

      /*主键 */
      id: number;

      /*是否酒店提供用餐 */
      isInsideHotel: boolean;

      /*需求日期 */
      demandDate: Record<string, unknown>;

      /*用餐类型 */
      cateringType: number;

      /*用餐时间 午餐/晚餐 */
      cateringTime: number;

      /*人数 */
      personNum: number;

      /*用餐标准 */
      demandUnitPrice: number;

      /*是否包含酒水 */
      isIncludeDrinks: boolean;

      /*自动测算单价 */
      calcUnitPrice: number;
    }[];

    /*用车 */
    vehicles: {
      /*主键 */
      id: number;

      /*需求日期 */
      demandDate: Record<string, unknown>;

      /*使用方式 单趟/包车 */
      usageType: number;

      /*使用时长 半天/全天 */
      usageTime: number;

      /*座位数 */
      seats: number;

      /*车辆数量 */
      vehicleNum: number;

      /*品牌 */
      brand: string;

      /*路线,多程逗号分隔 */
      route: string;

      /*自动测算单价 */
      calcUnitPrice: number;
    }[];

    /*服务人员 */
    attendants: {
      /*主键 */
      id: number;

      /*需求日期 */
      demandDate: Record<string, unknown>;

      /*人员类型 */
      type: number;

      /*人数 */
      personNum: number;

      /*工作范围 */
      duty: string;

      /*自动测算单价 */
      calcUnitPrice: number;
    }[];

    /*拓展活动 */
    activities: {
      /*主键 */
      id: number;

      /*需求日期 */
      demandDate: Record<string, unknown>;

      /*费用标准 */
      demandUnitPrice: number;

      /*人数 */
      personNum: number;

      /*活动说明 */
      description: string;

      /*自动测算单价 */
      calcUnitPrice: number;

      /*活动附件路径 */
      paths: Record<string, unknown>[];
    }[];

    /*保险 */
    insurances: {
      /*主键 */
      id: number;

      /*需求日期 */
      demandDate: Record<string, unknown>;

      /*单价 */
      demandUnitPrice: number;

      /*参保人数 */
      personNum: number;

      /*保险产品id(以互动时为准,需求时只为意向) */
      productId: number;

      /*产品所属商户id */
      productMerchantId: number;

      /*险种名称 */
      insuranceName: string;

      /*险种条目 */
      insuranceContent: string;

      /*自动测算单价 */
      calcUnitPrice: number;
    }[];

    /* */
    material: {
      /*主键 */
      id: number;

      /*费用标准/总 */
      demandTotalPrice: number;

      /*自动测算总价 */
      calcTotalPrice: number;

      /*需求布展物料明细 */
      materialDetails: {
        /*主键 */
        id: number;

        /*需求布展物料表id */
        miceDemandMaterialId: number;

        /*物料类型 枚举 */
        type: number;

        /*规格说明 */
        specs: string;

        /*数量 */
        num: number;

        /*单价 */
        unitPrice: number;
      }[];
    };

    /* */
    traffic: {
      /*主键 */
      id: number;

      /*需求总金额 */
      demandTotalPrice: number;

      /*自动测算总价 */
      calcTotalPrice: number;
    };

    /*礼品 */
    presents: {
      /*主键 */
      id: number;

      /*送达日期 */
      deliveryDate: Record<string, unknown>;

      /*费用标准 */
      demandTotalPrice: number;

      /*礼品数量 */
      personNum: number;

      /*礼品说明 */
      personSpecs: string;

      /*礼品产品id(以互动时为准,需求时只为意向) */
      productId: number;

      /*产品所属商户id */
      productMerchantId: number;

      /*产品名称,当未选择产品时可自由修改 */
      productName: string;

      /*自动测算单价 */
      calcUnitPrice: number;
    }[];

    /*其他 */
    others: {
      /*主键 */
      id: number;

      /*需求日期 */
      demandDate: Record<string, unknown>;

      /*数量 */
      num: number;

      /*单位 */
      unit: string;

      /*规格描述 */
      specs: string;

      /*费用标准 */
      demandTotalPrice: number;

      /*自动测算总价 */
      calcTotalPrice: number;
    }[];
  };

  /* */
  code: string;

  /* */
  message: string;

  /* */
  success: boolean;
}

export interface SelCollapseS {
  selWatch?: string;
  selCollapse0?: string;
  selCollapse1?: string;
  selCollapse2?: string;
  selCollapse3?: string;
  selCollapse4?: string;
  selCollapse5?: string;
  selCollapse6?: string;
}

export interface MiceDetail {
  miceName?: string;
  mainCode?: string;
}

export interface ReceiveRejectParams {
  /*主表id */
  miceId: number;

  /*需求驳回原因 */
  demandRejectReason: string;
}

export interface ReceiveParams {
  /*主表id */
  miceId: number;

  /*会务顾问工号 */
  consultantUserCode: string;

  /*会务顾问姓名 */
  consultantUserName: string;
}

export interface Count_3Res {
  /* */
  data: {
    /*顾问名称 */
    nickName: string;

    /*顾问介绍 */
    description: string;

    /*汇总 */
    results: {
      /*各节点对应节点名称 */
      processNode: string;

      /*各节点对应数量 */
      counts: number;

      /*需求总金额 */
      demandTotalPrice: number;
    }[];
  }[];

  /* */
  code: string;

  /* */
  message: string;

  /* */
  success: boolean;
}

export interface TestData {
  name?: string;
  desc?: string;
  username?: string;
  link?: string;
  isRecommend?: boolean;
  avatar?: string;
  performance?: { label: string; value: string }[];
}

// 会议完成订单
export interface OneEndedMeeting {
  /*主键 */
  id: number;

  /*订单号(不做关联) */
  mainCode: string;

  /*会议名称 */
  miceName: string;

  /*会议类型 */
  miceType: number;

  /*主订单id,当多个会议直接存在从属关系时添加 */
  mainMiceId: number;

  /*会议经办人工号 */
  operatorCode: string;

  /*会议经办人姓名 */
  operatorName: string;

  /*会议经办人手机号 */
  operatorPhone: string;

  /*会议经办人邮箱 */
  operatorEmail: string;

  /*意向会务顾问工号 */
  intentionConsultantUserCode: string;

  /*会务顾问工号 */
  consultantUserCode: string;

  /*会务顾问姓名 */
  consultantUserName: string;

  /*会务顾问手机号 */
  consultantUserPhone: string;

  /*流程所处节点 */
  processNode: string;

  /*会议状态 */
  state: number;
}
