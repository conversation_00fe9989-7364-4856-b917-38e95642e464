import { IPageRequest } from "../../basic";

export class IAssessmentItemsFilter extends IPageRequest {
    gmtCreateStart?: string
    gmtCreateEnd?: string
    name?: string
    relatedNo?: string
    state?: number
    type?: number
    score?: number
    money?: number
    createName?: string
    assessmentType?: string
    violationBegin?: string
    violationEnd?: string
    detail?: string
}


export class IAssessmentItems {
    id?: number | null
    creator?: string
    createTime?: string
    updater?: string
    updateTime?: string
    name?: string // 考核条目名称
    rule?: string // 考核明细
    detail?: string // 考核明细(弹框使用)
    score?: number // 考核分数
    amount?: number // 违规金额
    money?: number // 违规金额(弹框使用)
    status?: string // 状态 1启用 0禁用
    type?: string | number // 服务商类型
    gmtCreate?: string // 创建时间
    isAdd?: boolean // 是否追加
    file?: string // 文件路径
    state?: number
}

export interface IAssessmentItemState {
    id: number;
    state: number;
}