import { Dayjs } from "dayjs";
import { IPageRequest } from "../../basic";

export class IAnnualBudgetFilter extends IPageRequest {
    //会议月份
    miceTime?:string
    //会议天数
    day?:number
    //会议名称
    name?:string
    //预算金额
    budget?:number
    //参加人数
    personTotal?:number
    //会议地点
    place?:string
    //酒店星级
    hotelLevel?:number[] | number
    //是否布展
    isCloth?:boolean
    //需求项目
    items?:[]
    //会议描述
    description?:string
    //提报人
    createName?:string
    //提报人工号
    createBy?:string
    //提报时间
    gmtCreate?:string
    begin?:string
    end?:string
}


export class IAnnualBudget {
    id?:number
    //会议月份
    miceTime?:string
    //会议天数
    day?:number
    //会议名称
    name?:string
    //预算金额
    budget?:number
    //参加人数
    personTotal?:number
    //会议地点
    place?:string
    //酒店星级
    hotelLevel?:string | string[]
    //是否布展
    isCloth?:boolean
    //需求项目
    item?:number |string
    //会议描述
    description?:string
    //提报人
    createName?:string
    //提报人工号
    createBy?:string
    //提报时间
    gmtCreate?:string
    begin?:string
    end?:string
}