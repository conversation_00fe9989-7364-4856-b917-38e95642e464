import { IPageRequest } from "../../basic";

export class IMeetingHandoversFilter extends IPageRequest {
    begin?:string
    end?:string
    // 经办人
    connectBeforeName?:string
    //经办人电话
    connectBeforePhone?:string
    //经办人邮箱
    connectBeforeEmail?:string
    // 经办人工号
    connectBeforeCode?:string
    //承接人
    handoverAfterName?:string
    //承接人电话
    handoverAfterPhone?:string
    // 承接人邮箱
    handoverAfterEmail?:string
    //承接人工号
    handoverAfterCode?:string
    //承接人直线
    handoverLineName?:string
    //承接原因
    handoverReason?:string
}


export class IMeetingHandovers {
    id?: number | null
    // 经办人
    connectBeforeName?:string
    //经办人电话
    connectBeforePhone?:string
    //经办人邮箱
    connectBeforeEmail?:string
    // 经办人工号
    connectBeforeCode?:string
    //承接人
    handoverAfterName?:string
    //承接人电话
    handoverAfterPhone?:string
    // 承接人邮箱
    handoverAfterEmail?:string
    //承接人工号
    handoverAfterCode?:string
    //承接人直线
    handoverLineName?:string
    //承接原因
    handoverReason?:string
    //标题
    title?:string
    //状态
    handoverState?:number
    //创建时间
    createTime?:string
    creator?:string
    updater?: string
    updateTime?: string
}