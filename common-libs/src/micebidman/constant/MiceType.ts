
type keys = 'OPENING_SESSION' | 'CLIENT_MEETING' | 'TRAINING_SESSION' |
  'PRODUCT_LAUNCH' | 'EXHIBITION' | 'INTERNAL_COMMUNICATION' |
  'INVESTMENT_FORUM'

export const MiceTypeConstantO = {
  // 会议类型定义
  OPENING_SESSION: { code: 1, desc: '开盘会' },
  CLIENT_MEETING: { code: 2, desc: '客户会' },
  TRAINING_SESSION: { code: 3, desc: '培训会' },
  PRODUCT_LAUNCH: { code: 4, desc: '发布会' },
  EXHIBITION: { code: 5, desc: '展览会' },
  INTERNAL_COMMUNICATION: { code: 6, desc: '内部沟通会' },
  INVESTMENT_FORUM: { code: 7, desc: '招商会' },

  // 工具方法
  ofType: (type?: number): { code: number, desc: string } | null => {
    for (const key in MiceTypeConstantO) {
      const item = MiceTypeConstantO[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number, desc: string } | undefined)[] => {
    const types = Object.keys(MiceTypeConstantO).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return MiceTypeConstantO[i as keys]
      }
      return
    })
    const newTypes = types.filter(function (s) {
      return s && s;
    })
    return newTypes
  },
};