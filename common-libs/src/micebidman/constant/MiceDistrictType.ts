type keys = 'DOMESTIC' | 'INTERNATIONAL'

export const MiceDistrictTypeConstant = {
  // 会议地点类型定义
  DOMESTIC: { code: 0, desc: '国内' },
  INTERNATIONAL: { code: 1, desc: '国际' },

  // 工具方法
  ofType: (type?: number): { code: number, desc: string } | null => {
    for (const key in MiceDistrictTypeConstant) {
      const item = MiceDistrictTypeConstant[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number, desc: string } | undefined)[] => {
    const types = Object.keys(MiceDistrictTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return MiceDistrictTypeConstant[i as keys]
      }
      return
    })
    const newTypes = types.filter(function (s) {
      return s && s;
    })
    return newTypes
  },
};
