type keys = 'DOMESTIC' | 'ABORAD'

export const SchemeLedSource = {
  // 会议地点类型定义
  DOMESTIC: { code: 1, desc: '酒店自有' },
  ABORAD: { code: 2, desc: '广告公司外搭' },

  // 工具方法
  ofType: (type?: number): { code: number, desc: string } | null => {
    for (const key in SchemeLedSource) {
      const item = SchemeLedSource[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number, desc: string } | undefined)[] => {
    const types = Object.keys(SchemeLedSource).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return SchemeLedSource[i as keys]
      }
      return
    })
    const newTypes = types.filter(function (s) {
      return s && s;
    })
    return newTypes
  },
};
