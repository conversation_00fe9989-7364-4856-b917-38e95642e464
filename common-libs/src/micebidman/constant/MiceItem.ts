// MiceItem.ts

type keys = 'STAY' | 'PLACE' | 'CATERING' | 'VEHICLE' | 'ATTENDANT' |
  'ACTIVITY' | 'INSURANCE' | 'MATERIAL' | 'TRAFFIC' | 'PRESENT' |
  'OTHER'

export const MiceItemConstant = {
  // 基础服务类型
  STAY: { code: 1, desc: '住宿' },
  PLACE: { code: 2, desc: '会场' },
  CATERING: { code: 4, desc: '用餐' },
  VEHICLE: { code: 8, desc: '用车' },

  // 人员和活动类型
  ATTENDANT: { code: 16, desc: '服务人员' },
  ACTIVITY: { code: 32, desc: '拓展活动' },

  // 保障类型
  INSURANCE: { code: 64, desc: '保险' },
  MATERIAL: { code: 128, desc: '布展物料' },

  // 交通和礼品类型
  TRAFFIC: { code: 256, desc: '交通' },
  PRESENT: { code: 512, desc: '礼品' },

  // 其他类型
  OTHER: { code: 1024, desc: '其他' },
  FULL_SERVICE_FEE: { code: 2048, desc: '全单服务费' },
  PLATFORM_SERVICE_FEE: { code: 4096, desc: '平台服务费' },

  // 工具方法
  ofType: (type?: number): { code: number, desc: string } | null => {
    for (const key in MiceItemConstant) {
      const item = MiceItemConstant[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number, desc: string } | undefined)[] => {
    const types = Object.keys(MiceItemConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return MiceItemConstant[i as keys]
      }
      return
    })
    const newTypes = types.filter(function (s) {
      return s && s;
    })
    return newTypes
  },
};
