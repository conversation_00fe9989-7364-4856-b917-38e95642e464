type keys = 'APPROVAL' | 'REJECT' | 'COMPLETED'

export const SwitchStateType = {
    // 状态类型定义
    APPROVAL: { code: 10, desc: '审批中' },
    REJECT: { code: 20, desc: '审批驳回' },
    COMPLETED: { code: 30, desc: '已完成' },

    // 工具方法
    ofType: (type?: number): { code: number, desc: string } | null => {
        for (const key in SwitchStateType) {
            const item = SwitchStateType[key as keys];
            if (type === item?.code) {
                return item;
            }
        }
        return null;
    },

    toArray: (): ({ code: number, desc: string } | undefined)[] => {
        const types = Object.keys(SwitchStateType).map((i: string) => {
            if (i !== 'ofType' && i !== 'toArray') {
                return SwitchStateType[i as keys]
            }
            return undefined
        })
        const newTypes = types.filter((s): s is { code: number, desc: string } => {
            return s !== undefined;
        })
        return newTypes
    },
};
