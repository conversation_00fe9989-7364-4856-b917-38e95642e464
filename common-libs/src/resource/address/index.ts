export interface addressRes{
    /*id */
    id?: number;

    /*地区编码 */
    code?: string;

    /*上级行政区划id */
    parentId?: number;

    /*区号 */
    areaCode?: string;

    /*城市名称 */
    name?: string;

    /*英文名称 */
    enName?: string;

    /*城市类型 */
    type?: number;

    /*城市类型 */
    level?: string;

    /*洲类型 */
    continentsType?: string;

    /*洲名称 */
    continentsName?: string;

    /*经度 */
    lng?: string;

    /*维度 */
    lat?: string;

    /*是否国际城市0否1是 */
    internationalFlag?: number;

    /*城市英文首字符 */
    initial?: string;

    /*拼音检索码 */
    pinyinSearchCode?: string;

    /*所属国家iD */
    countryId?: number;

    /*所属国家名称 */
    countryName?: string;

    /*所属省份id */
    provinceId?: number;

    /*所属省份名称 */
    provinceName?: string;

    /*所属城市id */
    cityId?: number;

    /*所属城市名称 */
    cityName?: string;

    /*火车站 */
    trainStation?: string;

    /*机场 */
    airport?: string;

    /*推荐机场 */
    recommendedAirport?: string;

    /*创建人 */
    createBy?: string;

    /*创建时间 */
    gmtCreate?: Record<string, unknown>;

    /*更新时间 */
    gmtModified?: Record<string, unknown>;

    /*最后修改人 */
    lastModifiedBy?: string;

    /*是否删除 */
    deleted?: boolean;

    /* */
    districtPopularType?: string;

    /*上级行政区划名称 */
    parentName?: string;

    /*供应商list */
    providerMapList?: {
      /*自增id */
      id?: number;

      /*映射国旅行政区划id */
      mainId?: number;

      /*供应商code */
      providerCode?: string;

      /*供应商pathKey */
      pathKey?: string;

      /*供应商行政区划id */
      districtId?: string;

      /*上级PathKey */
      parentPathKey?: string;

      /*供应商地区编码 */
      districtCode?: string;

      /*供应商区域名称 */
      districtName?: string;

      /*供应商区域级别 */
      districtLevel?: string;
    }[];
}

// 请求入参
export interface DistrictProviderMapPageReq {
  id?: number;

  /* */
  mainId?: number;

  /* */
  providerCode?: string;

  /* */
  pathKey?: string;

  /* */
  districtId?: string;

  /* */
  districtName?: string;

  /* */
  districtLevel?: string;

  /* */
  parentPathKey?: string;

  districtMappingFlag:any;

  proverCode:string|number;

  name:string;

  level:string;
}
