// 同步状态
export const asyncStatus:any = {
    "0":"保存",
    "10":'同步中',
    "20":'同步完成',
    "999":'同步失败',
}

// 聚合状态
export const aggregationStatus:any = {
    "0":"保存",
    "10":'聚合中',
    "20":'待人工聚合',
    "30":'聚合完成',
    "40":'已挂起',
    "999":'聚合失败',
}

// 供应商
export const supplierType:any = {
    "QT":"千淘",
    "XC":'携程',
    "RJ":'如家',
    "TXFC":'天下房仓',
    "JJ":'锦江',
    "YD":'亚朵',
    "HZ":'华住',
}

// 地址供应商
export const addressSupplierType:any = {
    "QT":"千淘",
    "XC":'携程',
    "RJ":'如家',
    "TXFC":'天下房仓',
    "JJ":'锦江',
    "YD":'亚朵',
    "HZ":'华住',
    "AMAP":"高德",
    "VETECH":'胜意',
}

// 地址级别
export const addressLevel:any = {
    "country":"国家",
    "province":'省份',
    "city":'城市',
    "district":'区县',
    "street":'街道',
}

// 地址类型
 export const addressType:any = {
    1:"省会城市",
    2:'直辖市',
    0:'无',
    3:'地级市',
    4:'县级市',
}

// 品牌分类
export const brandCategory:any = {
    'ECONOMY':"快捷连锁",
    "MIDDLING":'中端连锁',
    'HIGH_END':"高端连锁",
    "OTHER":'其他',
}

// 品牌类型
export const brandType:any = {
    1:"集团",
    2:'品牌',
}

// 映射模块类型
export const moduleType:any = {
    '1':"酒店映射",
    "2":'品牌映射',
    '3':"品牌集团映射",
    "4":'地址映射',
}

// 映射操作类型
export const operateType:any = {
    '1':"映射",
    "2":'解除映射',
}

// 星级

export const levelType:any= {
    '50':"五星",
    "40":'四星/高档',
    "30":'三星/舒适',
    "20":'二星',
    "10":'经济',
    "0":'其他'
}