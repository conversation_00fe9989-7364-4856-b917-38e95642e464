import { Dayjs } from 'dayjs';

// 住宿需求价格测算
export interface DemandCalcStayObj {
  /*测算日期 */
  calcDate?: Dayjs | null;

  /*房型 大床/双床/套房enum:[[{"code":1,"desc":"大床房"},{"code":2,"desc":"双床房"},{"code":3,"desc":"套房"}]],可用值:1,2,3 */
  roomType?: number;

  /*早餐类型 无早/单早/双早enum:[[{"code":0,"desc":"无早"},{"code":1,"desc":"含早"}]],可用值:0,1 */
  breakfastType?: number;

  /*	酒店所在城市id */
  cityId?: number;

  /*酒店所在区域id,支持多区域,逗号分割\n 如果多个区域,则多个区域同时测算,并返回最高的区域的价格 */
  districtIds?: string;

  /*酒店等级:bitmap\n 例如:345, 如多个星级,取最高星级 */
  level?: number;

  /*需求中心点经度 */
  latitude?: string;

  /*需求中心点纬度 */
  longitude?: string;

  /*需求范围:单位米(可选) */
  distanceRange?: number;

  /*需求中心的地标名称 */
  centerMarker?: string;
}

// 会场需求价格测算
export interface DemandCalcPlaceObj {
  /*测算日期 */
  calcDate?: Dayjs | null;

  /*使用时间 上午/下午/晚间 bitmap */
  usageTime?: number;

  /*使用用途 会议举行/布展搭建/会议撤场 bitmap */
  usagePurpose?: number;

  /*人数 */
  personNum?: number;

  /*面积 */
  area?: number;

  /*灯下层高 */
  underLightFloor?: number;

  /*摆台形式enum:[[{"code":1,"desc":"U型式"},{"code":2,"desc":"董事会式"},{"code":3,"desc":"剧院式"},{"code":4,"desc":"海岛式"},{"code":5,"desc":"酒会式"},{"code":6,"desc":"课桌式"},{"code":7,"desc":"鱼骨式"}]],可用值:1,2,3,4,5,6,7 */
  tableType?: number;

  /*是否需要led */
  hasLed?: boolean;

  /*led数量 */
  ledNum?: number;

  /*led规格说明 */
  ledSpecs?: string;

  /*是否需要茶歇 */
  hasTea?: boolean;

  /*茶歇标准/每人 */
  teaEachTotalPrice?: number;

  /*茶歇说明 */
  teaDesc?: string;

  /*	酒店所在城市id */
  cityId?: number;

  /*酒店所在区域id,支持多区域,逗号分割\n 如果多个区域,则多个区域同时测算,并返回最高的区域的价格 */
  districtIds?: string;

  /*酒店等级:bitmap\n 例如:345, 如多个星级,取最高星级 */
  level?: number;

  /*需求中心点经度 */
  latitude?: string;

  /*需求中心点纬度 */
  longitude?: string;

  /*需求范围:单位米(可选) */
  distanceRange?: number;

  /*需求中心的地标名称 */
  centerMarker?: string;
}

// 用餐需求价格测算
export interface DemandCalcCateringObj {
  /*测算日期 */
  calcDate?: Dayjs | null;

  /*是否酒店提供用餐 */
  isInsideHotel?: boolean;

  /*用餐类型enum:[[{"code":0,"desc":"桌餐"},{"code":1,"desc":"自助"},{"code":2,"desc":"盒饭"}]],可用值:0,1,2 */
  cateringType?: number;

  /*用餐时间 午餐/晚餐enum:[[{"code":0,"desc":"午餐"},{"code":1,"desc":"晚餐"}]],可用值:0,1 */
  cateringTime?: number;

  /*人数 */
  personNum?: number;

  /*用餐标准 */
  demandUnitPrice?: number;

  /*是否包含酒水 */
  isIncludeDrinks?: boolean;

  /*	酒店所在城市id */
  cityId?: number;

  /*酒店所在区域id,支持多区域,逗号分割\n 如果多个区域,则多个区域同时测算,并返回最高的区域的价格 */
  districtIds?: string;

  /*酒店等级:bitmap\n 例如:345, 如多个星级,取最高星级 */
  level?: number;

  /*需求中心点经度 */
  latitude?: string;

  /*需求中心点纬度 */
  longitude?: string;

  /*需求范围:单位米(可选) */
  distanceRange?: number;

  /*需求中心的地标名称 */
  centerMarker?: string;
}

// 用车需求价格测算
export interface DemandCalcVehicleObj {
  /*测算日期 */
  calcDate?: Dayjs | null;

  /*使用方式 单趟/包车enum:[[{"code":0,"desc":"单趟"},{"code":1,"desc":"包车"}]],可用值:0,1 */
  usageType?: number;

  /*使用时长 半天/全天enum:[[{"code":0,"desc":"半天"},{"code":1,"desc":"全天"}]],可用值:0,1 */
  usageTime?: number;

  /*	座位数 */
  seats?: number;

  /*车辆数量 */
  vehicleNum?: number;

  /*品牌 */
  brand?: string;

  /*路线,多程逗号分隔 */
  route?: string;
}

// 服务人员需求价格测算
export interface DemandCalcAttendantObj {
  /*测算日期 */
  calcDate?: Dayjs | null;

  /*人员类型enum:[[{"code":0,"desc":"摄影师"},{"code":1,"desc":"导游"},{"code":2,"desc":"翻译"},{"code":3,"desc":"主持人"},{"code":4,"desc":"服务人员"},{"code":5,"desc":"其他"},{"code":6,"desc":"会务平台服务人员"}]],可用值:0,1,2,3,4,5,6 */
  type?: number;

  /*人数 */
  personNum?: number;

  /*工作范围 */
  duty?: string;
}

// 拓展活动需求价格测算
export interface DemandCalcActivityObj {
  /*测算日期 */
  calcDate?: Dayjs | null;

  /*费用标准 */
  demandUnitPrice?: number;

  /*人数 */
  personNum?: number;

  /*活动说明 */
  description?: string;
}

// 保险需求价格测算
export interface DemandCalcInsuranceObj {
  /*	需求单价 */
  demandUnitPrice?: number;

  /*人数 */
  personNum?: number;

  /*保险产品id(以互动时为准,需求时只为意向) */
  productId?: number;

  /*产品所属商户id */
  productMerchantId?: number;

  /*险种名称 */
  insuranceName?: string;

  /*险种条目 */
  insuranceContent?: string;
}

// 布展物料需求价格测算
export interface DemandCalcMaterialObj {
  /*费用标准/总 */
  demandTotalPrice?: number;

  /*需求布展物料表id */
  miceDemandMaterialId?: number;

  /*物料类型 枚举enum:[[{"code":0,"desc":"条幅"},{"code":1,"desc":"投影仪"},{"code":2,"desc":"音响"},{"code":3,"desc":"麦克"},{"code":4,"desc":"灯光"}]],可用值:0,1,2,3,4 */
  type?: number;

  /*数量 */
  num?: number;

  /*规格说明 */
  specs?: string;

  /*单价 */
  unitPrice?: number;
}

// 交通需求价格测算
export interface DemandCalcTrafficObj {
  /*测算日期 */
  calcDate?: Dayjs | null;

  /*需求总金额 */
  demandTotalPrice?: number;

  /*需求交通id */
  miceDemandTrafficId?: number;

  /*需求测算id */
  miceDemandTrafficCalcId?: number;

  /*交通类型 飞机/火车 */
  type?: number;

  /*航班号/车次号 */
  numberCode?: string;

  /*出发地城市id */
  departureCityId?: number;

  /*出发地城市名称 */
  departureCityName?: string;

  /*出发地机场/车站代码 */
  departureSiteCode?: string;

  /*出发地机场/车站代码名称 */
  departureSiteName?: string;

  /*出发时间 */
  departureDate?: Dayjs | null;

  /*到达地城市id */
  arrivalCityId?: number;

  /*到达地城市名称 */
  arrivalCityName?: string;

  /*到达地机场/车站代码 */
  arrivalSiteCode?: string;

  /*到达地机场/车站代码名称 */
  arrivalSiteName?: string;

  /*到达时间 */
  arrivalDate?: Dayjs | null;

  /*行程数量 */
  num?: number;

  /*行程单价 */
  unitPrice?: number;

  /*总费用 */
  totalPrice?: number;
}

// 礼品需求价格测算
export interface DemandCalcPresentObj {
  /*送达日期 */
  deliveryDate?: Dayjs | null;

  /*费用标准 */
  demandTotalPrice?: number;

  /*礼品数量 */
  personNum?: number;

  /*礼品说明 */
  personSpecs?: string;

  /*礼品产品id(以互动时为准,需求时只为意向) */
  productId?: number;

  /*产品所属商户id */
  productMerchantId?: number;

  /*产品名称,当未选择产品时可自由修改 */
  productName?: string;
}

// 其他需求价格测算
export interface DemandCalcOtherObj {
  /*测算日期 */
  calcDate?: Dayjs | null;

  /*数量 */
  num?: number;

  /*单位 */
  unit?: string;

  /*规格描述 */
  specs?: string;

  /*费用标准 */
  demandTotalPrice?: number;
}
