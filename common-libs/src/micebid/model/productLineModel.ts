import { IPageRequest } from "../../basic";

export class IProductLineFilter extends IPageRequest {
    begin?: string
    end?: string
    id?: number
    name?: string
    adminName?: string
    advisorName?: string
}

// 产品线流程类型
export interface IProductLineFlow {
    flowId?: string
    flowName?: string
    flowType?: string
    flowStatus?: string
    packageInfo?: string
    operation?: string
}

// 管理员信息
export interface IAdmin {
    id?: number
    nickName?: string
    // 其他管理员相关字段
}

// 顾问信息
export interface ICounsellor {
    id?: number
    nickName?: string
    // 其他顾问相关字段
}

// 用户信息接口
export interface IUser {
    nickName: string
    role: number
    id?: number
}

export class IProductLine {
    id?: number | null
    creator?: string
    createTime?: string
    updater?: string
    updateTime?: string
    name?: string // 产品线名称
    productName?: string // 产品线名称（兼容）
    adminList?: IAdmin[] // 产品线管理员列表
    counsellorList?: ICounsellor[] // 会议顾问列表
    userList?: IUser[] // 用户列表（合并管理员和顾问）
    processList?: any[] // 流程列表
    processCount?: number // 流程数量
    createName?: string // 创建人姓名
    gmtCreate?: string // 创建时间
    meetingLocation?: string // 会议属地
    description?: string // 产品线简介
    imgUrl?: string // 产品线图片
    processIdList?: IProductLineFlow[] // 产品线流程
    flows?: IProductLineFlow[] // 产品线流程
}