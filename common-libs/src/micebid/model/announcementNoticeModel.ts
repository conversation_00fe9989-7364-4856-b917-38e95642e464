import { IPageRequest } from "../../basic";

export class IAnnouncementNoticeFilter extends IPageRequest {
    begin?:string
    end?:string
    isWindowShow?:boolean
    isWindow?:boolean
    effectScope?:number
    state?:string
}

export class IAnnouncementNotice {
    id?: number | null
    creator?: string
    createTime?: string
    updater?: string
    updateTime?: string
    title?: string
    effectScope?: number
    contentForm?: number
    informContent?: string
    sort?: number
    isWindow?: boolean
    name?: string
    code?: string
    state?: string
    description?: string
}

export interface IMiceBidNotice {
    /*id */
    id: number;

    /*标题 */
    title: string;

    /*内容形式 */
    contentForm: number;

    /*通知作用范围 */
    effectScope: Array<number>;

    /*排序 */
    sort: number;

    /*是否弹窗 */
    isWindow: boolean;

    /*创建人姓名 */
    createName: string;

    /*创建时间 */
    gmtCreate: string;
}