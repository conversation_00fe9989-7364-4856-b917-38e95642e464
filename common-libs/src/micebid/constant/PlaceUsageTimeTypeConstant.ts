// 使用时间

type keys = 'ONE' | 'TWO' | 'THREE' | 'FOUR' | 'SIX' | 'SEVEN';

export const PlaceUsageTimeTypeConstant = {
  ONE: { code: 1, desc: '上午' },
  TWO: { code: 2, desc: '下午' },
  THREE: { code: 3, desc: '上午+下午' },
  FOUR: { code: 4, desc: '晚间' },
  SIX: { code: 6, desc: '下午+晚间' },
  SEVEN: { code: 7, desc: '上午+下午+晚间' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in PlaceUsageTimeTypeConstant) {
      const item = PlaceUsageTimeTypeConstant[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(PlaceUsageTimeTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return PlaceUsageTimeTypeConstant[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
