/**
 * 价格询价相关枚举常量
 */

/**
 * 季节类型枚举
 */
export const SeasonTypeConstant = {
  LIGHT: { "type": 1, "name": "淡季" },
  PEAK: { "type": 2, "name": "旺季" }
} as const;

export type SeasonTypeKeys = 'LIGHT' | 'PEAK';

/**
 * 桌型类型枚举
 */
export const TableShapeConstant = {
  U_SHAPE: { "type": 1, "name": "U型式" },
  BOARD_STYLE: { "type": 2, "name": "宴会式/董事会式" },
  THEATER_STYLE: { "type": 3, "name": "剧院式" },
  ISLAND_STYLE: { "type": 4, "name": "海岛式" },
  COCKTAIL_STYLE: { "type": 5, "name": "酒会式" },
  CLASSROOM_STYLE: { "type": 6, "name": "课桌式" },
  FISH_BONE_STYLE: { "type": 7, "name": "鱼骨式" },
  RETURN_STYLE: { "type": 8, "name": "回形式" }
} as const;

export type TableShapeKeys = 'U_SHAPE' | 'BOARD_STYLE' | 'THEATER_STYLE' | 'ISLAND_STYLE' | 
  'COCKTAIL_STYLE' | 'CLASSROOM_STYLE' | 'FISH_BONE_STYLE' | 'RETURN_STYLE';

/**
 * 时节设置类型枚举
 */
export const QuarterSettingTypeConstant = {
  ENABLE: { "value": true, "name": "按淡旺季设置" },
  DISABLE: { "value": false, "name": "无淡旺季" }
} as const;

export type QuarterSettingTypeKeys = 'ENABLE' | 'DISABLE';

/**
 * 价格阶梯设置类型枚举
 */
export const PriceTierSettingTypeConstant = {
  ENABLE: { "value": true, "name": "按照阶梯设置价格" },
  DISABLE: { "value": false, "name": "不按阶梯设置价格" }
} as const;

export type PriceTierSettingTypeKeys = 'ENABLE' | 'DISABLE';


/**
 * 价格类型枚举
 */
export const PriceTypeConstant = {
  MARKET_PRICE: { "code": 10, "desc": "市场价" },
  AGREEMENT_PRICE: { "code": 20, "desc": "协议价" },
  LIST_PRICE: { "code": 30, "desc": "门市价" },
  LIGHT_SEASON_MARKET_PRICE: { "code": 40, "desc": "淡季门市价" },
  LIGHT_SEASON_AGREEMENT_PRICE_UNDER_50: { "code": 50, "desc": "淡季协议价,50人以下" },
  LIGHT_SEASON_AGREEMENT_PRICE_ABOVE_50: { "code": 60, "desc": "淡季协议价,50人以上" },
  PEAK_SEASON_MARKET_PRICE: { "code": 70, "desc": "旺季门市价" },
  PEAK_SEASON_AGREEMENT_PRICE_UNDER_50: { "code": 80, "desc": "旺季协议价,50人以下" },
  PEAK_SEASON_AGREEMENT_PRICE_ABOVE_50: { "code": 90, "desc": "旺季协议价,50人以上" },
  LIGHT_SEASON_HALF_DAY_MARKET_PRICE: { "code": 100, "desc": "淡季半天市场价" },
  LIGHT_SEASON_FULL_DAY_MARKET_PRICE: { "code": 110, "desc": "淡季全天市场价" },
  LIGHT_SEASON_HALF_DAY_AGREEMENT_PRICE: { "code": 120, "desc": "淡季半天协议价" },
  LIGHT_SEASON_FULL_DAY_AGREEMENT_PRICE: { "code": 130, "desc": "淡季全天协议价" },
  PEAK_SEASON_HALF_DAY_MARKET_PRICE: { "code": 140, "desc": "旺季半天市场价" },
  PEAK_SEASON_FULL_DAY_MARKET_PRICE: { "code": 150, "desc": "旺季全天市场价" },
  PEAK_SEASON_HALF_DAY_AGREEMENT_PRICE: { "code": 160, "desc": "旺季半天协议价" },
  PEAK_SEASON_FULL_DAY_AGREEMENT_PRICE: { "code": 170, "desc": "旺季全天协议价" },
  MARKET_PRICE_FULL_DAY: { "code": 180, "desc": "市场价全天价" },
  MARKET_PRICE_HALF_DAY: { "code": 190, "desc": "市场价半天价" },
  LIST_PRICE_FULL_DAY: { "code": 200, "desc": "门市价半天价" },
  LIST_PRICE_HALF_DAY: { "code": 210, "desc": "门市价全天价" },
  AGREEMENT_PRICE_FULL_DAY: { "code": 220, "desc": "协议价半天价" },
  AGREEMENT_PRICE_HALF_DAY: { "code": 230, "desc": "协议价全天价" },
} as const;

export type PriceTypeKeys = 
  'MARKET_PRICE' | 
  'AGREEMENT_PRICE' | 
  'LIST_PRICE' | 
  'LIGHT_SEASON_MARKET_PRICE' | 
  'LIGHT_SEASON_AGREEMENT_PRICE_UNDER_50' | 
  'LIGHT_SEASON_AGREEMENT_PRICE_ABOVE_50' | 
  'PEAK_SEASON_MARKET_PRICE' | 
  'PEAK_SEASON_AGREEMENT_PRICE_UNDER_50' | 
  'PEAK_SEASON_AGREEMENT_PRICE_ABOVE_50' | 
  'LIGHT_SEASON_HALF_DAY_MARKET_PRICE' | 
  'LIGHT_SEASON_FULL_DAY_MARKET_PRICE' | 
  'LIGHT_SEASON_HALF_DAY_AGREEMENT_PRICE' | 
  'LIGHT_SEASON_FULL_DAY_AGREEMENT_PRICE' | 
  'PEAK_SEASON_HALF_DAY_MARKET_PRICE' | 
  'PEAK_SEASON_FULL_DAY_MARKET_PRICE' | 
  'PEAK_SEASON_HALF_DAY_AGREEMENT_PRICE' | 
  'PEAK_SEASON_FULL_DAY_AGREEMENT_PRICE' | 
  'MARKET_PRICE_FULL_DAY' | 
  'MARKET_PRICE_HALF_DAY';

/**
 * 文件类型枚举
 */
export const FileTypeConstant = {
  // 通用类型
  DOCUMENT: { "code": 1, "desc": "文档" },
  PICTURE: { "code": 2, "desc": "图片" },
  VIDEO: { "code": 3, "desc": "视频" },

  // 详细类型
  PAY_PROVE: { "code": 20, "desc": "支付凭证" },
  RECEIPT: { "code": 22, "desc": "收据" },
  REFUND_APPLY: { "code": 23, "desc": "退款申请单" },
  PRODUCT_CLAUSE: { "code": 24, "desc": "产品条款" },
  WITNESS_MATERIALS: { "code": 25, "desc": "见证性材料" },
  PRODUCT_FEATURE: { "code": 26, "desc": "产品特色" },
  EXEMPTION_TERMS: { "code": 30, "desc": "免除责任条款" },
  UNKNOWN: { "code": 7, "desc": "未知文件类型" },
  RELATED_FILE:{"code":31, "desc" : "相关文件"}
} as const;

export type FileTypeKeys = 
  'DOCUMENT' | 
  'PICTURE' | 
  'VIDEO' | 
  'PAY_PROVE' | 
  'RECEIPT' | 
  'REFUND_APPLY' | 
  'PRODUCT_CLAUSE' | 
  'WITNESS_MATERIALS' | 
  'PRODUCT_FEATURE' | 
  'EXEMPTION_TERMS' |
  'UNKNOWN';

/**
 * 根据代码获取文件类型信息
 */
export const getFileType = (code?: number): { "code": number, "desc": string } | null => {
  for (const key in FileTypeConstant) {
    const item = FileTypeConstant[key as FileTypeKeys];
    if (code === item.code) {
      return item;
    }
  }
  return null;
}

/**
 * 根据类型获取桌型信息
 */
export const getTableShape = (type?: number): { "type": number, "name": string } | null => {
  for (const key in TableShapeConstant) {
    const item = TableShapeConstant[key as TableShapeKeys];
    if (type === item.type) {
      return item;
    }
  }
  return null;
}

/**
 * 根据类型获取季节信息
 */
export const getSeasonType = (type?: number): { "type": number, "name": string } | null => {
  for (const key in SeasonTypeConstant) {
    const item = SeasonTypeConstant[key as SeasonTypeKeys];
    if (type === item.type) {
      return item;
    }
  }
  return null;
}

/**
 * 根据值获取时节设置类型信息
 */
export const getQuarterSettingType = (value?: boolean): { "value": boolean, "name": string } | null => {
  for (const key in QuarterSettingTypeConstant) {
    const item = QuarterSettingTypeConstant[key as QuarterSettingTypeKeys];
    if (value === item.value) {
      return item;
    }
  }
  return null;
}

/**
 * 根据值获取价格阶梯设置类型信息
 */
export const getPriceTierSettingType = (value?: boolean): { "value": boolean, "name": string } | null => {
  for (const key in PriceTierSettingTypeConstant) {
    const item = PriceTierSettingTypeConstant[key as PriceTierSettingTypeKeys];
    if (value === item.value) {
      return item;
    }
  }
  return null;
}


/**
 * 根据代码获取价格类型信息
 */
export const getPriceType = (code?: number): { "code": number, "desc": string } | null => {
  for (const key in PriceTypeConstant) {
    const item = PriceTypeConstant[key as PriceTypeKeys];
    if (code === item.code) {
      return item;
    }
  }
  return null;
} 