// 是否包含酒水

type keys = 'FALSE' | 'TRUE';

export const HaveDrinksTypeConstantNum = {
  FALSE: { code: 0, desc: '不含酒水' },
  TRUE: { code: 1, desc: '含酒水（啤酒、饮料、不含白酒）' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in HaveDrinksTypeConstantNum) {
      const item = HaveDrinksTypeConstantNum[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(HaveDrinksTypeConstantNum).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return HaveDrinksTypeConstantNum[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
