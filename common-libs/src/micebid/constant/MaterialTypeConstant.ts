// 物料类型

type keys =
  | 'BANNER'
  | 'PROJECTOR'
  | 'SOUND_SYSTEM'
  | 'MICROPHONE'
  | 'LIGHTING'
  | 'TABLE_SIGN'
  | 'CAR_DECAL'
  | 'CERTIFICATE'
  | 'TROPHY'
  | 'ROLLUP_BANNER'
  | 'X_STAND';

export const MaterialTypeConstant = {
  BANNER: { code: 0, desc: '条幅' },
  PROJECTOR: { code: 1, desc: '投影仪' },
  SOUND_SYSTEM: { code: 2, desc: '音响' },
  MICROPHONE: { code: 3, desc: '麦克' },
  LIGHTING: { code: 4, desc: '灯光' },
  TABLE_SIGN: { code: 5, desc: '桌牌' },
  CAR_DECAL: { code: 6, desc: '车贴' },
  CERTIFICATE: { code: 7, desc: '奖状' },
  TROPHY: { code: 8, desc: '奖杯' },
  ROLLUP_BANNER: { code: 9, desc: '易拉宝' },
  X_STAND: { code: 10, desc: 'X展架' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in MaterialTypeConstant) {
      const item = MaterialTypeConstant[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(MaterialTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return MaterialTypeConstant[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
