// 房型

type keys = 'BIG' | 'DOUBLE' | 'ROOM';

export const RoomTypeConstant = {
  BIG: { code: 1, desc: '大床房（默认1人）' },
  DOUBLE: { code: 2, desc: '双床房' },
  ROOM: { code: 3, desc: '套房（默认1人）' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in RoomTypeConstant) {
      const item = RoomTypeConstant[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(RoomTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return RoomTypeConstant[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
