// 早餐类型

type keys = 'DOMESTIC' | 'ABROAD';

export const LedSourceTypeConstant = {
  DOMESTIC: { code: 1, desc: '酒店自有' },
  ABROAD: { code: 2, desc: '广告公司外搭' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in LedSourceTypeConstant) {
      const item = LedSourceTypeConstant[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(LedSourceTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return LedSourceTypeConstant[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
