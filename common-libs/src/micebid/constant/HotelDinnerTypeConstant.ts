// 是否酒店内用餐

type keys = 'YES' | 'NO';

export const HotelDinnerTypeConstant = {
  YES: { code: 1, desc: '酒店提供' },
  NO: { code: 0, desc: '非酒店提供' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in HotelDinnerTypeConstant) {
      const item = HotelDinnerTypeConstant[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(HotelDinnerTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return HotelDinnerTypeConstant[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
