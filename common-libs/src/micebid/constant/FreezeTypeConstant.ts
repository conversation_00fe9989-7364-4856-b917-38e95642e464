type keys = 'MANUAL' | 'CONTRACT_EXPIRED' | 'NO_BILL' | 'INIT_DEFAULT';

/**
 * 冻结类型常量
 */
export const FreezeTypeConstant = {
  MANUAL: { "type": "10", "name": "手动停用" },
  MANUAL_UNFREEZE: { "type": "0", "name": "手动解冻" },
  CONTRACT_EXPIRED: { "type": "20", "name": "合同到期自动停用" },
  NO_BILL: { "type": "30", "name": "未录账单自动停用" },
  INIT_DEFAULT: { "type": "40", "name": "初始化默认停用" }
} as const;

/**
 * 根据类型获取冻结类型信息
 */
export const getFreezeTypeInfo = (type?: number): { "type": number, "name": string } | null => {
  for (const key in FreezeTypeConstant) {
    const item = FreezeTypeConstant[key as keys];
    if (type === item.type) {
      return item;
    }
  }
  return null;
} 