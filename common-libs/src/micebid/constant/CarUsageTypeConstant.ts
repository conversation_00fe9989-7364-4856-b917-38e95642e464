// 用车方式

type keys = 'ONCE' | 'ALL';

export const CarUsageTypeConstant = {
  ONCE: { code: 0, desc: '单趟' },
  ALL: { code: 1, desc: '包车' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in CarUsageTypeConstant) {
      const item = CarUsageTypeConstant[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(CarUsageTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return CarUsageTypeConstant[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
