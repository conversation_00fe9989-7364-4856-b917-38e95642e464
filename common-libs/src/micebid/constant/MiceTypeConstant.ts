// 会议类型

type keys = 'ONE' | 'TWO' | 'THREE' | 'FOUR' | 'FIVE' | 'SIX' | 'SEVEN';

export const MiceTypeConstant = {
  ONE: { code: 1, desc: '开盘会' },
  TWO: { code: 2, desc: '客户会' },
  THREE: { code: 3, desc: '培训会' },
  FOUR: { code: 4, desc: '发布会' },
  FIVE: { code: 5, desc: '展览会' },
  SIX: { code: 6, desc: '内部沟通会' },
  SEVEN: { code: 7, desc: '招商会' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in MiceTypeConstant) {
      const item = MiceTypeConstant[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(MiceTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return MiceTypeConstant[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
