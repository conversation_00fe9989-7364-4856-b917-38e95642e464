// 是否包含酒水

type keys = 'FALSE' | 'TRUE';

export const HaveDrinksTypeConstant = {
  FALSE: { code: false, desc: '不含酒水' },
  TRUE: { code: true, desc: '含酒水（啤酒、饮料、不含白酒）' },

  ofType: (type?: boolean): { code: boolean; desc: string } | null => {
    for (const key in HaveDrinksTypeConstant) {
      const item = HaveDrinksTypeConstant[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: boolean; desc: string } | undefined)[] => {
    const types = Object.keys(HaveDrinksTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return HaveDrinksTypeConstant[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
