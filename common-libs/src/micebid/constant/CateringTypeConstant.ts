// 用餐类型

type keys = 'ZHUO' | 'ZI' | 'HE' | 'WAI' | 'WAI_MAI';

export const CateringTypeConstant = {
  ZHUO: { code: 0, desc: '桌餐' },
  ZI: { code: 1, desc: '自助' },
  HE: { code: 2, desc: '盒饭' },
  WAI: { code: 3, desc: '外出用餐' },
  WAI_MAI: { code: 4, desc: '外卖' },

  ofType: (type?: number): { code: number; desc: string } | null => {
    for (const key in CateringTypeConstant) {
      const item = CateringTypeConstant[key as keys];
      if (type === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number; desc: string } | undefined)[] => {
    const types = Object.keys(CateringTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return CateringTypeConstant[i as keys];
      }
      return;
    });
    const newTypes = types.filter(function (s) {
      return s && s;
    });
    return newTypes;
  },
};
