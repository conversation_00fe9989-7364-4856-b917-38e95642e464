import {IPayData} from "./basicModel";
import {IPayRequest} from "./payModel";
import {IPageRequest} from "../../basic";


export interface IHaierBudgetDepartment {
    code?: string

    /**
     * 名称
     */
    name?: string

    /**
     * 父部门编码
     */
    parentCode?: string

    /**
     * 功能范围编码F：生产；V：销售；E：开发；L：管理
     */
    funcCode?: string

    /**
     * 0：无效；1：有效
     */
    isValid?: number

    /**
     * 修改时间
     */
    gmtModified?: string

    /**
     * 结算单位编码
     */
    companyCode?: string

    /**
     * 负责人编码
     */
    leaderCode?: string

    codePath?: string
}

export interface IHaierBudgetDeptBccDepartmentsRequest extends IPageRequest {
    code?: string

    /**
     * 名称
     **/
    name?: string

    /**
     * 父部门编码
     **/
    parentCode?: string

    /**
     * 功能范围编码F：生产；V：销售；E：开发；L：管理
     **/
    funcCode?: string

    /**
     * 0：无效；1：有效
     **/
    isValid?: number

    /**
     * 结算单位编码
     **/
    companyCode?: string

    /**
     * 负责人编码
     **/
    leaderCode?: string
}

export interface IHaierAccountCompany {
    /**
     * 编码
     */
    code?: string

    /**
     * 名称
     */
    name?: string

    /**
     * 0：无效；1：有效
     */
    isValid?: number

    /**
     * 更新时间
     */
    gmtModified?: string

    /**
     * 功能范围编码F：生产；V：销售；E：开发；L：管理
     */
    funcCode?: string
}

export interface IHaierBudgetDeptBccAccountsRequest extends IPageRequest {
    /**
     * 预算部门编码
     **/
    bdCode?: string

    /**
     * 预算部门路径
     **/
    bdCodePath?: string

    /**
     * 结算单位编码
     **/
    accountCode?: string

    /**
     * 结算单位编码集合
     **/
    accountCodes?: string[]

    /**
     * 结算单位名称
     **/
    accountName?: string
}

export interface IHaierBudgetDeptBccBudgetItemResponse {
    year?: number

    month?: number

    itemCode?: string

    itemName?: string

    typeCode?: string

    typeName?: string

    depCode?: string

    depName?: string

    feeItemCode?: string

    feeItemName?: string

    createDate?: string

    creater?: string

    saleType?: number
}

export interface IHaierBudgetDeptBccBudgetItemRequest {
    /**
     * 预算部门编码
     */
    bdCode?: string

    /**
     * 费用科目，福利必填
     **/

    feeItem?: string

    /**
     * 业务类型,EAI 匹配视图
     **/
    applicationCode?: string
    haierBudgetType?: string
}

export interface IHaierProject {
    /**
     * 编码
     */
    code?: string

    /**
     * 名称
     */
    name?: string

    /**
     * 申请人编码
     */
    applicantCode?: string

    /**
     * 结算单位编码
     */
    companyCode?: string

    /**
     * 部门编码
     */
    departmentCode?: string

    /**
     * 修改日期
     */
    gmtModified?: string
}

export interface IHaierBudgetDeptBccProjectRequest extends IPageRequest {
    /**
     * 结算单位编码
     **/
    companyCode?: string

    /**
     * 项目编码
     **/
    code?: string

    /**
     * 项目名称
     **/
    name?: string
}

export interface IHaierWbs {
    /**
     * 编码
     */
    code?: string

    /**
     * 名称
     */
    name?: string

    /**
     * 负责人编码
     */
    leaderCode?: string

    /**
     * 结算单位编码
     */
    companyCode?: string

    /**
     * 部门编码
     */
    departmentCode?: string

    /**
     * 修改日期
     */
    gmtModified?: string
}

export interface IHaierBudgetDeptBccWBSRequest extends IPageRequest {
    /**
     * 结算单位编码
     **/
    companyCode?: string

    /**
     * 结算单位编码
     **/
    code?: string

    /**
     * 结算单位名称
     **/
    name?: string
}

export interface IHaierDcProjectItem {
    /**
     * 地产项目分期编码
     */
    guid?: string

    /**
     * 部门(项目)编码
     */
    code?: string

    /**
     * 部门名称
     */
    name?: string

    /**
     * 上级部门编码
     */
    parentCode?: string

    /**
     * 排序
     */
    seq?: string

    /**
     * 类别（Gs\dep\project）
     */
    type?: string

    /**
     * 1,有效；0，无效
     */
    state?: number

    /**
     * 最后更新日期
     */
    gmtLastModified?: string

    /**
     * 部门类别（project 表示项目；item 表示分期）
     */
    departmentType?: string

    /**
     * 公司编码
     */
    accountCode?: string

    /**
     * 公司名称
     */
    accountName?: string
}

export interface IHaierBudgetDeptBccDcProjectRequest extends IPageRequest {
    /**
     * 项目编码
     **/
    code?: string

    /**
     * 项目名称
     **/
    name?: string

    /**
     * 结算单位编码
     **/
    accountCode?: string
}

export interface IHaierBudgetDeptBccDcItemRequest extends IPageRequest {
    /**
     * 分期编码
     **/
    code?: string

    /**
     * 分期名称
     **/
    name?: string

    /**
     * 地产项目编码
     **/
    projectCode?: string
}