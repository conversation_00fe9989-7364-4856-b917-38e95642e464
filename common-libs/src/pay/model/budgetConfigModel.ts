export class IBudgetConfigRequest {
    applicationCode?: string
    payTypeList?: Array<IPayTypeList>
}

export class IPayTypeList {
    payTypeId?: number
    payTypeName?: string
    budgetSettingList?: Array<IBudgetSettingList>
}

export class IBudgetSettingList {
    budgetType?: string
    settingJson?: string
    feeItemList?: Array<IFeeItemList>
}

export class IFeeItemList {
    itemCode?: string
    itemName?: string
}
