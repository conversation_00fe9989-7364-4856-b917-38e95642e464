import {IPageRequest} from "../../basic";
import {IPayData} from "./basicModel";
import {IPayRequest} from "./payModel";


export interface IQueryVirtualAccountsRequest {
    /**
     * 应用code
     */
    applicationCode?: string;
    username?: string;
}

export interface IQueryVirtualAccountsResponse {
    /**
     * 账户
     */
    accountNo?: string
    accountName?: string

    /**
     * 1: 不发送 2：发送
     */
    smsCaptchaFlag?:number
    /**
     * 企业编码
     */
    enterpriseCode?: string

    /**
     * 企业名称
     */
    enterpriseName?: string

    /**
     * 可用金额，单位元
     */
    amount?: string

    /**
     * 1：山庄卡；2：充值账户；3：挂账账户；
     */
    type?: number
    _typeName?: string
}


export interface IVirtualPayRequest extends IPayRequest {
    accountNo?: string;

    /**
     * 验证码
     */
    captcha?: string;

    /**
     * 是否开启审批
     */
    startApproveFlag?: boolean
}


export interface IVirtualAccountsListRequest extends IPageRequest {
    /**
     * 账户
     */
    accountNo?: string;

    /**
     * 应用code
     */
    applicationCode?: string;
}

export interface IPaymentVirtualAccount {
    id?: number

    /**
     * 账户
     */
    accountNo?: string
    accountName?: string

    /**
     * 企业编码
     */
    enterpriseCode?: string

    /**
     * 企业名称
     */
    enterpriseName?: string

    /**
     * 金额，单位元
     */
    amount?: string

    /**
     * 1：山庄卡；2：充值账户；3：挂账账户；
     */
    type?: number

    /**
     * 1：企业级(企业下所有用户共用)；2：个人级(企业下指定用户可用,可多人)；
     */
    scope?: number

    /**
     * 过期时间，有效期不含此日期
     */
    expireDate?: string

    /**
     * 0：无效；1：有效
     */
    state?: number

    appInfoList?: IAppInfoList

    createBy?: string

    gmtCreate?: string

    lastModifiedBy?: string

    gmtModified?: string
}

export interface IAppInfoList {

    applicationCode?: string

    scope?: number

    remark?: string
}

export interface IVirtualChangeListRequest extends IPageRequest {
    /**
     * 账户
     */
    accountNo?: string;

    /**
     * 应用code
     */
    applicationCode?: string;

    code?: string

    enterpriseCode?: string

    type?: number

    remark?: string

    changeCode?: string

    changeBusinessCode?: string

    changeBegin?: string

    changeEnd?: string

    needPage?: boolean
}


export interface IPaymentVirtualAccountChange {
    id?: number

    code?: string

    /**
     * 账户
     */
    accountNo?: string

    /**
     * 企业编码
     */
    enterpriseCode?: string

    /**
     * 企业名称
     */
    enterpriseName?: string

    /**
     * 变动金额 消费为负数, 充值退款为正数 (金额，单位元)
     */
    amount?: string

    /**
     * 本次变动后剩余金额
     */
    balance?: string

    /**
     * 1：消费；2：退款；3：充值；
     */
    type?: number

    /**
     * 变动来源应用
     */
    applicationCode?: string

    /**
     * 费用变动说明
     */
    remark?: string

    /**
     * 变动凭证单号
     * 消费退款时为对应的支付单号;
     * 充值时为当前code
     */
    changeCode?: string

    /**
     * 变动凭证业务单号,为实际业务单号
     */
    changeBusinessCode?: string

     /**
     * 详情地址
     */
     orderDetailsUrl?: string

    /**
     * 附件id
     */
    attachmentFileId?: string

    createBy?: string

    gmtCreate?: string
}


export interface IVirtualRechargeRequest {
    /**
     * 账户
     */
    accountNo?:string

    /**
     * 充值金额
     */
    amount?:string

    /**
     * 充值流水单号
     */
    businessCode?:string

    /**
     * 充值说明
     */
    remark?:string

    /**
     * 重置凭证id
     */
    attachmentFileId?:string
}


export interface IPaymentVirtualAccountTypeMap {
     id?:number

     accountNo?:string

     applicationCode?:string

    /**
     * 1: 消费 2: 充值 3:消费+充值
     */
     scope?:number

    /**
     * 说明
     */
     remark?:string

     createBy?:string

     gmtCreate?:string

     lastModifiedBy?:string

     gmtModified?:string

}


export interface IPaymentVirtualAccountUserMap {
    id?:number

     accountNo?:string

     userCode?:string

    /**
     * 当可用应用为all时, 取payment_virtual_account_type_map中配置值,
     * 当不为all时,为payment_virtual_account_type_map中配置值的子集
     */
     applicationCode?:string

    createBy?:string

    gmtCreate?:string

    lastModifiedBy?:string

    gmtModified?:string

}

export interface ISendCodeCaptcha {

    accountNo?: string

    orderCode?: string
}

export interface IVirtualAuthorizedUserListRequest extends IPageRequest {

    applicationCode?: string

    accountNo?: string

    needPage?: boolean
}

export interface IVirtualAccountUser {

    id?: number

    accountNo?: string

    userCode?: string

    username?: string

    phone?: string

    gender?: number

    nickName?: string

    email?: string

    gmtCreate?: string

    applicationInfoList?: {
        applicationCode?: string
        applicationName?: string
    }

    remark?: string
}

export interface IConfigUserByApp {
    
    accountNo?: string

    applicationCode?: string

    userCodeList?: Array<string>
}