
type keys = 'CHARGE' | 'RECHARGE' | 'ALL' ;

/**
 * 虚拟账户应用作用域
 */
export const VirtualAppScopeConstant = {
  CHARGE: { "code": 1, "desc": "消费" },
  RECHARGE: { "code": 2, "desc": "充值" },
  ALL: { "code": 3, "desc": "消费+充值" },

  ofType: (code?: number): { "code": number, "desc": string } | null => {
    for (const key in VirtualAppScopeConstant) {
      const item = VirtualAppScopeConstant[key as keys];
      if (code === item.code) {
        return item;
      }
    }
    return null;
  }
}