import {HaierBudgetSourceConstant } from "./haierBudgetSourceConstant";


type keys = 'DEPT_BCC_1' | 'DEPT_BCC_2' | 'DEPT_GEMS_1' | 'DEPT_KEMS_1' | 'DEPT_RRS_1' | 'DEPT_RRS_2' | 'DEPT_HBC_1' | 'DEPT_HBC_2' | 'DEPT_XW';

/**
 * 预算类型
 */
export const HaierBudgetTypeConstant = {
  DEPT_BCC_1: { "code": "DEPT_BCC_1","budgetSysCode": HaierBudgetSourceConstant.BCC.code,  "name": "BCC部门预算1" },
  DEPT_BCC_2: { "code": "DEPT_BCC_2","budgetSysCode": HaierBudgetSourceConstant.BCC.code, "name": "BCC部门预算2" },
  DEPT_GEMS_1: { "code": "DEPT_GEMS_1","budgetSysCode": HaierBudgetSourceConstant.GEMS.code, "name": "gems部门预算" },
  DEPT_KEMS_1: { "code": "DEPT_KEMS_1","budgetSysCode":  HaierBudgetSourceConstant.KEMS.code, "name": "卡奥斯部门预算" },
  DEPT_KEMS_2: { "code": "DEPT_KEMS_2","budgetSysCode":  HaierBudgetSourceConstant.KEMS.code, "name": "卡奥斯部门预算" },
  DEPT_HSH_1: { "code": "DEPT_HSH_1","budgetSysCode":  HaierBudgetSourceConstant.HSH.code, "name": "智家部门预算" },
  DEPT_RRS_1: { "code": "DEPT_RRS_1","budgetSysCode":  HaierBudgetSourceConstant.RRSGEMS.code, "name": "日日顺部门预算" },
  DEPT_RRS_2: { "code": "DEPT_RRS_2","budgetSysCode":  HaierBudgetSourceConstant.RRSGEMS.code, "name": "日日顺部门预算2" },
  DEPT_HBC_1: { "code": "DEPT_HBC_1","budgetSysCode":  HaierBudgetSourceConstant.HBC.code, "name": "HBC部门预算1" },
  DEPT_HBC_2: { "code": "DEPT_HBC_2","budgetSysCode":  HaierBudgetSourceConstant.HBC.code, "name": "HBC部门预算2" },
  DEPT_XW: { "code": "DEPT_XW_FIN","budgetSysCode":  HaierBudgetSourceConstant.XW.code, "name": "小微预算" },
  DEPT_CZY_2: { "code": "DEPT_CZY_2","budgetSysCode":  HaierBudgetSourceConstant.CZY.code, "name": "财智云" },
  DEPT_CZY_1: { "code": "DEPT_CZY_1","budgetSysCode":  HaierBudgetSourceConstant.CZY.code, "name": "财智云" },
  ofCode: (code?: string): { "code": string, "budgetSysCode": string,"name": string } | null => {
    for (const key in HaierBudgetTypeConstant) {
      const item = HaierBudgetTypeConstant[key as keys];
      if (code === item.code) {
        return item;
      }
    }
    return null;
  }
}