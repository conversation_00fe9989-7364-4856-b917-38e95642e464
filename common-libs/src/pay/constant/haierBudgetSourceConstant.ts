

type keys = 'BCC' | 'GEMS' | 'KEMS' | 'RRSGEMS' | 'HBC';

/**
 * 预算类型
 */
export const HaierBudgetSourceConstant = {
  BCC: { "code": "BCC", "name": "BCC" },
  GEMS: { "code": "GEMS", "name": "GEMS" },
  KEMS: { "code": "KEMS", "name": "KEMS" },
  RRSGEMS: { "code": "RRSGEMS", "name": "RRSGE<PERSON>" },
  HBC: { "code": "HBC", "name": "HBC" },
  HSH: { "code": "HSH", "name": "HSH" },
  SCAN_CODE: { "code": "SCAN_CODE", "name": "SCAN_CODE" },
  XW: { "code": "XWFIN", "name": "XWFIN" },
  CZY: { "code": "CZY", "name": "CZY" },
  ofCode: (code?: string): { "code": string,"name": string } | null => {
    for (const key in HaierBudgetSourceConstant) {
      const item = HaierBudgetSourceConstant[key as keys];
      if (code === item.code) {
        return item;
      }
    }
    return null;
  }
}