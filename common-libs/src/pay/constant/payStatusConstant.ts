
type keys = 'CANCEL' | 'SAVE' | 'ADVANCE' | 'SUCCESS' | 'ERROR';

/**
 * 支付状态
 */
export const PayStatusConstant = {
  CANCEL: { "type": 0, "name": "取消" },
  SAVE: { "type": 10, "name": "保存" },
  ADVANCE: { "type": 20, "name": "预支付" },
  SUCCESS: { "type": 30, "name": "成功" },
  ERROR: { "type": 999, "name": "失败" },

  ofType: (type?: number): { "type": number, "name": string } | null => {
    for (const key in PayStatusConstant) {
      const item = PayStatusConstant[key as keys];
      if (type === item.type) {
        return item;
      }
    }
    return null;
  }
}