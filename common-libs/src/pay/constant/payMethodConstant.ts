
type keys = 'PAYMENT_CODE' | 'H5' | 'SCAN_CODE';

/**
 * 支付通知状态
 */
export const PayMethodConstant = {
  PAYMENT_CODE: { "code": 1, "name": "动态条码支付" },
  H5: { "code": 2, "name": "Web支付" },
  SCAN_CODE: { "code": 3, "name": "扫码支付" },

  ofType: (code?: number): { "code": number, "name": string } | null => {
    for (const key in PayMethodConstant) {
      const item = PayMethodConstant[key as keys];
      if (code === item.code) {
        return item;
      }
    }
    return null;
  }
}