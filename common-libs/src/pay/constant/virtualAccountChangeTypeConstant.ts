
type keys = 'PAY' | 'REFUND' | 'RECHARGE';

/**
 * 企业余额变动类型 1：消费；2：退款；3：充值；
 */
export const VirtualAccountChangeTypeConstant = {
  PAY: { "code": 1, "desc": "消费" },
  REFUND: { "code": 2, "desc": "退款" },
  RECHARGE: { "code": 3, "desc": "充值" },

  ofType: (code?: number): { "code": number, "desc": string } | null => {
    for (const key in VirtualAccountChangeTypeConstant) {
      const item = VirtualAccountChangeTypeConstant[key as keys];
      if (code === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray:() :({ code: number, desc: string } | undefined)[] => {
    const types = Object.keys(VirtualAccountChangeTypeConstant).map((i: string) => {
      if(i !== 'ofType' && i !== 'toArray' ) {
        return VirtualAccountChangeTypeConstant[i as keys]
      }
      return
    })
    const newTypes = types.filter(function (s) {
      return s && s; 
    })
    return newTypes
  }
}