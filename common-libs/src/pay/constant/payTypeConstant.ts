

type keys = 'BUDGET' | 'COMPOSITION' | 'OFFLINE' | 'WALLET' | 'VIRTUAL_ACCOUNT';

/**
 * 支付类型
 */
export const PayTypeConstant = {
  BUDGET: { "type": 1, "name": "企业预算" },
  COMPOSITION: { "type": 2, "name": "聚合支付" },
  OFFLINE: { "type": 3, "name": "线下支付" },
  WALLET: { "type": 4, "name": "超市福利积分" },
  VIRTUAL_ACCOUNT: { "type": 5, "name": "公司支付" },
  WTRAVEL_WALLET: { "type": 6, "name": "机票福利积分" },

  ofType: (type?: number): { "type": number, "name": string } | null => {
    for (const key in PayTypeConstant) {
      const item = PayTypeConstant[key as keys];
      if (type === item.type) {
        return item;
      }
    }
    return null;
  }
}