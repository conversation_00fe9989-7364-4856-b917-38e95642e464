
type keys = 'SZK' | 'CZ' | 'GZ';

/**
 * 虚拟账户类型
 */
export const VirtualAccountTypeConstant = {
  SZK: { "code": 1, "desc": "山庄卡" },
  CZ: { "code": 2, "desc": "充值账户" },
  GZ: { "code": 3, "desc": "挂账账户" },
  FL: { "code": 4, "desc": "个人福利账户" },
  TU:{ "code": 5, "desc": "工会账户" },

  ofType: (code?: number): { "code": number, "desc": string } | null => {
    for (const key in VirtualAccountTypeConstant) {
      const item = VirtualAccountTypeConstant[key as keys];
      if (code === item.code) {
        return item;
      }
    }
    return null;
  },

  toArray: (): ({ code: number, desc: string } | undefined)[] => {
    const types = Object.keys(VirtualAccountTypeConstant).map((i: string) => {
      if (i !== 'ofType' && i !== 'toArray') {
        return VirtualAccountTypeConstant[i as keys]
      }
      return
    })
    const newTypes = types.filter(function (s) {
      return s && s;
    })
    return newTypes
  }
}