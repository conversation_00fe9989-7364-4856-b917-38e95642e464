

type keys = 'MOBILE' | 'PC' ;

/**
 * 支付来源类型
 */
export const PaySourceConstant = {
  PC: { "type": 1, "name": "PC" },
  MOBILE: { "type": 2, "name": "MOBILE" },


  ofType: (type?: number): { "type": number, "name": string } | null => {
    for (const key in PaySourceConstant) {
      const item = PaySourceConstant[key as keys];
      if (type === item.type) {
        return item;
      }
    }
    return null;
  }
}