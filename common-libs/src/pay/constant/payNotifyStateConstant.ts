
type keys = 'SUCCESS' | 'RETRY' | 'ERROR';

/**
 * 支付通知状态
 */
export const PayNotifyStateConstant = {
  SUCCESS: { "code": 0, "name": "成功" },
  RETRY: { "code": 1, "name": "重试" },
  ERROR: { "code": 2, "name": "失败" },


  ofType: (code?: number): { "code": number, "name": string } | null => {
    for (const key in PayNotifyStateConstant) {
      const item = PayNotifyStateConstant[key as keys];
      if (code === item.code) {
        return item;
      }
    }
    return null;
  }
}