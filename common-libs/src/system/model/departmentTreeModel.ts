export interface IDepartmentTreeReq {
  code?: string;
  name?: string;
  resource?: number;
  parentCode?: string;
  codePath?: string;
  enterpriseCode?: string;
  managerCode?: string;
  managerName?: string;
  pageNum?: string;
  pageSize?: string;

}

export interface IDepartmentTreeRes {
  /* */
  id: number;

  /* */
  code: string;

  /* */
  name: string;

  /* */
  resource: number;

  /* */
  parentCode: string;

  /* */
  codePath: string;

  /* */
  enterpriseCode: string;

  /* */
  managerCode: string;

  /* */
  managerName: string;

  /* */
  managerPhone: string;

  /* */
  managerMail: string;

  /* */
  state: number;

  /* */
  gmtCreate: Record<string, unknown>;

  /* */
  createBy: string;

  /* */
  gmtModified: Record<string, unknown>;

  /* */
  lastModifiedBy: string;

  /* */
  deleted: number;

  /* */
  departmentLevel: number;
}[];