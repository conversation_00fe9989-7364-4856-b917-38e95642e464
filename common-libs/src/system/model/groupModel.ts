export interface IGroupInfo {
     id?:number
     name?:string
     description?:string
     gmtCreate?:string
     createBy?:string
     gmtModified?:string
     lastModifiedBy?:string
}

export interface IGroupListRequest {
     pageNum?:number
     pageSize?:number
     name?:string
}

export interface IGroupSaveUpdateRequest {
     id?:number
     name?:string
     description?:string
}


export interface IGroupLinkRoleRequest {
     groupId?:number
     roleIds?:number[]
}

export interface IGroupLinkUserRequest {
     groupId?:number
     userId:number
     username:string
}

export interface IGroupDeleteRequest {
     id?:number
}