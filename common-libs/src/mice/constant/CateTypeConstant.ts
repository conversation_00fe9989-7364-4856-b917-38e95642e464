type keys = 'BUFFET' | 'TABLE' | 'DINING_OUT' | 'OTHER';


export const CateTypeConstant = {
    BUFFET: { code: 1, desc: '自助' },
    TABLE: { code: 2, desc: '桌餐' },
    DINING_OUT: { code: 3, desc: '外出' },
    OTHER: { code: 4, desc: '其他' },

    ofType: (type?: number): { code: number, desc: string } | null => {
      for (const key in CateTypeConstant) {
        const item = CateTypeConstant[key as keys];
        if (type === item.code) {
          return item;
        }
      }
      return null;
    },
  
    toArray:() :({ code: number, desc: string } | undefined)[] => {
      const types = Object.keys(CateTypeConstant).map((i: string) => {
        if(i !== 'ofType' && i !== 'toArray') {
          return CateTypeConstant[i as keys]
        }
        return
      })
      const newTypes = types.filter(function (s) {
        return s && s; 
      })
      return newTypes
    },
}
