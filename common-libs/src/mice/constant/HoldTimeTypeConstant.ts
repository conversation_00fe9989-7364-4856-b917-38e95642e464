type keys = 'ALL_DAY' | 'FORENOON' | 'AFTERNOON' | 'EVENING';


export const HoldTimeTypeConstant = {
    ALL_DAY: { code: 0, desc: '全天' },
    FORENOON: { code: 1, desc: '上午' },
    AFTERNOON: { code: 2, desc: '下午' },
    EVENING: { code: 3, desc: '晚上' },

    ofType: (type?: number): { code: number, desc: string } | null => {
        for (const key in HoldTimeTypeConstant) {
          const item = HoldTimeTypeConstant[key as keys];
          if (type === item.code) {
            return item;
          }
        }
        return null;
      },
  
      toArray:() :({ code: number, desc: string } | undefined)[] => {
        const types = Object.keys(HoldTimeTypeConstant).map((i: string) => {
          if(i !== 'ofType' && i !== 'toArray') {
            return HoldTimeTypeConstant[i as keys]
          }
          return
        })
        const newTypes = types.filter(function (s) {
          return s && s; 
        })
        return newTypes
      },
}
