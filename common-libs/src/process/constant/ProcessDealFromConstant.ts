type keys = 'PC' | 'MOBILE' | 'IHAIER' ;

export const ProcessDealFromConstant = {
    PC: { "type": 0, "name": "pc端" },
    MOBILE: { "type": 1, "name": "手机端" },
    IHAIER: { "type": 2, "name": "i<PERSON>er卡片" },

    ofType: (type?: number): { "type": number, "name": string } | null => {
        for (const key in ProcessDealFromConstant) {
          const item = ProcessDealFromConstant[key as keys];
          if (type === item.type) {
            return item;
          }
        }
        return null;
      },
  
      toArray:() :({ type: number, name: string } | undefined)[] => {
          const types = Object.keys(ProcessDealFromConstant).map((i: string) => {
            if(i !== 'ofType' && i !== 'toArray' ) {
              return ProcessDealFromConstant[i as keys]
            }
            return
          })
          const newTypes = types.filter(function (s) {
            return s && s; 
          })
          return newTypes
        }
}