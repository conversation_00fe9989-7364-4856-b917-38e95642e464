import { IPageRequest } from "../../basic";


export type TokenRequest = {
    username: string;
    password: string;
};

export type ErrorResponse = {
    code: number;
    message: string;
};

export type ListData<T> = {
    total: number;
    list: Array<T>;
};

export type ListResult<T> = {
    code: number;
    data: ListData<T>;
    msg: string;
};

// export interface Result {
//     code: number;
//     data: any;
//     msg: string;
// }

export interface SSOResult {
    resultCode: string;
    resultMsg: string;
    result: string;
    data: any;
}

export interface Searchable<T> {
    list(filter: object): Promise<ListResult<T>>;
    download?(filter: any): any;
}

// export interface Editable<R> {
//     create(request: R): Promise<Result>;

//     edit(request: R): Promise<Result>;
// }

// export interface Deleteable {
//     remove(id: number): Promise<Result>;
// }

export interface Detailable {
    detail(id: number): Promise<any>;
}

//banner相关type
export interface BannerFilter extends IPageRequest {
    adSubject?: string | null;
    showStatus: number | null;
    createTime: string[] | null;
}

export interface BannerEditRequest {
    id: number | null;
    imgUrl: string;
    jumpLinkApp: string;
    jumpLinkPc: string;
    showOrder: number;
    showStatus: number;
    adSubject: string;
}

export interface BannerType {
    id: number | null;
    creator: string;
    createTime: string;
    updater: string;
    updateTime: string;
    imgUrl: string;
    jumpLinkApp: string;
    jumpLinkPc: string;
    showOrder: number;
    showStatus: number;
    adSubject: string;
}

//Life 相关，由模板生成，须自行添加相应的类型
export interface LifeFilter extends IPageRequest {
    adSubject?: string | null;
    showStatus?: number | null;
    createTime?: string[] | null;
}

export interface LifeEditRequest {
    id: number | null;
    imgUrl: string;
    jumpLinkApp: string;
    jumpLinkPc: string;
    showOrder: number;
    showStatus: number;
    adSubject: string;
}

export interface LifeType {
    id: number | null;
    creator: string;
    createTime: string;
    updater: string;
    updateTime: string;
    imgUrl: string;
    jumpLinkApp: string;
    jumpLinkPc: string;
    showOrder: number;
    showStatus: number;
    adSubject: string;
}

//Application 相关，由模板生成，须自行添加相应的类型
export interface ApplicationFilter extends IPageRequest {
    menuName?: string | null;
    showStatus: number | null;
    createTime: string[] | null;
}

export interface ApplicationEditRequest {
    id: number | null;
    iconUrl: string;
    jumpLinkApp: string | null;
    jumpLinkPc: string | null;
    showOrder: number;
    showStatus: number;
    menuName: string;
}

export interface ApplicationType {
    id: number | null;
    creator: string;
    createTime: string;
    updater: string;
    updateTime: string;
    iconUrl: string;
    jumpLinkApp: string | null;
    jumpLinkPc: string | null;
    showOrder: number;
    showStatus: number;
    defaultShowStatus: number;
    menuName: string;
}

export interface AssignUserMenuType {
    userId: string
    userName: string
    menuIds: number[]
    client: string
}
//Notice 相关，由模板生成，须自行添加相应的类型
export interface NoticeFilter extends IPageRequest {
    ancmTitle: string | null;
    ancmAuthor: string | null;
    createTime: string[] | null;
    showStatus: number | null;
    ancmDate: string[] | null;
}

export interface CardNoticeFilter {
    pageNo: number;
    pageSize: number;
    showStatus: number;
}

export interface NoticeEditRequest {
    id: number | null;
    showStatus: number;
    imgUrl: string;
    ancmTitle: string;
    ancmAuthor: string;
    ancmDate: string;
    ancmContent: string;
}

export interface NoticeType {
    id: number | null;
    creator: string;
    createTime: string;
    showStatus: number;
    imgUrl: string;
    ancmTitle: string;
    ancmAuthor: string;
    ancmDate: string;
    ancmContent: string;
}
//Information 相关，由模板生成，须自行添加相应的类型
export interface InformationFilter extends IPageRequest {
    createTime: string[] | null;
    infoTitle: string | null;
    infoAuthor: string | null;
    showStatus: number | null;
    infoDate: string[] | null;
}

export interface CardInformationFilter {
    pageNo: number;
    pageSize: number;
    showStatus: number;
}

export interface InformationEditRequest {
    id: number | null;
    showStatus: number;
    imgUrl: string;
    infoAuthor: string;
    infoDate: string;
    infoTitle: string;
    infoContent: string;
}

//部门
export interface DeptType {
    id: number | null;
    name: string;
    parentId: number;
    sort: string;
    leaderUserId: number;
    phone: string;
    email: string;
    status: string;
    createTime: string;
}

export interface DeptEditRequest {
    id: number | null;
    name: string;
    parentId: number;
    sort: string;
    leaderUserId: number | null;
    phone: string | null;
    email: string;
    status: string | null;
}

export interface DeptFilter {
    name?: string;
    status?: string;
}

//User 相关，由模板生成，须自行添加相应的类型
export interface UserFilter extends IPageRequest {
    createTime: string[] | null;
}

export interface UserEditRequest {
    id: number | null;
    showStatus: number;
}

export interface UserType {
    id: number;
    username: string;
    nickname: string;
    deptId: number;
    postIds: string[];
    email: string;
    mobile: string;
    sex: number;
    avatar: string;
    loginIp: string;
    status: number;
    remark: string;
    loginDate: string;
    createTime: string;
}
//Role 相关，由模板生成，须自行添加相应的类型
export interface RoleFilter extends IPageRequest {
    createTime: string[] | null;
}

export interface RoleEditRequest {
    id: number | null;
    name: string;
    code: string;
    sort: number;
    remark: string;
    status: number;
}

export interface RoleType {
    id: number | null;
    creator: string;
    createTime: string;
    status: number;
    name: string;
    code: string;
    sort: number;
    remark: string;
    type: number;
    deleted: string;
}
//Menu 相关，由模板生成，须自行添加相应的类型
export interface MenuFilter extends IPageRequest {
    createTime: string[] | null;
}

export interface MenuEditRequest {
    id: number | null;
    name: string;
    permission: string;
    type: number;
    sort: string;
    parentId: number;
    path: string;
    icon: string;
    component: string;
    visible: number;
    keepAlive: string;
    status: number;
    noTagsView: number;
}

export interface MenuType {
    id: number;
    name: string;
    permission: string;
    type: number;
    sort: string;
    parentId: number;
    path: string;
    icon: string;
    component: string;
    visible: number;
    keepAlive: string;
    status: number;
    createTime: string;
}

//Order 相关，由模板生成，须自行添加相应的类型
export interface OrderFilter extends IPageRequest {
    createTime: string[] | null;
}

export interface OrderEditRequest {
    id: number | null;
    listUrl: string;
    jumpUrl: string;
    detailUrl: string;
    name: string;
    showStatus: number;
    showOrder: number;
}

export interface OrderType {
    id: number | null;
    listUrl: string;
    jumpUrl: string;
    detailUrl: string;
    name: string;
    showStatus: number;
    showOrder: number;
}
//Report 相关，由模板生成，须自行添加相应的类型
export interface ReportFilter extends IPageRequest {
    [key: string]: any;
    datartParams: {
        aggregators: any[];
        viewId: string | null;
        orders?: any;
        functionColumns?: any;
        defaultFilters?: any;
        groups?: any;
        type?: string,
        moduleType?: number
    };
}

export interface ReportEditRequest {
    id: number | null;
    showStatus: number;
}

export interface ReportType {
    id: number | null;
    creator: string;
    createTime: string;
    status: number;
    imgUrl: string;
}

//Permission 相关，由模板生成，须自行添加相应的类型
export interface PermissionFilter extends IPageRequest {
    createTime: string[] | null;
}

export interface PermissionEditRequest {
    id: number | null;
    showStatus: number;
}

export interface PermissionType {
    id: number | null;
    employeeId: string;
    createTime: string;
    status: string;
    businessType: string;
    columns: string[];
    account_company_code: string;
    permissionString?: string;
}
//Apply 相关，由模板生成，须自行添加相应的类型
export interface ApplyFilter extends IPageRequest {
    createTime: string[] | null;
}

export interface ApplyEditRequest {
    id: number | null;
    showStatus: number;
}

export interface ApplyType {
    approveReason: string;
    businessType: string;
    account_company_code: string[] | string;
    account_company_name: string[] | string;
    permissionString?: string;
    budgetPeopleEmpId?: string | number;
    budgetPeopleEmpName?: string
    // businessDataTime: string[];
    // businessDataTimeStart?: string;
    // businessDataTimeEnd?: string;
    permissionValidTime: string[];
    permissionValidTimeStart?: string | number;
    permissionValidTimeEnd?: string | number;
    employeeId?: string | number;
    createTime?: any;
    permissionStatus?: string;
    employeeName?: string;
    approveStatus?: number;
    approveRemark?: string;
}

export interface ApplyCompanyType {
    code: number | string;
    name: string;
}
