import { IMonthPlanDO, IMonthPlanDetailItemResponseDTO } from "../annual/plan/plan";
import { IEvaluateListResponseDTO } from "../evaluate/evaluate";

export class IMonthPlanListRequestDTO {
    /**
     * 年度计划code
     */
    apCode?: string

    /**
     * 年度计划主表版本
     */
    apVer?: number

    /**
     * 所属年份，每年只有一条数据
     */
    year?: number

    /**
     * 所属月份，各小微每年一条
     */
    month?: number

    /**
     * 部门code
     */
    deptCode?: string

    /**
     * 部门名称
     */
    deptName?: string

    /**
     * {@link MonthPlanItemStateConstant}
     */
    state?: number
}

export class IMonthPlantDTO {

    apiName?: string
    deptName?: string 
    year?: number
    month?: number
    principalUsername?: String
    planValue?: string
    planUnit?: string
    completePlanValue?: string
    completePlanDesc?: string 
    completeRate?: string
    completePlanTime?: string
}

export class IMonthPlanListResponseDTO extends IMonthPlanDO {

}

export class IMonthHolidayRequestDTO {
    year?: number
    month?: number
}

export class ICalendarAPIResponse {

    /**
     * ": "兔",
     */
    animal?: string

    /**
     * ": "祈福.安床.祭祀.谢土.造庙",
     */
    avoid?: string

    /**
     * ": "五",
     */
    cnDay?: string

    /**
     * ": "2",
     */
    day?: number

    /**
     * ": "北小年",
     */
    festivalList?: string

    /**
     * ": "丙申",
     */
    gzDate?: string

    /**
     * ": "乙丑",
     */
    gzMonth?: string

    /**
     * ": "癸卯",
     */
    gzYear?: string

    /**
     * ": "1",
     */
    isBigMonth?: string

    /**
     * ": "廿三",
     */
    lDate?: string

    /**
     * ": "腊",
     */
    lMonth?: string

    /**
     * ": "23",
     */
    lunarDate?: string

    /**
     * ": "12",
     */
    lunarMonth?: string

    /**
     * ": "2023",
     */
    lunarYear?: string

    /**
     * ": "2",
     */
    month?: number

    /**
     * ": "2024-02-01T16:00:00Z", 0 时区
     */
    oDate?: string


    /**
     * ": "结婚.搬家.合婚订婚.签订合同.交易.搬新房.纳财.开业.买衣服.订盟.动土.栽种.盖屋.安葬.安门.牧养.修造.起基.入殓.安香.成服.出火.除服.掘井.开池.开光.立碑.破土.打猎",
     */
    suit?: string

    /**
     * ": "北小年",
     */
    term?: string

    /**
     * ": "1706803200",
     */
    timestamp?: string

    /**
     * ": "t",
     * 节假日类型
     * i t a h
     */
    type?: string

    /**
     * 节假日状态（1-休假，2-补班）
     */
    status?: string

    /**
     * 1: 休息 2: 工作
     */
    workState?: number

    /**
     * ": "2024",
     */
    year?: number

    /**
     * ": "https://mobile.51wnl-cq.com/huangli_tab_h5/?posId=BDSS&STIME=2024-02-02",
     */
    yjJumpUrl?: string

    /**
     * ": "51wnl"
     * 数据来源
     */
    yjFrom?: string


    /**
     * 节假日信息
     */
    festivalInfoList?: ICalendarAPIFestivalInfoResponse[]

}

export class ICalendarAPIFestivalInfoResponse {
    /**
     * 9280829",
     */
    baikeId?: string

    /**
     * 小年",
     */
    baikeName?: string

    /**
     * https://baike.baidu.com/item/小年/9280829",
     */
    baikeUrl?: string

    /**
     * 北小年"
     */
    name?: string
}

export class IMonthPlanDetailRequestDTO {
    /**
     * 月度计划id
     */
    id?: number;

    code?: string;
    year?: number;
    month?: number;

    /**
     * 评价控制
     *  {@link EvaluateControlConstant}
     */
    evaluateControl?: number;
}

export class IMonthPlanDetailResponseDTO extends IMonthPlanDO {
    /**
     * 月度项目
     */
    monthPlanItems?: IMonthPlanDetailItemResponseDTO[]

    /**
     * 月度项目, 月份kv结构
     */
    monthPlanEnterItems?: Map<number, IMonthPlanDetailItemResponseDTO[]>

    /**
     * 月度评价
     */
    evaluate?: IEvaluateListResponseDTO[]

}