import { IEvaluateDO } from "../evaluate/evaluate"

export class IDailyReportListRequestDTO {
    /**年度计划code */
    apCode?: string

    /**月度计划code */
    mpCode?: string

    /**年 */
    year?: number

    /**月 */
    month?: number

    /**日 */
    day?: number

    /**部门code */
    deptCode?: string

    /**部门名称 */
    deptName?: string

    /**提交人工号 */
    submitUsercode?: string

    /**提交人姓名 */
    submitUsername?: string

    /**提交人部门code */
    submitDeptCode?: string

    /**提交人部门名称 */
    submitDeptName?: string

    /**
     * 主表状态
     * {@link com.haierbusiness.daily.enums.DailyReportStateEnum}
     */
    /**状态 */
    state?: string

}

export class IDailyReportDO {

    id?: number

    /**
     * 年度计划主表code
     */
    apCode?: string

    /**
     * 月度计划code
     */
    mpCode?: string

    /**
     * 所属年份
     */
    year?: number

    month?: number

    day?: number

    /**
     * 部门code
     */
    deptCode?: string

    /**
     * 部门名称
     */
    deptName?: string

    /**
     * 日决议数
     */
    conferenceCount?: number

    /**
     * 目标总数
     */
    itemCount?: number

    /**
     * 不需要录入数量
     */
    nonItemCount?: number

    /**
     * 已录入数量
     */
    completeItemCount?: number

    /**
     * 日激励数
     */
    dailyEvaluateTotal?: number

    /**
     * 平台评价金额
     */
    evaluateAmount?: number

    /**
     * 平台评价原因
     */
    evaluateRemark?: string

    /**
     * 平台评价时间
     */
    evaluateTime?: string

    /**
     * 提交人
     */
    submitUsercode?: string

    submitUsername?: string

    submitDeptCode?: string

    submitDeptName?: string

    /**
     * 提交时间
     */
    submitTime?: string

    /**
     * 主表状态
     * {@link com.haierbusiness.daily.enums.DailyReportStateEnum}
     */
    state?: number

    stateName?: string
}

export class IDailyReportListResponseDTO extends IDailyReportDO {

}

export class IDailyReportDetailRequestDTO {
    /** 日清主表id*/
    id?: number
}

export class IDailyReportProjectDO {


    id?: number

    /**
     * 年度计划主表code
     */
    apCode?: string

    /**
     * 年度项目表code
     */
    apiCode?: string

    /**
     * 月度计划code
     */
    mpCode?: string

    /**
     * 日id
     */
    drId?: string

    /**
     * 月度项目code
     */
    mpiCode?: string

    /**
     * 所属年份
     */
    year?: number

    month?: number

    day?: number

    /**
     * 部门code
     */
    deptCode?: string

    /**
     * 部门名称
     */
    deptName?: string

    /**
     * 操作人
     */
    principalUsercode?: string

    /**
     * 操作人部门
     */
    principalUsername?: string

    principalDeptCode?: string

    principalDeptName?: string

    /**
     * 录入时间
     */
    operationTime?: string

    /**
     * 日推进效果
     */
    content?: string


}

export class IDailyReportConferenceDO {

    id?: number

    /**
     * 年度计划主表code
     */
    apCode?: string

    /**
     * 月度计划code
     */
    mpCode?: string

    /**
     * 日id
     */
    drId?: string

    /**
     * 所属年份，每年只有一条数据
     */
    year?: number

    month?: number

    day?: number

    /**
     * 部门code
     */
    deptCode?: string

    /**
     * 部门名称
     */
    deptName?: string

    /**
     * 责任人
     */
    principalUsercode?: string

    principalUsername?: string

    /**
     * 责任人部门
     */
    principalDeptCode?: string

    principalDeptName?: string


    /**
     * 时间节点
     */
    executionTime?: string

    /**
     * 达成目标及推进要求
     */
    content?: string

}

export class IDailyReportQuantifierIssueDO {


    id?: number

    /**
     * 代办id
     */
    todoId?: string

    /**
     * 年度计划主表code
     */
    apCode?: string
    mpCode?: string

    /**
     * 日id
     */
    drId?: string

    /**
     * 所属年份
     */
    year?: number

    month?: number

    day?: number

    /**
     * 部门code
     */
    deptCode?: string

    /**
     * 部门名称
     */
    deptName?: string

    /**
     * 量化人
     */
    quantifierUsercode?: string

    quantifierUsername?: string

    /**
     * 量化人部门
     */
    quantifierDeptCode?: string

    quantifierDeptName?: string

    /**
     * 量化人状态
     * {@link com.haierbusiness.daily.enums.QuantifierStateEnum}
     */
    quantifierState?: number

    /**
     * 不录入原因
     */
    noQuantifierReason?: string

    /**
     * 最小录入数
     */
    minIssueCount?: string

    /**
     * 日量化问题已录入数
     */
    issueCount?: string
}

export class DailyReportQuantifierIssueItemDO {

    id?: number

    /**
     * 年度计划主表id
     */
    apCode?: string
    mpCode?: string

    /**
     * 日id
     */
    drId?: number

    /**
     * 日量化主表id
     */
    drqiId?: number

    /**
     * 所属年份
     */
    year?: number

    month?: number

    day?: number

    /**
     * 部门code
     */
    deptCode?: string

    /**
     * 部门名称
     */
    deptName?: string

    /**
     * 量化人
     */
    quantifierUsercode?: string

    quantifierUsername?: string

    quantifierDeptCode?: string

    quantifierDeptName?: string

    /**
     * 日量化问题
     */
    issue?: string

    /**
     * 处理方案
     */
    dispose?: string
    disposeDate?: string

    /**
     * 责任人
     */
    principalUsercode?: string

    principalUsername?: string

    /**
     * 责任人部门
     */
    principalDeptCode?: string

    /**
     * 责任人部门
     */
    principalDeptName?: string

    /**
     * 咬合人
     */
    managerUsercode?: string

    managerUsername?: string

    /**
     * 咬合人部门
     */
    managerDeptCode?: string

    /**
     * 咬合人部门
     */
    managerDeptName?: string
}

export class IDailyReportQuantifierIssueResponseDTO extends IDailyReportQuantifierIssueDO {
    dailyReportQuantifierIssueItems?: DailyReportQuantifierIssueItemDO[]
}

export class IDailyReportEvaluateDO {

    id?: number

    /**
     * 年度计划主表code
     */
    apCode?: string

    /**
     * 月度计划id
     */
    mpCode?: string

    /**
     * 日id
     */
    drId?: string

    /**
     * 责任人
     */
    principalUsercode?: string

    principalUsername?: string

    principalDeptCode?: string

    principalDeptName?: string

    /**
     * 所属年份
     */
    year?: number

    month?: number

    day?: number

    /**
     * 部门code
     */
    deptCode?: string

    /**
     * 部门名称
     */
    deptName?: string


    /**
     * 录入时间
     */
    evaluateTime?: string

    /**
     * 评价原因
     */
    evaluateRemark?: string

    evaluateAmount?: number

    /**
     * 关差措施
     */
    dispose?: string

    /**
     * 关差时间
     */
    disposeTime?: string

    /**
     * 日清评价状态
     * {@link com.haierbusiness.daily.enums.DailyReportEvaluateStateEnum}
     */
    state?: number

    stateName?: string

}

export class IDailyReportDetailResponseDTO extends IDailyReportDO {
    /**
     * 月度预算项目
     */
    dailyReportProjects?: IDailyReportProjectDO[]

    /**
     * 日清会
     */
    dailyReportConferences?: IDailyReportConferenceDO[]

    /**
     * 日量化问题
     */
    dailyReportQuantifierIssues?: IDailyReportQuantifierIssueResponseDTO[]

    /**
     * 日清评价
     */
    dailyReportEvaluates?: IDailyReportEvaluateDO[]
    
    /**
     * 平台评价
     */
    platformEvaluate?: IEvaluateDO
    
}

export class IDailyReportSaveRequestDTO {
    /**
     * 日清主表id
     */
    id?: number


    /**
     * 是否触发下一节点，值: 1: 保存 ，2: 提交
     */
    action?: number


    /**
     * 月度预算项目
     */
    dailyReportProjects?: IDailyReportProjectRequestDTO[]

    /**
     * 日清会
     */
    dailyReportConferences?: IDailyReportConferenceRequestDTO[]

    /**
     * 日量化问题
     */
    dailyReportQuantifierIssueItems?: IDailyReportQuantifierIssueItemRequestDTO[]

    /**
     * 日清评价
     */
    dailyReportEvaluates?: IDailyReportEvaluateDTO[]

}


export class IDailyReportProjectRequestDTO {

    /**
     * 记录id，传递则为修改
     */
    id?: number

    /**
     * 删除标记
     */
    isDeleted?: number

    /**
     * 年度项目表code
     */
    apiCode?: string

    /**
     * 月度项目code
     */
    mpiCode?: string


    /**
     * 日推进效果
     */
    content?: string

    /**
     * 责任人工号
     */
    principalUsercode?: string

    /**
     * 责任人姓名
     */
    principalUsername?: string

    /**
     * 责任人部门code
     */
    principalDeptCode?: string

    /**
     * 责任人部门名称
     */
    principalDeptName?: string
}


export class IDailyReportConferenceRequestDTO {

    /**
     * 记录id，传递则为修改
     */
    id?: number

    /**
     * 删除标记
     */
    isDeleted?: number

    /**
     * 时间节点
     */
    executionTime?: string

    /**
     * 达成目标及推进要求
     */
    content?: string

    /**
     * 责任人工号
     */
    principalUsercode?: string

    /**
     * 责任人姓名
     */
    principalUsername?: string

    /**
     * 责任人部门code
     */
    principalDeptCode?: string

    /**
     * 责任人部门名称
     */
    principalDeptName?: string
}


export class IDailyReportQuantifierIssueItemRequestDTO {

    /**
     * 记录id，传递则为修改
     */
    id?: number

    /**
     * 删除标记
     */
    isDeleted?: number

    /**
     * 日量化问题主表id
     */
    drqiId?: number

    /**
     * 日量化人员状态,
     * {@link com.haierbusiness.daily.enums.QuantifierStateEnum}
     */
    state?: number

    /**
     * 不录入原因
     */
    reason?: string

    /**
     * 日量化问题
     */
    issue?: string

    /**
     * 处理方案
     */
    dispose?: string

    /**
    * 量化人
    */
    quantifierUsercode?: string

    quantifierUsername?: string

    /**
     * 量化人部门
     */
    quantifierDeptCode?: string

    quantifierDeptName?: string
    /**
     * 责任人工号
     */
    principalUsercode?: string

    /**
     * 责任人姓名
     */
    principalUsername?: string

    /**
     * 责任人部门code
     */
    principalDeptCode?: string

    /**
     * 责任人部门名称
     */
    principalDeptName?: string

    /**
     * 关差时间
     */
    disposeDate?: string

    /**
     * 咬合人工号
     */
    managerUsercode?: string

    /**
     * 咬合人姓名
     */
    managerUsername?: string

    /**
     * 咬合人部门code
     */
    managerDeptCode?: string

    /**
     * 咬合人部门名称
     */
    managerDeptName?: string
}


export class IDailyReportEvaluateDTO {

    /**
     * 记录id，传递则为修改
     */
    id?: number

    /**
     * 删除标记
     */
    isDeleted?: number
    
    deptCode?: string
    deptName?: string

    /**
     * 责任人工号
     */
    principalUsercode?: string

    /**
     * 责任人姓名
     */
    principalUsername?: string

    /**
     * 责任人部门code
     */
    principalDeptCode?: string

    /**
     * 责任人部门名称
     */
    principalDeptName?: string

    /**
     * 评价原因
     */
    evaluateRemark?: string

    /**
     * 评价金额
     */
    evaluateAmount?: number

    /**
     * 关差措施
     */
    dispose?: string

    /**
     * 关差时间
     */
    disposeTime?: string

    /**
     * 日清评价状态
     * {@link com.haierbusiness.daily.enums.DailyReportEvaluateStateEnum}
     */
    state?: number
}

export class IDailyReportPlatformEvaluateDTO {
    /**
     * 日清主表id
     */
    id?: number

    /**
     * 平台评价金额
     */
    evaluateAmount?: number

    /**
     * 平台评价原因
     */
    evaluateRemark?: string
}

export class IDailyReportBatchPlatformEvaluateDTO {


    /**
     * 年
     */
    year?: number

    /**
     * 月
     */
    month?: number

    /**
     * 日
     */
    day?: number
    /**
     * 评价内容
     */
    content?: string
}