type keys =
    "PLATFORM_DAILY"
    | "THREE_STRATEGY"
    | "THREE_FINANCE"
    | "THREE_CONTROL"
    | "THREE_MANPOWER"
    | "MICRO_DAILY"
    | "MICRO_MONTH"
    | "PLATFORM_MONTH";

/**
 * 评价类型
 */
export const EvaluateTypeConstant = {

    PLATFORM_DAILY: {"code": 10, "desc": "平台日评价"},
    THREE_STRATEGY: {"code": 21, "desc": "三自评价-战略"},
    THREE_FINANCE: {"code": 22, "desc": "三自评价-财务"},
    THREE_CONTROL: {"code": 23, "desc": "三自评价-风控"},
    THREE_MANPOWER: {"code": 24, "desc": "三自评价-人力"},
    MICRO_DAILY: {"code": 30, "desc": "小微日评价"},
    MICRO_MONTH: {"code": 40, "desc": "小微月评价"},
    PLATFORM_MONTH: {"code": 50, "desc": "平台月评价"},

    ofCode: (code?: number): { "code": number, "desc": string } | null => {
        for (const key in EvaluateTypeConstant) {
            const item = EvaluateTypeConstant[key as keys];
            if (code === item.code) {
                return item;
            }
        }
        return null;
    }
}