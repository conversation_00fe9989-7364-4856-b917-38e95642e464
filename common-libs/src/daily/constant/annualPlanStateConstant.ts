

type keys = "WAIT" | "REJECT"| "APPROVAL"| "WAIT_RUNNING"| "RUNNING"| "COMPLETED"| "INCOMPLETE";

/**
 * 年度计划类型
 */
export const AnnualPlanStateConstant = {
    /**
     * 待提交 (草稿状态）
     */
    WAIT: { "code": 10, "desc": "待提交" },

    /**
     * 已驳回 （草稿状态）
     */
    REJECT: { "code": 11, "desc": "已驳回" },

    /**
     * 审批中 （提交后）
     */
    APPROVAL: { "code": 20, "desc": "审批中" },

    /**
     * 等待执行（审批通过后，未到开始日期）
     */
    WAIT_RUNNING: { "code": 30, "desc": "计划执行中" },

    /**
     * 计划执行中（审批通过后）
     */
    RUNNING: { "code": 40, "desc": "计划待执行" },

    /**
     * 目标已完成 （次年，子项目全部达成）
     */
    COMPLETED: { "code": 50, "desc": "目标已完成" },

    /**
     * 目标未完成（次年，子项目未达成）
     */
    INCOMPLETE: { "code": 60, "desc": "目标未完成" },


    ofCode: (code?: number): { "code": number, "desc": string } | null => {
        for (const key in AnnualPlanStateConstant) {
            const item = AnnualPlanStateConstant[key as keys];
            if (code === item.code) {
                return item;
            }
        }
        return null;
    }
}