

type keys = "QUANTIFY" | "QUALITATIVE";

/**
 * 项目类型
 */
export const PlanTypeConstant = {
    QUANTIFY: { "code": 1, "desc": "定量" },
    QUALITATIVE: { "code": 2, "desc": "定性" },

    ofCode: (code?: number): { "code": number, "desc": string } | null => {
        for (const key in PlanTypeConstant) {
            const item = PlanTypeConstant[key as keys];
            if (code === item.code) {
                return item;
            }
        }
        return null;
    },
    toArray: (): ({ code: number, desc: string } | undefined)[] => {
        const types = Object.keys(PlanTypeConstant).map((i: string) => {
            if (i !== 'ofCode' && i !== 'toArray') {
                return PlanTypeConstant[i as keys]
            }
            return
        })
        const newTypes = types.filter(function (s) {
            return s && s;
        })
        return newTypes
    }
}