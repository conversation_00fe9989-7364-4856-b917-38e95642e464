import { IUserInfo,ITraveler } from '@haierbusiness-front/common-libs';
export interface ICity {
  city?: string;
  date: string;
  active?: boolean;
  syId?: string;
  cityCode: string;
}

// 基本信息
export interface ITripInfo {
  outPersonId?: Array<string>, // 外部出差人id
  inPerson?: Array<IUserInfo>, // 出差人列表
  outPerson?: Array<IUserInfo>, // 外部出行人
  travelReserveFlag?: number, // 是否是商旅
  travelReason?: string, // 出差原因
  name?: string,
}

/*
  行程列表


  [
    dataSource: [
      {
        person: []// 出差人
      }
    ]
  ]
*/
// 出差人
export interface IPerson {
  name?: string;
  id?: string;
}
export interface sourceItem {
  key?: number;
  persons?: Array<IPerson>;
  name?: string;
  money?: string;
  result?: string;
  insure?: boolean;
}

// 出行方式
export interface IProduct {
  productCode?: string;
  productName?: string;
}

export interface IStepData {
  name?: string,
  timer?: string,
  isTrain?: boolean;
  isFly?: boolean;
  isHotal?: boolean;
}

export interface IDataListItem {
  stepData?:Array<IStepData>;
  reserve?: number;
  schedule?: boolean;
  moreListShow?: boolean;
  isMouseInter?: boolean;
  auditStatus: string;
  status: string;
  reimburseNum?:number;
  changeStatus:string;
  travelerList?: Array<ITraveler>
}