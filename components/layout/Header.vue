<script setup lang="ts">
import {
  Dropdown as hDropdown,
  Menu as hMenu,
  MenuItem as hMenuItem,
  SubMenu as hSubMenu,
  Popover as hPopover,
  message,
} from 'ant-design-vue';
import {businessDataStore} from '@haierbusiness-front/utils/src/store/businessData';
import {CaretDownFilled, PhoneFilled} from '@ant-design/icons-vue';
import {storeToRefs} from 'pinia';
import {onMounted, ref, computed, watch} from 'vue';
import user from '@/assets/image/icon/user.png';
import order from '@/assets/image/icon/order.png';
import vertical from '@/assets/image/icon/vertical.png';
import service from '@/assets/image/icon/service.png';
import todo from '@/assets/image/icon/todo.png';
import wechat from '@/assets/image/icon/wechat.png';
import logo from '@/assets/image/logo.png'
import qrcode from '@/assets/image/qrcode/qrcode.jpg'
import hot from '@/assets/image/discount/hot.png'
import { useRouter } from "vue-router";
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
import globalPinia from "@haierbusiness-front/utils/src/store/store"
import { loginApi, indirectApi } from '@haierbusiness-front/apis';
import { removeStorageItem } from '@haierbusiness-front/utils';
import { tripApi } from '@haierbusiness-front/apis';

import {
  HeaderConstant
} from '@haierbusiness-front/common-libs'
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil'

const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false)

// 达产激励暂时隐藏菜单----
const userList = [
  '01506579',
  '01306872',
  '22069502',
  '01347713',
  '01062839',
  '01435540',
  '01300132',
  '00575927',
  '01424673',
  '00594769',
  '19004080',
  '01377697',
  '24032740',
  '20112584',
  '22023471',
];
const showTabs = (val: string) => {
  if (!val) {
    return false;
  }
  return userList.indexOf(val) > -1;
};

// 达产激励暂时隐藏菜单----


const indexUrl = import.meta.env.VITE_BUSINESS_INDEX_URL;

const teamUrl = import.meta.env.VITE_BUSINESS_TEAM_URL;

const helperUrl = import.meta.env.VITE_BUSINESS_HELPER_URL;

const rechargeUrl = import.meta.env.VITE_BUSINESS_RECHARGE_URL;


const tripUrl = import.meta.env.VITE_BUSINESS_TRIP_URL

const slUrl = import.meta.env.VITE_BUSINESS_TRIP_SINGLE


const { loginUser } = storeToRefs(applicationStore(globalPinia))

const router = useRouter()

const goToSl = () => {
  const url = slUrl + 'fcc'
  window.open(url, '_blank')
}

const gotoDetail = (id: number | undefined) => {
  if (!id) {
    message.error('未找到对应的资讯！');
    return;
  }
  const thisUrl = router.resolve({
    path: '/travel/detail',
  });
  window.open(thisUrl.href + '?id=' + id + '&type=1', '_blank');
};

const gotoExternalUrl = (type: string) => {

  const url = slUrl + 'fcc//ddgl/ddgl.html?cplx=' + type

  window.open(url, '_blank')
}


const gotoUrl = (url: string | undefined) => {
  if (!url) {
    message.error('链接错误！');
    return;
  }
  window.open(indexUrl + '#' + url, '_blank');
  // router.push({ path: url })
}

// 跳转生态审批列表页
const travelHaier = import.meta.env.VITE_BUSINESSTRAVEL_URL
const gotoSt = (url:string) => {
  
  if(!url) {
    return
  }
  let realUrl = travelHaier + url
  
  let fullUrl = travelHaier + '#/login?url=' + encodeURIComponent(realUrl)
  console.log(9999999, fullUrl)
  window.open(fullUrl, '_blank')
} 

const thisPage = (url: string | undefined) => {
  window.location.href = indexUrl + '#' + url
}
// 限制 以 字母开头的账户不能展示跳转按钮
const isFirstCharDigit = (str: any) => {
  return /^\d/.test(str);
}

const goToTeam = (url:string | undefined) => {
  window.location.href = teamUrl + '#' + url
}

const goToTrip = (url:string | undefined) => {
  // 判断用户权限部分内测,其他用户跳回商旅
  tripApi.checkBeta().then((res:any) => {
    console.log('checkBeta------>>>>>', res)
    if(res) {
      window.location.href = tripUrl + '#' + url
    }else {
      goToSl()
    }
  })

  
}
const goToHelper = (url:string | undefined) => {
  window.location.href = helperUrl + '#' + url
}


const goToAccountRecharge = (url:string | undefined) => {
  window.location.href = indexUrl + '#' + url
}
const goToHandover = (url:string | undefined) => {
  window.location.href = indexUrl + '#' + url
}

const openNewWindow = (url: string) => {
  window.open(url, '_blank');
};

const logout = () => {
  loginApi.haierIamTokenLogout({token: loginUser?.value?.extended?.iamToken}).finally(() => {
    removeStorageItem(HeaderConstant.TOKEN_KEY.key, false);
    window.location.reload();
  });
};

const currentIndex = ref(['1']);
const offset = ref<[number, number]>([-40, 0]);

const gotoWater = () => {
  indirectApi.support().then((data) => {
    const url = 'http://gopurchase.haier.com/GoPurchase/login_GL.aspx?PSTRGL=' + data;
    window.open(url, '_blank');
  });
};

onMounted(async () => {});
watch(
    router.currentRoute,
    (newValue) => {
      if (newValue.path === '/discount') {
        currentIndex.value = ['11'];
      } else if (newValue.path === '/index') {
        currentIndex.value = ['1'];
      } else if (newValue.path === '/helper' || newValue.path === '/helper/detail') {
        currentIndex.value = ['12'];
      }
    },
    {immediate: true, deep: true},
);
</script>

<template>
  <div class="header">
    <div class="headerCon">
      <div>
        <h-dropdown>
          <div class="user">
            <div class="flex align-items">
              <img :src="user" class="icon"/>
            </div>
            <span>{{ loginUser?.nickName }}</span>
            <div>
              <CaretDownFilled class="font-10"/>
            </div>
          </div>
          <template #overlay>
            <h-menu>
              <h-menu-item @click="logout"> 退出登录</h-menu-item>
            </h-menu>
          </template>
        </h-dropdown>
      </div>

      <div class="mr-20">
        <h-dropdown class="header-dropdown">
          <div class="flex order">
            <div class="flex align-items">
              <img :src="order" class="icon"/>
              <span class="pl-8 font-14">订单中心</span>
            </div>
            <div>
              <CaretDownFilled class="font-10"/>
            </div>
          </div>
          <template #overlay>
            <h-menu class="order-menu">
              <h-menu-item class="order-menu" @click="gotoUrl('/card-order/localrest')">
                <span class="order-type"> 订餐 </span>
              </h-menu-item>
              <h-menu-item class="order-menu" @click="gotoUrl('/card-order/localhotel')">
                <div class="order-type">订房</div>
              </h-menu-item>
              <!-- <h-menu-item class="order-menu">
                            <div class="order-type">
                            网易云
                            </div>
                        </h-menu-item> -->
                        <h-menu-item class="order-menu" @click="gotoUrl('/card-order/jd')">
                            <div class="order-type">
                            京东
                            </div>
                        </h-menu-item>
                        <h-menu-item class="order-menu" @click="gotoUrl('/card-order/mice')">
                            <div class="order-type">
                            会展
                            </div>
                        </h-menu-item>
                        <h-menu-item class="order-menu" @click="gotoExternalUrl('jd')">
                            <div class="order-type">
                            酒店
                            </div>
                        </h-menu-item>
                        <h-menu-item class="order-menu" @click="gotoExternalUrl('hcp')">
                            <div class="order-type">
                            火车票
                            </div>
                        </h-menu-item>
                        <h-menu-item class="order-menu" @click="gotoExternalUrl('yc')">
                            <div class="order-type">
                            用车
                            </div>
                        </h-menu-item>
                        <h-menu-item class="order-menu" @click="gotoExternalUrl('jp')">
                            <div class="order-type">
                            机票
                            </div>
                        </h-menu-item>

                        <h-menu-item class="order-menu" @click="thisPage('/card-order/trip')">
                            <div class="order-type">
                            出差申请单
                            </div>
                        </h-menu-item>

                        <h-menu-item class="order-menu" @click="thisPage('/card-order/team')">
                            <div class="order-type">
                            团队票
                            </div>
                        </h-menu-item>
                          <h-sub-menu :popup-class-name="'order-menu'" v-if="isFirstCharDigit(loginUser?.username)" >
                            <template #title><span style="">商务云充值</span></template>
                            <h-menu-item class="order-menu" @click="thisPage('/card-order/recharge')">
                                <div class="order-type">
                                  商务云对公充值
                                </div>
                            </h-menu-item>
                            <h-menu-item class="order-menu" @click="thisPage('/card-order/excitation')">
                                <div class="order-type">
                                  商务云支付下发
                                </div>
                            </h-menu-item>
                          </h-sub-menu>


                      </h-menu>
                  </template>
                </h-dropdown>
            </div>

            <div class="mr-20">
                <h-dropdown class="header-dropdown">
                  <div class="flex order">
                      <div class="flex align-items">
                        <img :src="todo" class="icon">
                        <span class="pl-8 font-14">我的审批</span>
                      </div>
                      <div>
                        <CaretDownFilled class="font-10" />
                      </div>
                  </div>
                  <template #overlay>
                      <h-menu class="order-menu">
                        <h-menu-item class="order-menu" @click="gotoUrl('/portal-control/me')">
                            <span class="order-type">
                              我发起的
                            </span>
                        </h-menu-item>
                        <h-menu-item class="order-menu" @click="gotoUrl('/portal-control/todo')">
                            <div class="order-type">
                              待我审批
                            </div>
                        </h-menu-item>

                        <h-menu-item class="order-menu" @click="gotoSt('enterprise/#/personal/waitApproval')">
                            <div class="order-type">
                              审批订单(旧)
                            </div>
                        </h-menu-item>
                      </h-menu>
                  </template>
                </h-dropdown>
            </div>

      <div class="mr-13">
        <img :src="vertical" class="vertical"/>
      </div>

      <div class="mr-32">
        <h-popover placement="bottom">
          <template #content>
            <div class="contact">
              <div class="phone">
                <PhoneFilled/>
              </div>
              <div class="number">0532-88931999</div>
            </div>
            <div class="contact">
              <div class="phone"></div>
              <div class="number">(或)4006-999-521</div>
            </div>
          </template>
          <img :src="service" class="icon pointer"/>
        </h-popover>
      </div>

      <div class="mr-26">
        <h-popover placement="bottom">
          <template #content>
            <img :src="qrcode" class="qrcode pointer"/>
          </template>
          <img :src="wechat" class="icon pointer"/>
        </h-popover>
      </div>

      <!-- <div class="mr-26">
                <img :src="notice" class="icon pointer">
            </div> -->

        </div>
    </div>
    <div class="menu flex">
        <div class="logo-con">
            <img :src="logo" class="logo">
        </div>
        <div class="menu-list">
            <h-menu v-model:selectedKeys="currentIndex" mode="horizontal" class="menu-center">
              <h-menu-item key="1" @click="thisPage('/index' )" >
                  <span class="title">首页</span>
              </h-menu-item>
              <h-sub-menu key="2" :popup-offset="offset" :popup-class-name="'menu-con'">
                  <template #title><span class="title">差旅服务</span></template>
                  <h-menu-item key="2-1" @click="goToTrip('/apply')"><span class="sub-title">出差申请</span></h-menu-item>
                  <h-menu-item key="2-10" @click="goToTeam('/pc/addTeam')"><span class="sub-title">团队票</span></h-menu-item>
                  <!-- <h-menu-item key="2-2" @click="goToSl"><span class="sub-title">国内机票</span></h-menu-item>
                  <h-menu-item key="2-3" @click="goToSl"><span class="sub-title">国际机票</span></h-menu-item>
                  <h-menu-item key="2-4" @click="goToSl"><span class="sub-title">酒店</span></h-menu-item>
                  <h-menu-item key="2-8" @click="goToSl"><span class="sub-title">火车</span></h-menu-item>
                  <h-menu-item key="2-9" @click="goToSl"><span class="sub-title">地面服务</span></h-menu-item> -->

              </h-sub-menu>
              <h-sub-menu key="7" :popup-offset="offset" :popup-class-name="'menu-con'">
                  <template #title><span class="title">青岛餐房</span></template>
                  <h-menu-item key="7-1" @click="openNewWindow('https://businesstravel.haier.net/localrest/#/?hb-token=' + token)"><span class="sub-title">青岛订餐</span></h-menu-item>
                  <h-menu-item key="7-2" @click="openNewWindow('https://businesstravel.haier.net/localhotel/#/?hb-token=' + token)"><span class="sub-title">青岛订房</span></h-menu-item>
              </h-sub-menu>
              <h-sub-menu key="8" :popup-offset="offset" :popup-class-name="'menu-con'">
                  <template #title><span class="title">商务会展</span></template>
                  <h-menu-item key="8-1" @click="openNewWindow('https://businesstravel.haier.net/mice/#/add/conference/qingdao/view')"><span class="sub-title">青岛会议</span></h-menu-item>
                  <h-menu-item key="8-2" @click="openNewWindow('https://businesstravel.haier.net/mice/#/add/conference/allopatric/view')"><span class="sub-title">异地会议</span></h-menu-item>
                  <h-menu-item key="8-3" @click="openNewWindow('https://businesstravel.haier.net/mice/#/add/conference/ecological/qingdao/view')"><span class="sub-title">生态会议（青岛）</span></h-menu-item>
                  <h-menu-item key="8-4" @click="openNewWindow('https://businesstravel.haier.net/mice/#/add/conference/ecological/allopatric/view')"><span class="sub-title">生态会议（异地）</span></h-menu-item>
                  <h-menu-item key="8-5" @click="openNewWindow('https://businesstravel.haier.net/mice/#/add/flight/view')"><span class="sub-title">会展机票</span></h-menu-item>
                  <h-menu-item key="8-6" @click="openNewWindow('https://authority.haier.net/authority/login-syslogin.jsp?sysuuid=a8581259-64c0-9f4b-e053-3577850aaa70')"><span class="sub-title">权限申请</span></h-menu-item>
                  <h-menu-item key="8-7" @click="openNewWindow('https://authority.haier.net/authority/login-syslogin.jsp?sysuuid=a82f7403-64c0-9f4b-e053-3577850aaa70')"><span class="sub-title">审批流维护</span></h-menu-item>
              </h-sub-menu>
              <h-sub-menu key="9" :popup-offset="offset" :popup-class-name="'menu-con'">
                  <template #title><span class="title">企业福利</span></template>
                  <h-menu-item key="9-1" @click="openNewWindow('https://cksupermarket.haier.net/')"><span class="sub-title">园区福利</span></h-menu-item>
                  <h-menu-item key="9-2" @click="openNewWindow('https://businesstravel.haier.net/mall/#/jd/index')"><span class="sub-title">京东云超市</span></h-menu-item>
                  <!-- <h-menu-item key="9-3" @click="openNewWindow('https://netecloud.haier.net/#/home')"><span class="sub-title">网易云超市</span></h-menu-item> -->

                  <h-sub-menu key="9-6" v-if="isFirstCharDigit(loginUser?.username)" :popup-class-name="'menu-con'">
                      <template #title><span class="title">商务云充值</span></template>
                      <h-menu-item key="9-6-1"  @click="goToAccountRecharge('/recharge/accountRecharge')"><span class="sub-title">商务云对公充值</span></h-menu-item>
                      <h-menu-item key="9-6-2" @click="goToAccountRecharge('/excitation/addExcitation')"><span class="sub-title">商务云支付下发</span></h-menu-item>
                      <!-- <h-menu-item key="9-6-3" @click="goToHandover('/excitation/handover')"><span class="sub-title">工会激励交接</span></h-menu-item> -->
                  </h-sub-menu>

          <!--
                  <h-menu-item key="9-4" @click="goToAccountRecharge('/recharge/accountRecharge')"><span class="sub-title">工会账户充值</span></h-menu-item>
                  <h-menu-item key="9-5" @click="goToAccountRecharge('/excitation/addExcitation')"><span class="sub-title">工会激励下发</span></h-menu-item>
                  <h-menu-item key="9-6" @click="goToHandover('/excitation/handover')"><span class="sub-title">工会激励交接</span></h-menu-item> -->

              </h-sub-menu>
              <h-menu-item key="11">
                  <div class="title gradient discount" @click="thisPage('/discount')">
                    <div class="user-discount">
                      <span style="--i:1" class="gradient-1">员</span>
                      <span style="--i:2" class="gradient-2">工</span>
                      <span style="--i:3" class="gradient-3">特</span>
                      <span style="--i:4" class="gradient-4">惠</span>
                    </div>
                    <div class="hot">
                      <img :src="hot" class="hot-img" style="--i:5" />
                    </div>
                  </div>
              </h-menu-item>
              <h-menu-item key="12">
                  <span class="title" @click="goToHelper('/pc/index')">创客帮</span>
              </h-menu-item>
              <h-menu-item key="13">
                  <span class="title" @click="openNewWindow('https://businesstravel.haier.net/#/about')">关于我们</span>
              </h-menu-item>
              <h-sub-menu key="14" :popup-offset="offset" :popup-class-name="'menu-con'">
                  <template #title><span class="title">饮用水</span></template>
                  <h-menu-item key="14-1"  @click="openNewWindow('https://haierwater.haier.net/water/login.aspx?PSTR=' + loginUser?.username)"><span class="sub-title">饮用水预定</span></h-menu-item>
                  <h-menu-item key="14-2"  @click="openNewWindow('https://haierwater.haier.net/water/login.aspx?PSTRQX=' + loginUser?.username)"><span class="sub-title">饮用水权限申请</span></h-menu-item>
              </h-sub-menu>
            </h-menu>
        </div>
        <!-- <div class="space"></div> -->

    </div>
</template>

<style scoped lang="less">
.pointer {
  cursor: pointer;
}

.mr-20 {
  margin-right: 20px;
}

.mr-13 {
  margin-right: 13px;
}

.mr-32 {
  margin-right: 13px;
}

.mr-26 {
  margin-right: 13px;
}

.font-10 {
  font-size: 10px;
}

.font-14 {
  font-size: 14px;
}

.icon {
  width: 22px;
  height: 22px;
}

.vertical {
  width: 1px;
}

.pl-8 {
  padding-left: 8px;
}

.flex {
  display: flex;
}

.align-items {
  align-items: center;
}

.header {
  background: #3983e5;
  width: 100%;
  height: 40px;
  display: flex;
  justify-content: center;
  font-family: 'HarmonyBold';

  .headerCon {
    width: 1200px;
    display: flex;
    height: 40px;
    flex-direction: row-reverse;
    align-items: center;
    color: #ffffff;

    .user {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 32px;
      width: 130px;
      border-radius: 16px;
      background: rgba(255, 255, 255, 0.3);
      padding-left: 12px;
      padding-right: 12px;
      cursor: pointer;
      font-family: Microsoft YaHei;
    }

    .order {
      align-items: center;
      justify-content: space-between;
      height: 32px;
      width: 105px;
      border-radius: 16px;

      cursor: pointer;
    }
  }
}

.menu {
  height: 50px;
  width: 100%;
  box-shadow: 0px 0px 5px #f5f5f5;

  .logo-con {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 250px;

    .logo {
      // width: 168px;
      // height: 35px;
      width: 130px;
      height: 26px;
    }
  }

  .menu-list {
    width: 1200px;

    .menu-center {
      display: flex;
      width: 100%;
      // justify-content: center;
    }

    .title {
      color: #3983e5;
      font-size: 18px;
      font-weight: 600;
    }

    .gradient-1 {
      background: linear-gradient(to right, #ff8d2e 0%, #ff8d2e 50%);
      -webkit-background-clip: text; /*将设置的背景颜色限制在文字中*/
      -webkit-text-fill-color: transparent; /*给文字设置成透明*/
    }

    .gradient-2 {
      background: linear-gradient(to right, #ff8d2e 50%, #ff0d0f 100%);
      -webkit-background-clip: text; /*将设置的背景颜色限制在文字中*/
      -webkit-text-fill-color: transparent; /*给文字设置成透明*/
    }

    .gradient-3 {
      background: linear-gradient(to right, #ff0d0f 0%, #ff0d0f 50%);
      -webkit-background-clip: text; /*将设置的背景颜色限制在文字中*/
      -webkit-text-fill-color: transparent; /*给文字设置成透明*/
    }

    .gradient-4 {
      background: linear-gradient(to right, #ff0d0f 50%, #ff0d0f 100%);
      -webkit-background-clip: text; /*将设置的背景颜色限制在文字中*/
      -webkit-text-fill-color: transparent; /*给文字设置成透明*/
    }

    .discount {
      display: flex;
      flex-direction: row;
      align-items: center;
      position: relative;

      .user-discount {
        display: flex;

        span {
          /* 设置行内块元素 */
          display: inline-block;
          /* 添加动画 */
          animation: jump 1.5s ease-in-out infinite;
          /* 利用变量动态计算动画延迟时间 */
          animation-delay: calc(0.1s * var(--i));
        }
      }

      .hot {
        position: absolute;
        width: 28px;
        top: -15px;
        right: -28px;
        /* 添加动画 */
        animation: jump 1.5s ease-in-out infinite;
        /* 利用变量动态计算动画延迟时间 */
        animation-delay: 0.5s;

        .hot-img {
          width: 28px;
        }
      }
    }
  }
}

.order-type {
  color: #3983e5;
}

.contact {
  display: flex;
  flex-direction: row;

  .phone {
    display: flex;
    width: 26px;
    align-items: center;
  }

  .number {
    display: flex;
  }
}
</style>

<style>
.menu-con {
  width: 160px !important;
}

.menu-list .ant-menu-horizontal {
  line-height: 46px;
  border-bottom: 0px solid #f0f0f0;
}

.menu-list .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item:hover::after,
.ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu:hover::after,
.ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item-active::after,
.ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu-active::after,
.ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item-open::after,
.ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu-open::after,
.ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item-selected::after,
.ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu-selected::after {
  border-bottom: 3px solid #3983e5;
}

.ant-menu-light:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected):hover {
  background: #3983e5;
  font-weight: 500;
}

.ant-menu-sub .ant-menu-item-active .sub-title {
  color: #fff;
  font-weight: 500;
}

.ant-menu-submenu .ant-menu-sub .ant-menu-item-active .ant-menu-title-content {
  color: #fff;
  font-weight: 500;
}

.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item:hover,
.ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item:hover,
.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu-title:hover,
.ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-submenu-title:hover,
.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item-active,
.ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item-active,
.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu-title-active,
.ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-submenu-title-active {
  background: #3983e5;
  color: #fff !important;
}

.order-menu .ant-dropdown-menu-item:hover .order-type {
  color: #fff !important;
}

.order-menu .ant-dropdown-menu-submenu-title .ant-dropdown-menu-title-content span {
  color: #3983e5;
}

.order-menu .ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-arrow {
  color: #3983e5;
}

.menu-con .ant-menu-submenu-title:hover {
  background: #3983e5 !important;
  color: #fff !important;
}

.order-menu .ant-dropdown-menu-submenu-title:hover span {
  color: #fff !important;
}

.order-menu .ant-dropdown-menu-submenu-title:hover .ant-dropdown-menu-submenu-arrow {
  color: #fff !important;
}

.order-menu .ant-dropdown-menu-submenu-item:hover .ant-dropdown-menu-title-content .order-type {
  color: #fff !important;
}

.qrcode {
  width: 150px;
  height: 150px;
}

@keyframes jump {

0
{
  transform: translateY(0px)
;
}
20
%
{
  transform: translateY(-7px)
;
}
40
%
,
100
%
{
  transform: translateY(0px)
;
}
}
</style>
