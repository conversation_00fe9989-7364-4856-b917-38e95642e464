<script setup lang="ts">
import EFooter from './Footer.vue';
import EHeader from './Header.vue';

</script>

<template>
    <div style="min-height:100vh">
        <e-header></e-header>
        <div class="content">
            <router-view v-slot="{ Component }">
                <component :is="Component" />
            </router-view>
        </div>
        <e-Footer></e-Footer>
    </div>
</template>

<style scoped lang="less">

.content {
  min-height: calc(100vh - 335px);
}

</style>