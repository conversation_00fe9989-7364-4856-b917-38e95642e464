<script lang="ts" setup>
import { BarsOutlined, DownOutlined } from '@ant-design/icons-vue';
import type { MenuItemType, MenuInfo } from 'ant-design-vue/lib/menu/src/interface'

interface Props {
    menuOptions: MenuItemType[]
    onMenuClick: (record, e: MenuInfo) => void
}

const props = withDefaults(defineProps<Props>(), {
    
});
</script>

<template>
    <a-dropdown v-if="props.menuOptions.length > 0">
        <a-button style="border: none;">
            <BarsOutlined style="margin-right: 2px;" />
            <DownOutlined />
        </a-button>
        <template #overlay>
            <a-Menu @click="props.onMenuClick">
                <a-menu-item v-for="item in props.menuOptions" :key="item.key" :title="item.label">
                    {{ item.label }}
                </a-menu-item>
            </a-Menu>
        </template>
    </a-dropdown>
</template>

<style lang="less" scoped>

</style>