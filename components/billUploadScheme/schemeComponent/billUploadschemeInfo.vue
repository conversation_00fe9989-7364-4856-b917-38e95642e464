<script setup lang="ts">
// 方案互动
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, computed, nextTick, defineProps } from 'vue';
import { DemandSubmitObj, ProcessNode, MiceTypeConstant } from '@haierbusiness-front/common-libs';
import miceHeader from '../../mice/miceHeader.vue';

const props = defineProps({
  demandInfo: {
    type: Object,
    default: {},
  },
  readonly: {
    type: Boolean,
    default: false,
  },
});
const orderType = ref(false)
// 🔥 监听 demandInfo 变化
watch(
  () => props.demandInfo,
  (newVal) => {},
  { immediate: true, deep: true },
);

// 复制文本到剪贴板
const getCopy = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    message.success('复制成功！');
  } catch (err) {
    message.success('复制失败');
  }
};

onMounted(async () => {
  // console.log('🔥 billUploadschemeInfo 组件已挂载，当前 demandInfo:', props.demandInfo);
});
</script>

<template>
  <!-- 方案互动 - 头部 -->
  <miceHeader :miceDetail="props.demandInfo">
    <template #header v-if="orderType"> <slot name="header"></slot></template>
  </miceHeader>
</template>

<style scoped lang="less">
.scheme_info {
  padding: 24px 32px;

  width: 100%;
  height: 100%;
  background: url('@/assets/image/scheme/mice_bgc.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;

  border-radius: 6px;

  .interact_mice_title {
    display: flex;
    align-items: center;

    .interact_mice_name_img {
      width: 28px;
      height: 28px;
      background-image: url('@/assets/image/scheme/mice_name.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .interact_mice_name {
      font-family: PingFangSCSemibold, PingFangSCSemibold;
      font-weight: normal;
      font-size: 20px;
      color: #1d2129;
      line-height: 28px;
    }

    .interact_mice_type {
      width: 108px;
      height: 28px;
      line-height: 28px;
      text-align: center;

      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #ffffff;
      background: #1868db;
      border-radius: 4px;
    }
  }

  .interact_mice_num {
    display: flex;
    align-items: center;

    font-size: 14px;
    color: #86909c;
    line-height: 20px;

    img {
      cursor: pointer;
    }
  }
  .interact_mice_info {
    width: 50%;
    font-size: 14px;
    color: #86909c;
    line-height: 20px;

    .mice_info_title {
      display: inline-block;
      text-indent: 26px;
      background-size: 16px 16px;
      background-repeat: no-repeat;
      background-position: center left;
    }
    .mice_info_person_img {
      background-image: url('@/assets/image/scheme/mice_person.png');
    }

    .mice_info_type_img {
      background-image: url('@/assets/image/scheme/mice_type.png');
    }

    .mice_info_time_img {
      background-image: url('@/assets/image/scheme/mice_time.png');
    }

    .mice_info_value {
      color: #1d2129;
    }
  }
}
</style>
