<script setup lang="ts">
// 方案互动-拓展方案
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';

const props = defineProps({
  schemeItem: {
    type: Object,
    default: {},
  },
  schemeCacheItem: {
    type: Object,
    default: {},
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  schemeIndex: {
    type: Number,
    default: 0,
  },
  schemeType: {
    // 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
    type: String,
    default: '',
  },
  showBindingScheme: {
    type: Boolean,
    default: true,
  },
  readonly: {
    // 是否为只读模式
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['schemePriceEmit', 'schemeActivityEmit']);

const oldSchemeList = ref<Array<any>>([]);
const newSchemeList = ref<Array<any>>([]);

const subtotal = ref<number>(0); // 小计
const demandSubtotal = ref<number>(0); // 左侧需求小计

// 价格计算 - 使用账单数据
const priceCalcFun = () => {
  subtotal.value = 0;

  newSchemeList.value.forEach((e) => {
    if (e.billUnitPrice && e.billPersonNum) {
      subtotal.value += e.billUnitPrice * e.billPersonNum;
    }
  });

  emit('schemePriceEmit', { type: 'activity', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
};

watch(
  () => props.schemeItem,
  (newObj) => {


    oldSchemeList.value = JSON.parse(JSON.stringify(newObj))?.activities || [];

    if (
      props.isSchemeCache &&
      props.schemeCacheItem &&
      props.schemeCacheItem.activities &&
      props.schemeCacheItem.activities.length > 0 &&
      !props.readonly // 🔥 查看模式下不使用缓存
    ) {
      // 编辑模式：缓存 - 反显（只有当缓存数据存在且不为空时才使用）
      newSchemeList.value = props.schemeCacheItem.activities.map((e) => {
        // 确保缓存数据也有账单字段
        return {
          ...e,
          billPersonNum: e.billPersonNum || e.schemePersonNum || e.personNum,
          billUnitPrice: e.billUnitPrice || e.schemeUnitPrice || e.demandUnitPrice,
          // 对于账单上传，sourceId 应该是活动项自己的 id
          sourceId: e.sourceId || e.id,
          // 确保有miceSchemeId字段
          miceSchemeId: e.miceSchemeId,
        };
      });

      // 价格计算
      priceCalcFun();
    } else {
      // 使用原始数据或缓存为空时的回退逻辑
      const demandData = JSON.parse(JSON.stringify(newObj))?.activities || [];

      newSchemeList.value = demandData.map((e) => {
        return {
          // 从详情数据获取的字段
          miceDemandActivityId: e.miceDemandActivityId || e.id,
          miceSchemeId: e.miceSchemeId,                    // 保留miceSchemeId用于提交
          demandDate: e.demandDate,
          demandUnitPrice: e.demandUnitPrice,
          schemePersonNum: e.schemePersonNum || e.personNum,
          schemeUnitPrice: e.schemeUnitPrice || e.demandUnitPrice,
          description: e.description,
          sourceId: e.sourceId || e.id,
          paths: e.paths,
          fileList: e.fileList,

          // 账单字段
          // 🔥 查看模式使用详情数据，编辑模式使用默认值
          billPersonNum: props.readonly ? e.billPersonNum || e.schemePersonNum || e.personNum : e.schemePersonNum || e.personNum,
          // 🔥 查看模式使用详情数据，编辑模式使用默认值
          billUnitPrice: props.readonly ? e.billUnitPrice || e.schemeUnitPrice || e.demandUnitPrice : null,
          // 🔥 查看模式使用详情数据，编辑模式使用默认值
          invoiceTempId: props.readonly ? e.invoiceTempId || null : null,
          statementTempId: props.readonly ? e.statementTempId || null : null,
        };
      });



      // 如果是缓存模式但缓存为空，也需要计算价格
      if (props.isSchemeCache) {
        priceCalcFun();
      }
    }

    // 小计 - 使用账单数据计算
    subtotal.value = 0;
    newSchemeList.value.forEach((e) => {
      if (e.billUnitPrice && e.billPersonNum) {
        subtotal.value += e.billUnitPrice * e.billPersonNum;
      }
    });

    // 左侧需求小计
    demandSubtotal.value = 0;
    oldSchemeList.value.forEach((e) => {
      if (e.demandUnitPrice && e.schemePersonNum) {
        demandSubtotal.value += e.demandUnitPrice * e.schemePersonNum;
      }
    });

    emit('schemePriceEmit', { type: 'activity', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
  },
  {
    immediate: true,
    deep: true,
  },
);

const schemePlanLabelList = ['费用标准', '人数', '活动需求', '附件'];

const changePrice = (index: number) => {
  // 账单人数变化时重新计算价格
  priceCalcFun();
};

const fileChange = (fileList: Array<any>) => {
  // 添加参数验证，确保 fileList 是一个数组
  if (!fileList || !Array.isArray(fileList) || fileList.length === 0) {
    return '-';
  }

  let document = '';
  let file = {};

  fileList.forEach((item, index) => {
    let isJson = true;

    try {
      file = JSON.parse(item);
    } catch (error) {
      console.log(error);
      isJson = false;
    }

    if (!isJson) return;

    document += `
      <a target='_blank' href='${file.url}'>${file.name}</a>
      <span style='margin-right: 10px;color: #86909c' >${index === fileList.length - 1 ? '' : ','}</span>
    `;
  });

  return document ? document : '-';
};

// 暂存 - 传递账单数据
const activityTempSave = () => {
  emit('schemeActivityEmit', {
    schemeActivities: [...newSchemeList.value],
    billActivities: getBillActivitySubmitData(),
    schemeIndex: props.schemeIndex,
  });
};

// 校验
const activitySub = () => {
  let isVerPassed = true;

  // newSchemeList.value.forEach((e, i) => {
  //   if (!e.schemeUnitPrice) {
  //     message.error('请输入' + e.demandDate + '拓展方案' + (i + 1) + '竞价单价');

  //     isVerPassed = false;
  //     return;
  //   }
  // });

  if (isVerPassed) {
    activityTempSave();
  }

  return isVerPassed;
};

// 获取账单活动提交数据
const getBillActivitySubmitData = () => {
  return newSchemeList.value.map(item => ({
    // 必填字段
    invoiceTempId: item.invoiceTempId || null,           // 发票临时id - 暂时为空
    statementTempId: item.statementTempId || null,       // 水单临时id - 暂时为空
    billPersonNum: item.billPersonNum,                   // 账单人数 - 必填
    billUnitPrice: item.billUnitPrice,                   // 账单单价 - 必填

    // 可选字段
    sourceId: item.sourceId,                             // 上一版本id
    miceDemandActivityId: item.miceDemandActivityId,     // 需求拓展活动id
    miceSchemeActivityId: item.miceSchemeId,             // 方案拓展活动id - 使用详情的miceSchemeId
    demandDate: item.demandDate,                         // 需求日期
    demandUnitPrice: item.demandUnitPrice,               // 费用标准
    schemePersonNum: item.schemePersonNum,               // 方案人数
    schemeUnitPrice: item.schemeUnitPrice,               // 方案单价
    description: item.description,                       // 方案说明
  }));
};

defineExpose({ activitySub, activityTempSave, getBillActivitySubmitData });

onMounted(async () => {});
</script>

<template>
  <!-- 拓展方案 -->
  <div class="scheme_vehicle">
    <div class="common_table">
      <!-- 左侧 -->
      <div class="common_table_l" v-if="props.schemeType !== 'notBidding' && props.schemeType !== 'biddingView'">
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>拓展方案</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in oldSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '拓展需求' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.demandUnitPrice ? item.demandUnitPrice + '元/人' : '-' }}
                </template>
                {{ item.demandUnitPrice ? item.demandUnitPrice + '元/人' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.description || '-' }}
                </template>
                {{ item.description || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12" v-html="fileChange(item.paths)"></div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  item.demandUnitPrice && item.schemePersonNum
                    ? '¥' + formatNumberThousands(item.demandUnitPrice * item.schemePersonNum)
                    : '-'
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.demandUnitPrice && item.schemePersonNum">
                {{ item.schemePersonNum + '人*' + item.demandUnitPrice + '(单价)' }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="common_table_divide"></div>

      <!-- 右侧 -->
      <div class="common_table_r">
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>拓展账单</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in newSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '拓展方案' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.billUnitPrice ? item.billUnitPrice + '元/人' : '-' }}
                </template>
                {{ item.billUnitPrice ? item.billUnitPrice + '元/人' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value p0">
              <div
                class="pl12"
                v-if="
                  props.readonly ||
                  props.schemeType === 'notBidding' ||
                  props.schemeType === 'biddingView' ||
                  props.schemeType === 'schemeView'
                "
              >
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ item.billPersonNum ? item.billPersonNum + '人' : '-' }}
                  </template>
                  {{ item.billPersonNum ? item.billPersonNum + '人' : '-' }}
                </a-tooltip>
              </div>
              <div class="pl12" v-else>
                <a-tooltip placement="topLeft">
                  <template #title v-if="item.billPersonNum">
                    {{ item.billPersonNum ? item.billPersonNum + '人' : '-' }}
                  </template>
                  <a-input-number
                    v-model:value="item.billPersonNum"
                    @change="changePrice(idx)"
                    style="width: calc(100% - 30px)"
                    placeholder="人数"
                    :min="1"
                    :max="999"
                    :precision="0"
                    :bordered="false"
                    :controls="false"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.description || '-' }}
                </template>
                {{ item.description || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12" v-html="fileChange(item.paths)"></div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  item.billUnitPrice && item.billPersonNum
                    ? '¥' + formatNumberThousands(item.billUnitPrice * item.billPersonNum)
                    : '-'
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.billUnitPrice && item.billPersonNum">
                {{ item.billPersonNum + '人*' + item.billUnitPrice + '(单价)' }}
              </div>
            </div>
          </div>
        </div>

        <div v-show="subtotal" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_vehicle {
  .scheme_plan_img {
    background: url('@/assets/image/demand/demand_activity.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
    background-position: left center;
  }

  // 价格输入框样式 - 如果有的话
  .scheme_plan_price_value {
    :deep(.ant-input-number .ant-input-number-input) {
      height: 24px;
      padding: 0 5px;
      text-align: end;

      width: 84px;
      font-weight: 500;
      font-size: 14px;
      color: #1868db;
      text-align: right;
      border-bottom: 1px solid #4e5969;
    }
  }

  // 人数输入框样式，模仿服务人员组件
  .scheme_plan_value {
    :deep(.ant-input-number) {
      border: none;
      box-shadow: none;

      .ant-input-number-input {
        height: auto;
        padding: 0;
        text-align: left;
        width: 100%;
        font-weight: normal;
        font-size: 14px;
        color: #333;
        border-bottom: none;
        border: none;

        &::placeholder {
          color: #bfbfbf;
        }
      }

      &:hover .ant-input-number-input,
      &:focus .ant-input-number-input,
      &.ant-input-number-focused .ant-input-number-input {
        border: none;
        box-shadow: none;
      }
    }

    .scheme_plan_edit {
      margin-left: 5px;
      display: inline-flex;
      vertical-align: middle;

      width: 16px;
      height: 18px;
      background: url('@/assets/image/common/edit_gray.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
    }
  }

  .p0 {
    padding: 0 !important;
  }
}
</style>
