<script setup lang="ts">
// 方案互动-其他方案
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';

const props = defineProps({
  demandInfo: {
    type: Object,
    default: {},
  },
  schemeCacheInfo: {
    type: Object,
    default: {},
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  schemeType: {
    // 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
    type: String,
    default: '',
  },
  showBindingScheme: {
    type: Boolean,
    default: true,
  },
  // 只读模式
  readonly: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['otherPriceEmit', 'schemeOtherEmit']);

const oldSchemeList = ref<array>([]);
const newSchemeList = ref<array>([]);

const subtotal = ref<number>(0); // 小计
const leftSubtotal = ref<number>(0); // 左侧展示小计

watch(
  () => [props.demandInfo, props.schemeCacheInfo],
  () => {
    // 如果 demandInfo 为空或者没有 others 属性，直接返回
    if (!props.demandInfo || !props.demandInfo.others) {
      return;
    }

    oldSchemeList.value = JSON.parse(JSON.stringify(props.demandInfo))?.others || [];

    if (props.isSchemeCache && props.schemeCacheInfo) {
      // 缓存 - 反显
      newSchemeList.value = props.schemeCacheInfo?.others || [];

      newSchemeList.value.forEach((e) => {
        // 确保缓存数据包含所有必要字段
        if (!e.hasOwnProperty('invoiceTempId')) e.invoiceTempId = null;
        if (!e.hasOwnProperty('statementTempId')) e.statementTempId = null;
        if (!e.hasOwnProperty('sourceId') || e.sourceId === null) {
          // 如果缓存中没有sourceId或为null，从原始需求数据中获取
          // 根据接口数据结构，应该匹配 id 字段
          const originalItem = props.demandInfo?.others?.find((item: any) => item.id === e.miceDemandOtherId);
          e.sourceId = originalItem?.sourceId || null;
        }
        if (!e.hasOwnProperty('miceSchemeOtherId')) e.miceSchemeOtherId = null;
        if (!e.hasOwnProperty('billNum')) e.billNum = e.num;

        // 重要：schemeTotalPrice 必须使用详情返回的原始值，不能使用缓存中的值
        const originalItem = props.demandInfo?.others?.find((item: any) => item.id === e.miceDemandOtherId);
        if (originalItem) {
          e.schemeTotalPrice = originalItem.schemeTotalPrice;
          console.log(
            '%c [ 修正schemeTotalPrice ]-24',
            'font-size:13px; background:green; color:white;',
            `从缓存的 ${e.schemeTotalPrice} 修正为详情的 ${originalItem.schemeTotalPrice}`,
          );
        }

        // billTotalPrice 不设置默认值，用户手动输入
        if (!e.hasOwnProperty('billTotalPrice')) {
          e.billTotalPrice = null;
        }
      });
    } else {
      const demandData = JSON.parse(JSON.stringify(props.demandInfo))?.others || [];
      newSchemeList.value = demandData.map((e) => {
        return {
          // 临时id字段，暂时为空
          invoiceTempId: null,
          statementTempId: null,
          sourceId: e.sourceId || null, // 从当前项中获取sourceId

          // 需求相关字段
          miceDemandOtherId: e.id,
          miceSchemeOtherId: null,

          demandDate: e.demandDate,
          itemName: e.itemName,
          num: e.num, // 方案数量
          billNum: e.num, // 账单数量，初始值与方案数量相同
          unit: e.unit,
          specs: e.specs,
          description: e.description,

          // 价格相关字段
          demandTotalPrice: e.demandTotalPrice,
          schemeTotalPrice: e.schemeTotalPrice, // 方案总金额（详情返回的原始值）
          billTotalPrice: null, // 账单总金额，不设置默认值，用户手动输入
        };
      });
    }

    // 小计 - 使用账单总金额计算（与changeTotalPrice保持一致）
    subtotal.value = 0;
    newSchemeList.value.forEach((e) => {
      if (e.billTotalPrice) {
        subtotal.value += e.billTotalPrice;
      }
    });

    // 左侧展示小计 - 使用方案总金额计算
    leftSubtotal.value = 0;
    oldSchemeList.value.forEach((e: any) => {
      if (e.schemeTotalPrice) {
        leftSubtotal.value += e.schemeTotalPrice;
      }
    });

    emit('otherPriceEmit', subtotal.value);
  },
  {
    immediate: true,
    deep: true,
  },
);

const schemePlanLabelList = ['项目', '数量', '单位', '总预算', '规则说明'];
const billPlanLabelList = ['项目', '账单数量', '单位', '账单总金额', '规则说明'];

const changePrice = (index: number) => {
  if (newSchemeList.value[index].biddingPrice) {
    newSchemeList.value[index].planPrice = newSchemeList.value[index].biddingPrice * newSchemeList.value[index].num;
  }

  const isAllPriceWrite = newSchemeList.value.every((e) => e.planPrice && e.planPrice > 0);
  subtotal.value = 0;

  if (isAllPriceWrite) {
    newSchemeList.value.forEach((e) => {
      subtotal.value += e.planPrice;
    });

    emit('otherPriceEmit', subtotal.value);
  }
};

// 账单总额变化处理函数
const changeTotalPrice = (index: number) => {
  // 确保 billNum 始终等于 num（数量不能修改，直接使用左边的数量）
  newSchemeList.value[index].billNum = newSchemeList.value[index].num;

  // 重新计算小计 - 使用 billTotalPrice 计算
  subtotal.value = 0;
  newSchemeList.value.forEach((e: any) => {
    if (e.billTotalPrice) {
      subtotal.value += e.billTotalPrice;
    }
  });

  // 触发事件
  emit('otherPriceEmit', subtotal.value);
};

// 暂存
const otherTempSave = () => {
  // 确保提交前 billNum 始终等于 num
  newSchemeList.value.forEach((item: any) => {
    item.billNum = item.num;
    // 注意：schemeTotalPrice 保持原始值，billTotalPrice 是用户输入的账单金额
    // 提交时两个字段都会传递，后端根据需要使用
  });

  emit('schemeOtherEmit', [...newSchemeList.value]);
};

// 锚点
const anchorJump = (id: string) => {
  const element = document.getElementById(id);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
};
const isVerifyFailed = ref(false)
// 校验
const otherSub = () => {
  let isVerPassed = true;

  newSchemeList.value.forEach((e, i) => {
    if (!e.billTotalPrice) {
      isVerifyFailed.value = true
      message.error('请输入其他' + (i + 1) + '单价');
      anchorJump('orderbillTotalPrice');
      isVerPassed = false;
      return;
    }
  });

  if (isVerPassed) {
    otherTempSave();
  }

  return isVerPassed;
};

defineExpose({ otherSub, otherTempSave });

onMounted(async () => { });
</script>

<template>
  <!-- 其他方案 -->
  <div class="scheme_vehicle">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>其他方案</span>
    </div>

    <div class="common_table mt16">
      <!-- 左侧 -->
      <div class="common_table_l" v-if="props.schemeType !== 'notBidding' && props.schemeType !== 'biddingView'">
        <div class="scheme_plan_table" v-for="(item, idx) in oldSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '其他' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.itemName || '-' }}
                </template>
                {{ item.itemName || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.num || '-' }}
                </template>
                {{ item.num || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.unit || '-' }}
                </template>
                {{ item.unit || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{
                    item.schemeTotalPrice
                      ? item.schemeTotalPrice + '元'
                      : item.demandTotalPrice
                        ? item.demandTotalPrice + '元'
                        : '-'
                  }}
                </template>
                {{
                  item.schemeTotalPrice
                    ? item.schemeTotalPrice + '元'
                    : item.demandTotalPrice
                      ? item.demandTotalPrice + '元'
                      : '-'
                }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.specs || '-' }}
                </template>
                {{ item.specs || '-' }}
              </a-tooltip>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  item.schemeTotalPrice
                    ? '¥' + formatNumberThousands(item.schemeTotalPrice)
                    : item.demandTotalPrice
                      ? '¥' + formatNumberThousands(item.demandTotalPrice)
                      : '-'
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.schemeTotalPrice || item.demandTotalPrice">
                {{ (item.schemeTotalPrice || item.demandTotalPrice) + '(元/总预算)' }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="common_table_divide"></div>
      <!-- 右侧 -->
      <div class="common_table_r">
        <div class="scheme_plan_table" v-for="(item, idx) in newSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '其他' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.itemName || '-' }}
                </template>
                {{ item.itemName || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.num || '-' }}
                </template>
                {{ item.num || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.unit || '-' }}
                </template>
                {{ item.unit || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.billTotalPrice ? item.billTotalPrice + '元' : '-' }}
                </template>
                {{ item.billTotalPrice ? item.billTotalPrice + '元' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.specs || '-' }}
                </template>
                {{ item.specs || '-' }}
              </a-tooltip>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value" id="orderbillTotalPrice">
                <!-- 查看模式：直接展示金额 -->
                <div v-if="readonly" class="readonly_price_display">
                  {{ item.billTotalPrice ? '¥' + formatNumberThousands(item.billTotalPrice) : '-' }}
                </div>
                <!-- 编辑模式：输入框 -->
                <a-input-number v-else v-model:value="item.billTotalPrice" @change="changeTotalPrice(idx)"
                  placeholder="" :bordered="false" :controls="false" :min="0.01" :max="999999.99" :precision="2"
                  style="width: 100%" allow-clear
                  :class="[isVerifyFailed && !item.billTotalPrice ? 'error_price_tip' : '']" />
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div v-if="item.billTotalPrice">
                {{ item.billTotalPrice + '(元/' + (readonly ? '总预算' : '账单总额') + ')' }}
              </div>
            </div>
          </div>
        </div>

        <div v-show="subtotal" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_vehicle {
  .scheme_plan_img {
    background: url('@/assets/image/demand/demand_material.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
    background-position: left center;
  }

  // 总额输入框样式
  .scheme_plan_price_value {
    :deep(.ant-input-number) {
      border: none;
      box-shadow: none;
      border-bottom: 1px solid #4e5969;

      .ant-input-number-input {
        height: 24px;
        padding: 0 5px;
        text-align: right;
        width: 100%;
        font-weight: 500;
        font-size: 14px;
        color: #1868db;
        border: none;
        background: transparent;

        &::placeholder {
          color: #bfbfbf;
        }
      }
    }
  }

  // 左侧总额展示样式（与礼品方案保持一致）
  .scheme_plan_price_value {
    height: 24px;
    padding: 0 5px;
    text-align: right;
    width: 100%;
    font-weight: 500;
    font-size: 14px;
    color: #1868db;
    border: none;
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .p0 {
    padding: 0 !important;
  }

  // 只读模式下的金额展示样式
  .readonly_price_display {
    height: 24px;
    padding: 0 5px;
    text-align: right;
    width: 100%;
    font-weight: 500;
    font-size: 14px;
    color: #1868db;
    border: none;
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}

.error_price_tip {
    border-bottom: 2px solid #ff4d4f !important;
}
</style>
