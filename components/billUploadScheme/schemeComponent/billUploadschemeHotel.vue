<script setup lang="ts">
// 方案互动-酒店需求方案
import { ref, watch, defineProps } from 'vue';
import { hotelLevelAllConstant } from '@haierbusiness-front/common-libs';

const props = defineProps({
  hotels: {
    type: Array,
    default: [],
  },
  schemeHotels: {
    type: Array,
    default: [],
  },
  schemeType: {
    // 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
    type: String,
    default: '',
  },
  showBindingScheme: {
    type: Boolean,
    default: true,
  },
  merchantType: {
    type: Number,
    default: null,
  },
});

// 左侧数据（深拷贝）
const leftData = ref([]);
// 右侧数据（深拷贝）
const rightData = ref([]);

// 深拷贝函数
const deepClone = (obj: any): any => {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map((item: any) => deepClone(item));
  if (typeof obj === 'object') {
    const clonedObj: any = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
};

watch(
  () => [props.hotels, props.schemeHotels],
  () => {
    // 根据 schemeType 决定显示哪个数据源
    let dataSource = [];
    if (props.schemeType === 'billUpload') {
      // 账单上传模式，优先显示方案酒店数据，如果没有则显示需求酒店数据
      dataSource = props.schemeHotels.length > 0 ? props.schemeHotels : props.hotels;
    } else {
      // 其他模式，根据现有逻辑
      dataSource = props.schemeHotels.length > 0 ? props.schemeHotels : props.hotels;
    }

    // 使用深拷贝创建左右两份数据
    leftData.value = deepClone(dataSource);
    rightData.value = deepClone(dataSource);

  },
  {
    immediate: true,
    deep: true,
  },
);

// 酒店表格列定义
const hotelColumns = [
  {
    name: 'schemeIndex',
    dataIndex: '1',
    width: 72,
    ellipsis: true,
    align: 'center',
    className: 'interact_table_bgc_gray',
    customRender: ({ index }: any) => {
      return '酒店' + (index + 1);
    },
  },
  {
    title: 'schemeTitle',
    dataIndex: '2',
    ellipsis: true,
    customRender: ({ record }: any) => {
      return record.hotelName || '-';
    },
  },
  {
    title: 'schemeValue',
    dataIndex: '3',
    ellipsis: true,
    customRender: ({ record }: any) => {
      return hotelLevelAllConstant.ofType(record.level)?.desc || '-';
    },
  },
];


</script>

<template>
  <!-- 酒店需求方案 -->
  <div class="scheme_hotel">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>酒店需求方案</span>
    </div>

    <div class="common_table mt16">
      <!-- 左侧表格 -->
      <div class="common_table_l" v-show="showBindingScheme">
        <a-table
          :columns="hotelColumns"
          :data-source="leftData"
          bordered
          :show-header="false"
          size="small"
          :pagination="false"
        >
        </a-table>
      </div>

      <!-- 分隔线 -->
      <div class="common_table_divide"></div>

      <!-- 右侧表格 -->
      <div class="common_table_r">
        <a-table
          :columns="hotelColumns"
          :data-source="rightData"
          bordered
          :show-header="false"
          size="small"
          :pagination="false"
        >
        </a-table>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_hotel {
  .action_icons {
    .add_icon,
    .del_icon {
      width: 16px;
      height: 40px;

      cursor: pointer;
      background: url('@/assets/image/common/add_gray.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
      background-position: center;
    }
    .del_icon {
      background: url('@/assets/image/common/del_red.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
      background-position: center;
    }
  }
}
</style>
