<template>
  <div class="thinking-process">
    <!-- 折叠按钮和思考时间 -->
    <div class="process-header" @click="toggleCollapse">
      <div class="collapse-icon">
        <div class="triangle" :class="{ rotated: isCollapsed }"></div>
      </div>
      <div class="process-label">
        思考过程
        <span v-if="executionDuration">（{{ executionDuration }}秒）</span>
        <!-- 添加的loading效果 -->
        <span v-if="showThinking" class="header-loading">
          <span class="loading-dot"></span>
          <span class="loading-dot"></span>
          <span class="loading-dot"></span>
        </span>
      </div>
    </div>

    <!-- 思考内容区域 -->
    <div v-if="!isCollapsed" class="process-content">
      <div class="thinking-content" v-html="thinkingContent"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref,computed } from 'vue';

const props = defineProps({
  thinkingContent: String,
  thinkingLoading: Boolean,
  executionDuration: Number
});
// 计算是否显示思考过程
const showThinking = computed(() => {
  return props.thinkingLoading;
});
const isCollapsed = ref(false);

// 切换折叠状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};
</script>

<style scoped lang="less">
.thinking-process {
  border-left: 3px solid #5a6268; /* 左侧竖线 */
  background-color: #f8f9fa; /* 暗淡背景 */
  border-radius: 0 4px 4px 0; /* 右侧圆角 */
  margin: 0 0 16px 2px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.process-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  background-color: #e9ecef; /* 头部稍深背景 */
  border-bottom: 1px solid #dee2e6; /* 分隔线 */

  &:hover {
    background-color: #dee2e6;
  }
}

.collapse-icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;

  .triangle {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 5px 0 5px 8px;
    border-color: transparent transparent transparent #495057;
    transition: transform 0.3s ease;

    &.rotated {
      transform: rotate(90deg);
    }
  }

  .icon {
    position: relative;
    display: inline-block;
    width: 14px;
    height: 14px;

    .line {
      position: absolute;
      background-color: #495057;
      transition: all 0.3s ease;

      &:first-child {
        top: 50%;
        left: 0;
        width: 100%;
        height: 2px;
        transform: translateY(-50%);
      }

      &:last-child {
        top: 0;
        left: 50%;
        width: 2px;
        height: 100%;
        transform: translateX(-50%);
      }
    }

    &.rotated {
      .line:last-child {
        opacity: 0;
        transform: translateX(-50%) rotate(90deg);
      }
    }
  }
}

.process-label {
  font-size: 13px;
  color: #495057; /* 暗灰色文字 */
  font-weight: 500;
}

.process-content {
  padding: 12px 15px 15px 35px; /* 左侧留出更多空间 */
}

.thinking-content {
  color: #495057; /* 暗灰色文字 */
  font-size: 13px;
  line-height: 1.6;

  /* Markdown内容样式 */
  :deep(p) {
    margin: 8px 0;
  }

  :deep(code) {
    background-color: #e9ecef;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 12px;
  }

  :deep(ul) {
    padding-left: 20px;
    margin: 8px 0;
  }

  :deep(li) {
    margin-bottom: 4px;
  }
}

/* 新增头部loading样式 */
.process-label {
  position: relative; /* 为loading提供定位上下文 */
  font-size: 13px;
  color: #495057;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.header-loading {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  height: 16px;
}

.loading-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #495057;
  display: inline-block;
  animation: loading-bounce 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0.6);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
