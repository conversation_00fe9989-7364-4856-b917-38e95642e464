<template>
  <div>
    <div class="response-content">
      <div v-if="dialogue.dialogueContent" class="markdown-content" v-html="dialogue.dialogueContent"></div>
      <div v-if="dialogue.sql" class="sql-view">
        <a-button type="primary" size="small" @click="toggleSqlModal(true)">查看执行SQL</a-button>
      </div>
    </div>
    <!-- 使用 Modal 展示 SQL -->
    <a-modal
        title="执行SQL"
        :open="visibleSql"
        @cancel="toggleSqlModal(false)"
        :z-index="2000"
        width="800px"
    >
      <div class="sql-modal-content">
        <pre class="sql-code"><code v-html="formattedSql"></code></pre>
      </div>

      <template #footer>
        <div style="display: flex; justify-content: flex-end; align-items: center;">
          <a-button type="primary" size="small" @click="copySql">复制 SQL</a-button>
          <a-button size="small" @click="toggleSqlModal(false)">关闭</a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import {computed, ref} from 'vue';
import {format as sqlFormat} from 'sql-formatter';
import hljs from 'highlight.js';
import 'highlight.js/styles/atom-one-dark.css'
import {message} from "ant-design-vue";

const props = defineProps({
  dialogue: Object,
  ailogohead: String
});

const visibleSql = ref(false);

// 格式化 SQL
const formattedSql = computed(() => {
  if (!props.dialogue?.sql) return '';
  const formatted = sqlFormat(props.dialogue.sql, {
    language: 'sql',
    indent: '  ',
    uppercase: true,
  });
  return hljs.highlight(formatted, {language: 'sql'}).value;
});
const copySql = () => {
  const sql = props.dialogue?.sql;
  if (!sql) return;

  navigator.clipboard.writeText(sql).then(() => {
    message.success('SQL 已复制到剪贴板');
  }).catch(err => {
    console.error('复制失败:', err);
    message.error('复制失败，请手动选择复制');
  });
}
const toggleSqlModal = (visible: boolean) => {
  visibleSql.value = visible;
};
</script>

<style scoped lang="less">
@import url('../../../node_modules/highlight.js/styles/atom-one-dark.css');

.sql-code {
  background-color: #1e1e1e;
  color: #d4d4d4;
  padding: 12px;
  border-radius: 6px;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 500px;
  overflow-x: auto;
  font-size: 14px;
}

.response-content {
  flex: 1;

  .markdown-content {
    padding: 4px 10px 4px 36px;
    text-align: left;
  }

  .sql-view {
    margin-left: 2px;
    margin-bottom: 15px;
  }
}
</style>
