<template>
  <a-popover v-model:open="visible" class="city-popover" trigger="click" :destroyTooltipOnHide="true"
    placement="bottomLeft">
    <template #title>
      <a-row justify="space-between">
        <a-col v-if="!props.defaultValue">
          支持城市、机场检索
        </a-col>
        <a-col v-if="props.showInternational">
          <a-radio-group v-model:value="cityParams.internationalFlag" button-style="solid" size="small"
            @change="radioChange">
            <a-radio-button style="font-size: 12px;" :value="0">国内城市</a-radio-button>
            <a-radio-button style="font-size: 12px;" :value="1" @click="getIninternationalList">国际城市</a-radio-button>
          </a-radio-group>
        </a-col>
      </a-row>
    </template>
    <template #content>
      <div class="city-main-box">
        <!-- 输入检索 -->
        <a-input v-if="!props.defaultValue" v-model:value="searchValue" allowClear autocomplete="off"
          placeholder="输入机场名称检索" />

        <!-- 机场数据 -->
        <div class="search-value-list" v-if="airportList?.length > 0 || nearbyList?.length > 0">
          <div v-if="!props.defaultValue" @click="back" class="pointer"
            style=" margin-top:5px;  color: #3983e5;  display: flex; flex-flow: row-reverse;">返回城市选择</div>


          <template v-if="airportList?.length > 0">
            <a-list size="small" :data-source="airportList">
              <template #renderItem="{ item, index }">
                <a-list-item class="pointer" style="display: flex; align-items: flex-start; flex-direction: column;"
                  @mouseenter="mouseenter(index)" @mouseleave="mouseleave" @click="chosedAirPort(item)">
                  <div style="display: flex;     width: 100%; flex-direction: column; ">
                    <div style="padding:5px;" :class="activeIndex == index && activeIndex2 == undefined ? 'active' : ''">
                      <span class="mr-20">{{ item.cityName }}(全部机场)</span>
                      <span>{{ item.threeCharacterCode }}</span>
                    </div>
                    <div @mouseenter="mouseenter2(index2)" @mouseleave="mouseleave2" @click.stop="chosedAirPort(item2)"
                      :class="activeIndex == index && activeIndex2 == index2 ? 'active' : ''" style="padding: 5px 20px ;"
                      v-for="(item2, index2) in item.airportList" :key="index2">
                      <span class="mr-20">{{ item2.cityName }}</span>
                      <span class="mr-20">{{ item2.name }}</span>
                      <span>{{ item2.threeCharacterCode }}</span>
                    </div>
                  </div>

                </a-list-item>
              </template>
            </a-list>
          </template>


          <template v-if="nearbyList?.length > 0">
            <a-list size="small" :data-source="nearbyList">
              <template #renderItem="{ item, index }">
                <a-list-item style="padding-top: 5px; padding-bottom: 5px" class="pointer"
                  :class="activeAirIndex == index ? 'active' : ''" @mouseenter="mouseenterAir(index)"
                  @mouseleave="mouseleaveAir" @click="chosedAirPort(item)">

                  <div style="padding: 0px 20px ;">
                    <span class="mr-20" style="color: #999;">临近机场</span>

                    <span class="mr-20">{{ item.cityName }}</span>
                    <span class="mr-20">{{ item.name }}</span>
                    <span>{{ item.threeCharacterCode }}</span>
                  </div>
                  <div>{{ item.nearbyDistance }}公里</div>
                </a-list-item>
              </template>
            </a-list>
          </template>


        </div>


        <template v-else>
          <!-- 输入检索列表 -->
          <div class="search-value-list" v-if="searchList?.length > 0">
            <a-list size="small" :data-source="searchList" :pagination="listPagination">
              <template #renderItem="{ item, index }">
                <a-list-item class="pointer" :class="activeIndex == index ? 'active' : ''" @mouseenter="mouseenter(index)"
                  @mouseleave="mouseleave" @click="choseCity(item)">
                  <div>{{ item.name }}</div>
                  <div>{{ item.provinceName }}</div>
                </a-list-item>
              </template>
            </a-list>
          </div>

          <div v-else>
            <!-- 国内城市 -->
            <a-tabs v-model:activeKey="activeKey" v-if="!cityParams.internationalFlag">
              <a-tab-pane v-for="tab in cityOptions" :key="tab.key" :tab="tab.label">
                <div class="city-list">
                  <div class="city-box" v-show="city?.children?.length > 0" v-for="(city, index) in tab?.children"
                    :key="index">
                    <div class="box-left">{{ city.label }}</div>
                    <div class="box-right">
                      <a-button v-for="(item, index) in city?.children" :key="index" @click="choseCity(item)"
                        type="text">{{
                          item.name
                        }}</a-button>
                    </div>
                  </div>
                </div>
              </a-tab-pane>
            </a-tabs>

            <!-- 国际城市 -->
            <a-tabs v-model:activeKey="internationalKey" v-else>
              <a-tab-pane v-for="tab in internationalTab" :key="tab.key" :tab="tab.label">
                <div class="box-right">
                  <a-button v-for="(item, index) in ininternationalList" :key="index" @click="choseCity(item)"
                    type="text">{{
                      item.name
                    }}</a-button>
                </div>
              </a-tab-pane>
            </a-tabs>
          </div>
        </template>


      </div>
    </template>
    <a-input :style="{ width: props.width }" class="city-chose-input" allowClear readonly :bordered="props.bordered"
      v-model:value="props.value" :placeholder="props.placeholder" />
  </a-popover>
</template>

<script setup lang="ts">


import { computed, onMounted, reactive, ref, watch, watchEffect, toRefs, createVNode } from 'vue';
import { cityApi } from '@haierbusiness-front/apis';
import { DataType, usePagination, useRequest } from 'vue-request';
import { CityResponse, CityItem, CityParams, CityOption } from '@haierbusiness-front/common-libs';
import { useCityStore } from '@haierbusiness-front/utils/src/store/city';
// 自定义组件
import { Form } from 'ant-design-vue';
const formItemContext = Form.useInjectFormItemContext();

const emit = defineEmits(['chosedCity']);
interface Props {
  width: string | number
  value?: string
  placeholder?: string
  bordered?: boolean
  index?: number
  i?: number
  // 是否可选择国际城市.默认为true
  showInternational?: boolean

  // 是否只选择国际城市.默认为false
  onlyInternational?: boolean
  defaultValue?: string;
}

const props = withDefaults(defineProps<Props>(), {
  value: '',
  bordered: true,
  placeholder: '请选择城市',
  index: 0,
  i: 0,
  width: '200px',
  showInternational: true,
  onlyInternational: false,
  defaultValue: ''

});


const listPagination = {
  current: 1,
  defaultPageSize: 10,
  pageSize: 10,
  total: 0,
  size: 'small',
  showSizeChanger: false,
  onChange: (page: number) => {
    console.log(9999, page)
    listPagination.current = page
    listDistrictBySyRun()
  },
}

// 机场数据
const airportList = ref<Array<any>>([])
const nearbyList = ref<Array<any>>([])

// 清除机场列表
const back = () => {
  airportList.value = []
  nearbyList.value = []
}

const airListPagination = {
  current: 1,
  defaultPageSize: 10,
  pageSize: 10,
  total: 0,
  size: 'small',
  showSizeChanger: false,
  onChange: (page: number) => {
    console.log(9999, page)
    listPagination.current = page
    listDistrictBySyRun()
  },
}

// 国际城市---
const internationalKey = ref<number>(0)
const internationalTab = ref([
  {
    key: 0,
    label: '国内热门',
    value: '1'

  },

  {
    key: 1,
    label: '亚洲',
    value: '2'
  },
  {
    key: 2,
    label: '美洲',
    value: '3'
  },

  {
    key: 3,
    label: '欧洲',
    value: '4'

  },
  {
    key: 4,
    label: '大洋洲',
    value: '5'

  },
  {
    key: 5,
    label: '非洲',
    value: '6'

  },

  {
    key: 6,
    label: '国际/中国港澳台热门',
    value: '7'

  },

])

const ininternationalList = ref([])

const getIninternationalList = () => {
  const params = {
    level: 'city',
    internationalFlag: 1,
    districtPopularId: internationalTab.value[internationalKey.value].value
  }
  cityApi.getCityList(params).then(res => {
    ininternationalList.value = res.records
  })
}

// ----

const activeIndex = ref<number>()
const activeIndex2 = ref<bumber>()
const activeAirIndex = ref<number>()
const searchValue = ref<string>('');
const visible = ref<boolean>(false);
const activeKey = ref<number>(999);

// const searchList = ref<Array<object>>([]);

const cityParams = ref<CityParams>({
  level: 'city',
  internationalFlag: 0,
  providerCode: 'VETECH'
})

const cityOptions = ref<Array<CityOption>>([])
const cityObj = ref({})

const getCityList = async () => {
  // const store = useCityStore();
  // const { city } = store

  // if (city?.length > 0) {
  //   cityOptions.value = city
  //   return
  // }

  // 如果是只展示国际
  if (props.onlyInternational) {

    getIninternationalList()
    return
  }

  const params = {
    level: 'city',
    internationalFlag: 1,
    districtPopularId: 1,
    providerCode: 'VETECH',
  }
  const cityData = await cityApi.getCityList(params)


  cityApi.getCityList(cityParams.value).then(res => {
    cityObj.value = groupByForEach(res.records, 'initial')
    const cityInitialArr = Object.keys(cityObj.value)
    cityInitialArr.sort(function (a, b) {
      return a.localeCompare(b);
    });
    cityOptions.value = chunkArray(cityInitialArr, 3, cityData.records)
    // store.setDate(cityOptions.value)
  })
}

// 分组拼接国内城市数据-------

// 数组分层
const chunkArray = (array, chunkSize: number, cityData: any) => {
  let result = [
    {
      key: 999,
      label: '热门',
      children: [{
        label: "热门",
        children: cityData
      }]
    }
  ];
  for (let i = 0; i < array.length; i += chunkSize) {
    result.push({
      key: i,
      label: array.slice(i, i + chunkSize).join(' '),
      children: getChildrenByList(array.slice(i, i + chunkSize))
    });
  }
  return result;
}


// 根据分类数组填充子类数据
const getChildrenByList = (arr) => {
  let children = []
  arr.forEach(item => {
    children.push(
      {
        label: item,
        children: cityObj.value[item]
      }
    )
  })
  return children
}

// 城市数据分组
const groupByForEach = (arr: Array<CityItem>, prop: string) => {
  const grouped = {};
  arr.forEach(item => {
    const key = item[prop];
    if (!grouped[key]) {
      grouped[key] = [];
    }
    grouped[key].push(item);
  });
  return grouped;
}



const searchList = ref<Array<CityResponse>>([])

const listDistrictBySyRun = () => {
  const params = {
    name: searchValue.value,
    pageSize: 8,
    pageNum: listPagination.current,
    level: 'city',
    internationalFlag: cityParams.value.internationalFlag,
    providerCode: 'VETECH'

  }
  cityApi.getCityList(params).then(res => {
    listPagination.total = res.total
    searchList.value = res.records
  })
}

const radioChange = () => {
  searchList.value = []
  searchValue.value = ''
}


const choseCity = async (city: any) => {
  // 根据城市id获取机场数据
  getAirportList(city.id)

};

const chosedAirPort = (airport: any) => {
  searchValue.value = '';
  airportList.value = [];
  nearbyList.value = []
  searchList.value = [];
  visible.value = false;
  emit('chosedCity', airport, props.index, props.i);
}

// 根据城市id获取机场数据
const getAirportList = (cityIds: string) => {
  cityApi.getAirportByCityId({ cityIds, isNeedNearby: true, domesticInternationalType: cityParams.value.internationalFlag }).then(res => {

    airportList.value = res
    nearbyList.value = res[0]?.nearbyList || []


  })
}

// 根据城市id获取机场数据 不带临近机场
const getAirportList2 = (cityIds: string) => {
  cityApi.getAirportByCityId({ cityIds, isNeedNearby: false, domesticInternationalType: cityParams.value.internationalFlag }).then(res => {

    airportList.value = res
    nearbyList.value = res[0]?.nearbyList || []

  })
}

// 根据关键字模糊查询机场数据
const getAirportListByName = (keyWord: string) => {
  cityApi.getAirportByCityId({ keyWord, isNeedNearby: true, domesticInternationalType: cityParams.value.internationalFlag }).then(res => {
    res = res.splice(0, 4)
    airportList.value = res
    // res.map(item => {
    //   airportList.value = [...airportList.value, ...item.airportList]
    // })
    // nearbyList.value = res[0]?.nearbyList || []
  })
}

const mouseenter = (index: number) => {
  activeIndex.value = index
}

const mouseleave = () => {
  activeIndex.value = undefined
}

const mouseenter2 = (index: number) => {
  activeIndex2.value = index
}

const mouseleave2 = () => {
  activeIndex2.value = undefined
}

const mouseenterAir = (index: number) => {
  activeAirIndex.value = index
}

const mouseleaveAir = () => {
  activeAirIndex.value = undefined
}

onMounted(() => {
  getCityList()
})
watch(
  internationalKey,
  () => {
    getIninternationalList()
  },
)


watch(
  props,
  (newValue) => {
    if (newValue.onlyInternational) {
      cityParams.value.internationalFlag = 1
    }
    // 如果存在,只能展示当前城市下的机场数据
    if (newValue.defaultValue) {
      getAirportList2(newValue.defaultValue)
    }
  },

  {
    deep: true,
    immediate: true
  }
)

watch(
  visible,
  (newValue) => {
    if (props.defaultValue) {
      // airportList.value = [];
    } else {
      airportList.value = [];
      searchValue.value = '';
      nearbyList.value = []
      searchList.value = [];
    }
  }
)

watch(
  searchValue,
  (newVal) => {
    airportList.value = []
    nearbyList.value = []
    if (!newVal) {
      searchList.value = [];

      return;
    }
    listPagination.current = 1
    getAirportListByName(newVal);
  },
  {
    deep: true,
  },
);
</script>

<style scoped lang="less">
.mr-20 {
  margin-right: 20px;
}

.city-main-box {
  width: 600px;

  .pointer {
    cursor: pointer;
  }
}

.city-list {
  .city-box {
    display: flex;
    border-bottom: 1px solid #eee;

    .box-left {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 10%;
      color: orange;
      font-size: 20px;
    }

    .box-right {
      display: flex;
      flex-wrap: wrap;
      flex: 1;
    }
  }
}

.active {
  background: #829efe;
  color: #fff;
}</style>