<template>
    <div style="border: 1px solid #ccc;z-index:20;">
      <Toolbar
          style="border-bottom: 1px solid #ccc"
          :editor="editorRef"
          :editorId="editorId"
          :default-config="toolbarConfig"
      />
      <Editor
          style="overflow-y: hidden;"
          v-model="valueHtml"
          :editorId="editorId"
          :defaultConfig="editorConfig"
          :style="editorStyle"
          @on-change="handleChange"
          @on-created="handleCreated"
      />
    </div>
  </template>
  
  <script lang="ts" setup>
  import { onBeforeUnmount, ref, shallowRef, watch, unref, computed, nextTick, isReadonly } from 'vue'
  import type { PropType } from 'vue'
  import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
  import type { IDomEditor, IEditorConfig } from '@wangeditor/editor'
  import {message} from 'ant-design-vue'
  import { isNumber } from 'lodash-es'
  
  type InsertFnType = (url: string, alt: string, href: string) => void
  
  // 编辑器实例，必须用 shallowRef
  const editorRef = shallowRef<IDomEditor>()
  
  const props = defineProps({
    editorId: {
      type: String,
      default: 'wangeEditor-1'
    },
    height: {
      type: [Number, String],
      default: '500px'
    },
    editorConfig: {
      type: Object as PropType<IEditorConfig>,
      default: () => undefined
    },
    modelValue: {
      type: String,
      default: ''
    },
    uploadUrl: {
      type: String,
      default: ''
    },
    isReadonly:{
      type: Boolean,
      default: false
    }
  })

  // 内容 HTML
  const valueHtml = ref('')
  
  const emit = defineEmits(['change', 'update:modelValue'])
  
  watch(
      () => props.modelValue,
      (val: string) => {
        if (val === unref(valueHtml)) return
        valueHtml.value = val || '<p><br></p>'
      },
      {
        immediate: true
      }
  )
  
  // 监听
  watch(
      () => valueHtml.value,
      (val: string) => {
        emit('update:modelValue', val)
      }
  )
  
  const handleCreated = (editor: IDomEditor) => {
    editorRef.value = editor // 记录 editor 实例，重要！
    // console.log(editor.getAllMenuKeys())
  }
  
  const toolbarConfig = {
    excludeKeys: [
        'group-video',
        'uploadVideo',
        'insertVideo' // 排除菜单组，写菜单组 key 的值即可
    ]
  }
  // 编辑器配置
  const editorConfig = computed( (): IEditorConfig => {
    return Object.assign(
        {
          placeholder: '请输入内容...',
          readOnly: props.isReadonly,
          customAlert: (s: string, t: string) => {
            switch (t) {
              case 'success':
                message.success(s)
                break
              case 'info':
                message.info(s)
                break
              case 'warning':
                message.warning(s)
                break
              case 'error':
                message.error(s)
                break
              default:
                message.info(s)
                break
            }
          }, //自定义编辑器 alert
          autoFocus: false,
          scroll: true, // 配置编辑器是否支持滚动
          MENU_CONF: {
            ['uploadImage']: {
              server: props.uploadUrl,
              // 单个文件的最大体积限制，默认为 5M
              maxFileSize: 5 * 1024 * 1024,
              // 最多可上传几个文件，默认为 100
              maxNumberOfFiles: 10,
              // 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
              allowedFileTypes: ['image/*'],
  
              // 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。
              meta: { },
              // 将 meta 拼接到 url 参数中，默认 false
              metaWithUrl: true,
  
              // 自定义增加 http  header
              headers: {
                // 目前上传是上传到集团的接口，不需要传token，如果需要请放开注释
                // Accept: '*',
                // Authorization: 'Bearer ' + appStore.token,
              },
  
              // 跨域是否传递 cookie ，默认为 false
              withCredentials: false,
  
              // 超时时间，默认为 10 秒
              timeout: 5 * 1000, // 5 秒
  
              // form-data fieldName，后端接口参数名称，默认值wangeditor-uploaded-image
              fieldName: 'file',
  
              // 上传之前触发
              onBeforeUpload(file: File) {
                console.log(file)
                return file
              },
              // 上传进度的回调函数
              onProgress(progress: number) {
                // progress 是 0-100 的数字
                console.log('progress', progress)
              },
              onSuccess(file: File, res: any) {
                console.log('onSuccess', file, res)
              },
              onFailed(file: File, res: any) {
                alert(res.message)
                console.log('onFailed', file, res)
              },
              onError(file: File, err: any, res: any) {
                alert(err.message)
                console.error('onError', file, err, res)
              },
              // 自定义插入图片
              customInsert(res: any, insertFn: InsertFnType) {
                insertFn(res?.content?.url, 'image', res.data)
              }
            }
          }
        },
        props.editorConfig || {}
    )
  })
  
  // 编辑器高度设置
  const editorStyle = computed(() => {
    return {
      height: isNumber(props.height) ? `${props.height}px` : props.height
    }
  })
  
  // 回调函数
  const handleChange = (editor: IDomEditor) => {
    emit('change', editor)
  }
  
  // 组件销毁时，也及时销毁编辑器
  onBeforeUnmount(() => {
    const editor = unref(editorRef.value)
    if (editor === null) return
    // 销毁，并移除 editor
    editor?.destroy()
  })
  
  const getEditorRef = async (): Promise<IDomEditor> => {
    await nextTick()
    return unref(editorRef.value) as IDomEditor
  }
  
  defineExpose({
    getEditorRef
  })
  
  
  </script>
  
<style >
  @import './css/style.css';
</style>