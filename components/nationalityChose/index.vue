<template>
    <a-popover
      v-model:open="visible"
      class="city-popover"
      trigger="click"
      :destroyTooltipOnHide="true"
      placement="bottomLeft"
    > 
      <template #title>
        <a-row justify="space-between">
          <a-col>
            关键字查询
          </a-col>
        </a-row>
      </template>
      <template #content>
        <div class="city-main-box">
          <!-- 输入检索 -->
          <a-input v-model:value="searchValue" allowClear autocomplete="off" placeholder="输入城市名称检索" />
          <!-- 输入检索列表 -->
          <div class="search-value-list-box" v-if="searchList?.length > 0">
            <a-list size="small" class="search-value-list" :data-source="searchList">
              <template #renderItem="{ item, index }">
                <a-list-item class="pointer" :class="activeIndex == index ? 'active' :''" @mouseenter="mouseenter(index)" @mouseleave="mouseleave" @click="choseCity(item)">
                  <div>{{ item.mc }}</div>
                  <div>{{ item.by6 }}</div>
                </a-list-item>
              </template>
            </a-list>
          </div>

          <div v-else>
            <!-- 国籍数据 -->
            <a-tabs v-model:activeKey="activeKey" >
              <a-tab-pane v-for="tab in cityOptions" :key="tab.bClassFky.mcDede" :tab="tab.bClassFky.mc">
                <div class="city-list">
                  <div class="city-box" v-for="(py,pyi) in pyList"  :key="pyi">
                    <div class="box-left">{{ py.label }}</div>
                    
                    <div class="box-right">
                      <a-button v-for="(item, index) in tab?.bclassMap[py.label]" :key="index" @click="choseCity(item)" type="text">{{
                        item.mc
                      }}</a-button>
                    </div>
                  </div>
                </div>
              </a-tab-pane>
            </a-tabs>

          </div>
        
        </div>
      </template>
      <a-input :style="{width: props.width}" class="city-chose-input" allowClear readonly :bordered="props.bordered" v-model:value="props.value" :placeholder="props.placeholder" />
    </a-popover>
</template>

<script setup lang="ts">


import { computed, onMounted, reactive, ref, watch, watchEffect, toRefs, createVNode } from 'vue';
import { tripApi, reasonApi,cityApi } from '@haierbusiness-front/apis';

import { DataType, usePagination, useRequest } from 'vue-request';
import { CityResponse, CityItem, CityParams,CityOption } from '@haierbusiness-front/common-libs';
import { useCityStore } from '@haierbusiness-front/utils/src/store/city';
// 自定义组件
import { Form } from 'ant-design-vue';
const formItemContext = Form.useInjectFormItemContext();

const emit = defineEmits(['chosedCity']);
interface Props {
    width: string | number
    value: string
    placeholder?: string
    bordered?: boolean
    index?: number
    i?: number
 
}

const pyList = ref([
  {
    label: 'A-F',
    value: 'A-F'
  },
  {
    label: 'G-M',
    value: 'G-M'
  },
  {
    label: 'N-W',
    value: 'N-W'
  },
  {
    label: 'X-Z',
    value: 'X-Z'
  },
])

const props = withDefaults(defineProps<Props>(), {
    value: '',
    bordered: true,
    placeholder: '请选择国籍/地区',
    index: 0,
    i: 0,
    width: '200px',
});




// ----

const activeIndex = ref<number>()
const searchValue = ref<string>('');
const visible = ref<boolean>(false);
const activeKey = ref<number>(0);

// const searchList = ref<Array<object>>([]);
const cityObj = ref({})

const cityOptions = ref<Array<CityOption>>([])

const queryTripNationality = () => {

  tripApi.queryTripNationality().then(res => {
    cityOptions.value = res
    activeKey.value = cityOptions.value[0].bClassFky.mcDede
  })
}


const searchList = ref<Array<CityResponse>>([])

const listDistrictBySyRun = () => {
  tripApi.queryTripNationalityByKeyword(searchValue.value).then(res => {
    searchList.value = res
  })
}



const choseCity = (city: CityItem) => {

  // 暂时不转换 使用id
  searchList.value = [];
  visible.value = false;
  searchValue.value = '';
  emit('chosedCity', city);
  
};

const mouseenter = (index: number) => {
  activeIndex.value = index
}

const mouseleave = () => {
  activeIndex.value = undefined
}

onMounted(() => {
  queryTripNationality()
})


watch(
  searchValue,
  (newVal) => {
    if (!newVal) {
      searchList.value = [];
      return;
    }
    listDistrictBySyRun();
  },
  {
    deep: true,
  },
);
</script>

<style scoped lang="less">
.city-main-box {
  width: 600px;
  .pointer{
    cursor: pointer;
  }
}
.city-list {
  .city-box {
    display: flex;
    border-bottom: 1px solid #eee;
    .box-left {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 10%;
      color: orange;
      font-size: 20px;
    }
    .box-right {
      display: flex;
      flex-wrap: wrap;
      flex: 1;
    }
  }
}
.active {
  color:#1e65df ;
}
.search-value-list-box,.search-value-list {
  height: 400px;
  overflow: auto;
}
</style>