{"name": "@haierbusiness-front/components", "private": true, "version": "0.0.0", "type": "module", "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@haierbusiness-front/apis": "workspace:^0.0.0", "@haierbusiness-front/common-libs": "workspace:^0.0.0", "@haierbusiness-front/components": "workspace:^0.0.0", "@haierbusiness-front/utils": "workspace:^0.0.0", "@microsoft/fetch-event-source": "^2.0.1", "@vueuse/core": "^9.13.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "ant-design-vue": "4.x", "dayjs": "1.11.9", "echarts": "^5.4.3", "highlight.js": "^11.11.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "marked": "^15.0.12", "pinia": "^2.0.33", "sql-formatter": "^15.6.6", "uuid": "^11.1.0", "vue": "^3.2.45", "vue-echarts": "^6.6.1", "vue-request": "2.0.0-rc.4", "vue-router": "4", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.0.0", "typescript": "^4.9.3", "vite": "^4.1.0", "vue-tsc": "^1.0.24"}}