<script setup lang="ts">
import { Progress as hProgress, BreadcrumbItem as hBreadcrumbItem, Breadcrumb as hBreadcrumb, LayoutSider as hLayoutSider, LayoutFooter as hLayoutFooter, LayoutContent as hLayoutContent, Layout<PERSON>eader as hLayoutHeader, Layout as hLayout, MenuItem as hMenuItem, MenuItemGroup as hMenuItemGroup, SubMenu as hSubMenu, Menu as hMenu, <PERSON>vider as hDivider, Space as hSpace, Button as hButton, Col as hCol, Result as hResult, Row as hRow, TabPane as hTabPane, Tabs as hTabs, message } from 'ant-design-vue';
import { onMounted, ref, watch } from 'vue';
import { UploadOutlined, UserOutlined, NotificationOutlined, AppstoreOutlined, MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons-vue';
const props = defineProps({
    time: Number,
    loading: Boolean
})
const loadInt = ref()
const percent = ref(0)
const time = props.time

const loadingFlag = ref(false)
watch(() => props.loading, (n: any, o: any) => {
    if (n === true) {
        loadingFlag.value = true
        eLoadingStart()
    } else {
        eLoadingEnd();
        setTimeout(() => {
            loadingFlag.value = false
        }, 500);
    }
});

const eLoadingStart = () => {
    let count = 0;
    let step = 1;
    loadInt.value = setInterval(() => {
        if (percent.value < 40) {
            count += 50;
            step = 5;
        } else if (percent.value < 60) {
            count += 35;
            step = 4;
        } else if (percent.value < 80) {
            count += 20;
            step = 2;
        } else if (percent.value < 90) {
            count += 10;
            step = 1;
        } else if (percent.value < 95) {
            count += 3;
            step = 1;
        } else {
            count += 1;
            step = 1;
        }
        if (count >= 100) {
            count = 0;
            while (step >= 1) {
                if (percent.value !== 99) {
                    percent.value++;
                } else {
                    clearInterval(loadInt.value);
                }
                step--;
            }
            step;
        }
    }, 100);
}

const eLoadingEnd = () => {
    percent.value = 100;
    clearInterval(loadInt.value);
    setTimeout(() => {
        percent.value = 0;
    }, 500);
}
</script>

<template>
    <div v-if="loadingFlag" class="masking" style="padding-top: 15%;padding-left: 40%;">
        <div style="height: 160px;width: 160px;background-color: #ffffff;border-radius: 80px;">
            <h-progress :stroke-color="{
                '0%': '#0073E5',
                '100%': '#87d068',
            }" style="padding-left: 20px;padding-top: 20px;" type="circle" :percent="percent" />
        </div>
    </div>
</template>

<style scoped lang="less">
.masking {
    background-color: #24232385;
    position: fixed;
    z-index: 10000;
    height: 100%;
    width: 100%;
}
</style>