<script setup lang="ts">
// 方案变更-变更申请说明资料
import { message, Upload } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import { fileApi } from '@haierbusiness-front/apis';

const props = defineProps({
  schemeChangeType: {
    // schemeBargainingEdit - 方案议价, schemeBargainingView - 议价查看, schemeChangeEdit - 方案变更, schemeChangeView - 方案变更查看, schemeWinBidView - 方案中标查看
    type: String,
    default: '',
  },
  schemeInfo: {
    type: Object,
    default: {},
  },
  schemeCacheInfo: {
    type: Object,
    default: {},
  },
  bargainSchemeInfo: {
    type: Object,
    default: {},
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  isSchemeBargain: {
    // 是否议价
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['schemeFileEmit', 'schemeReasonEmit']);

const uploadLoading = ref<boolean>(false);
const isLt50M = ref<boolean>(true);
const attachmentList = ref<array>([]);
const schemeChangeReason = ref<string>('');

const isVerifyFailed = ref<boolean>(false); // 校验是否失败

// 上传变更申请说明资料 - 删除
const handleRemove: UploadProps['onRemove'] = (file) => {};

const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  isLt50M.value = file.size / 1024 / 1024 < 50;

  if (!isLt50M.value) {
    message.error('文件最大不超过50M！');
    return Upload.LIST_IGNORE;
  }

  return isLt50M.value;
};

// 上传附件
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const uploadRequest = (options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;

      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 锚点
const anchorJump = (id: string) => {
  document.getElementById(id).scrollIntoView({ behavior: 'smooth', block: 'center' });
};

const fileClick = (url: string) => {
  window.open(url);
};

// 暂存
const serviceFileTempSave = () => {
  let paths = [];

  attachmentList.value.forEach((e) => {
    const params = {
      name: e.name,
      url: e.filePath || e.url,
    };
    paths.push(JSON.stringify(params));
  });

  emit('schemeFileEmit', [...paths]);
  emit('schemeReasonEmit', schemeChangeReason.value);
};

// 校验
const serviceFileSub = () => {
  isVerifyFailed.value = true;

  let isVerPassed = true;

  // if (!attachmentList.value || attachmentList.value.length === 0) {
  //   message.error('请上传变更申请说明资料！');

  //   isVerPassed = false;
  //   anchorJump('schemeAttachmentId');
  //   return;
  // }

  if (!schemeChangeReason.value) {
    message.error('请填写变更原因！');

    isVerPassed = false;
    anchorJump('schemeAttachmentId');
    return;
  }

  if (isVerPassed) {
    serviceFileTempSave();
  }

  return isVerPassed;
};

defineExpose({ serviceFileSub, serviceFileTempSave });

onMounted(async () => {
  // console.log('%c [ 变更申请说明 ]-24', 'font-size:13px; background:pink; color:#bf2c9f;', props.schemeInfo.applyAttachment);
  if (props.isSchemeCache && props.schemeCacheInfo) {
    // 缓存 - 反显
    const attachment = props.schemeCacheInfo?.applyAttachment || [];

    attachmentList.value = attachment.map((e) => {
      const obj = JSON.parse(e);
      return { name: obj.name, url: obj.url };
    });

    schemeChangeReason.value = props.schemeCacheInfo?.reason || null;
  } else if (props.isSchemeBargain && props.bargainSchemeInfo) {
    // 议价、议价查看
    const attachment = props.bargainSchemeInfo?.applyAttachment || [];

    attachmentList.value = attachment.map((e) => {
      const obj = JSON.parse(e);
      return { name: obj.name, url: obj.url };
    });

    schemeChangeReason.value = props.bargainSchemeInfo?.reason || null;
  } else {
    // 变更查看
    const attachment = props.schemeInfo?.applyAttachment || [];

    attachmentList.value = attachment.map((e) => {
      const obj = JSON.parse(e);
      return { name: obj.name, url: obj.url };
    });

    schemeChangeReason.value = props.schemeInfo?.reason || null;
  }
});
</script>

<template>
  <!-- 变更申请说明资料 -->
  <div class="scheme_file">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>变更申请说明</span>
    </div>

    <div class="scheme_file_list mt16" id="schemeAttachmentId" v-if="schemeChangeType === 'schemeChangeEdit'">
      <div class="scheme_files_label">变更申请附件：</div>
      <div class="scheme_files_value">
        <a-upload
          v-model:fileList="attachmentList"
          :custom-request="uploadRequest"
          :multiple="false"
          :max-count="10"
          :before-upload="beforeUpload"
          @remove="handleRemove"
        >
          <!-- accept=".rar, .zip, .pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx" -->
          <a-button>
            <upload-outlined></upload-outlined>
            上传
          </a-button>
        </a-upload>

        <div :class="['support_extend_tip', 'mt18', isLt50M ? '' : 'err_color']">
          <!-- 支持扩展名：.rar, .zip, .pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx -->
          文件最大不超过50M
        </div>
      </div>
    </div>
    <div class="mt16" v-else>
      <span v-for="(item, index) in attachmentList" :key="item.url">
        <span v-show="index > 0" class="mr8">，</span>
        <a @click="fileClick(item.url)">
          {{ item.name }}
        </a>
      </span>
    </div>

    <div class="scheme_file_list mt20">
      <div class="scheme_files_label">备注：</div>
      <div class="scheme_files_value">
        <a-textarea
          v-model:value="schemeChangeReason"
          :status="isVerifyFailed && !schemeChangeReason ? 'error' : ''"
          style="width: calc(50% - 67px)"
          placeholder="请填写变更原因"
          :maxlength="500"
          allow-clear
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_file {
  .scheme_file_list {
    display: flex;

    .scheme_files_label {
      padding-right: 12px;
      width: 114px;
      text-align: right;
      line-height: 32px;
    }
    .scheme_files_value {
      width: calc(100% - 114px);
    }
  }

  :deep(.ant-input-status-error:not(.ant-input-disabled):not(.ant-input-borderless).ant-input) {
    border-color: #ff5533;
    border-width: 2px;
  }
}
</style>
