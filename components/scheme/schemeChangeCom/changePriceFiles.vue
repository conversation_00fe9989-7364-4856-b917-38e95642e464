<script setup lang="ts">
// 方案变更-价格说明附件
import { message, Upload } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, inject, defineProps, defineEmits } from 'vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import { fileApi } from '@haierbusiness-front/apis';

const props = defineProps({
  schemeChangeType: {
    // schemeBargainingEdit - 方案议价, schemeBargainingView - 议价查看, schemeChangeEdit - 方案变更, schemeChangeView - 方案变更查看, schemeWinBidView - 方案中标查看
    type: String,
    default: '',
  },
  schemeInfo: {
    type: Object,
    default: {},
  },
  schemeCacheInfo: {
    type: Object,
    default: {},
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['schemePriceFileEmit']);

const schemeChangeAddNum = inject('schemeChangeAddNum', 0);

const uploadLoading = ref<boolean>(false);
const isLt50M = ref<boolean>(true);
const attachmentList = ref<array>([]);

const isVerifyFailed = ref<boolean>(false); // 校验是否失败

// 上传说明资料 - 删除
const handleRemove: UploadProps['onRemove'] = (file) => {};

const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  isLt50M.value = file.size / 1024 / 1024 < 50;

  if (!isLt50M.value) {
    message.error('文件最大不超过50M！');
    return Upload.LIST_IGNORE;
  }

  return isLt50M.value;
};

// 上传附件
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const uploadRequest = (options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;

      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 锚点
const anchorJump = (id: string) => {
  document.getElementById(id).scrollIntoView({ behavior: 'smooth', block: 'center' });
};

const fileClick = (url: string) => {
  window.open(url);
};

// 暂存
const serviceFileTempSave = () => {
  let paths = [];

  attachmentList.value.forEach((e) => {
    const params = {
      name: e.name,
      url: e.filePath || e.url,
    };
    paths.push(JSON.stringify(params));
  });

  emit('schemePriceFileEmit', [...paths]);
};

// 校验
const serviceFileSub = () => {
  isVerifyFailed.value = true;

  let isVerPassed = true;

  if (schemeChangeAddNum && (!attachmentList.value || attachmentList.value.length === 0)) {
    message.error('请上传价格说明附件！');

    isVerPassed = false;
    anchorJump('schemeAttachmentId');
    return;
  }

  if (isVerPassed) {
    serviceFileTempSave();
  }

  return isVerPassed;
};

defineExpose({ serviceFileSub, serviceFileTempSave });

onMounted(async () => {
  // console.log('%c [ 价格说明附件 ]-24', 'font-size:13px; background:pink; color:#bf2c9f;', props.schemeInfo.proveAttachment);
  if (props.isSchemeCache && props.schemeCacheInfo) {
    // 缓存 - 反显
    const attachment = props.schemeCacheInfo?.proveAttachment || [];

    attachmentList.value = attachment.map((e) => {
      const obj = JSON.parse(e);
      return { name: obj.name, url: obj.url };
    });
  } else {
    // 附件查看
    const attachment = props.schemeInfo?.proveAttachment || [];

    attachmentList.value = attachment.map((e) => {
      const obj = JSON.parse(e);
      return { name: obj.name, url: obj.url };
    });
  }
});
</script>

<template>
  <!-- 价格说明附件 -->
  <div class="scheme_price_file">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>价格说明附件</span>
    </div>

    <div class="scheme_file_list mt16" id="schemeAttachmentId" v-if="schemeChangeType === 'schemeChangeEdit'">
      <div class="scheme_files_label">价格说明附件：</div>
      <div
        :class="[
          'scheme_files_value',
          isVerifyFailed && schemeChangeAddNum && (!attachmentList || attachmentList.length === 0)
            ? 'scheme_price_file_error'
            : '',
        ]"
      >
        <a-upload
          v-model:fileList="attachmentList"
          :custom-request="uploadRequest"
          :multiple="false"
          :max-count="10"
          :before-upload="beforeUpload"
          @remove="handleRemove"
        >
          <!-- accept=".rar, .zip, .pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx" -->
          <a-button>
            <upload-outlined></upload-outlined>
            上传
          </a-button>
        </a-upload>

        <div :class="['support_extend_tip', 'mt18', isLt50M ? '' : 'err_color']">
          <!-- 支持扩展名：.rar, .zip, .pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx -->
          文件最大不超过50M
        </div>
      </div>
    </div>
    <div class="mt16" v-else>
      <span v-for="(item, index) in attachmentList" :key="item.url">
        <span v-show="index > 0" class="mr8">，</span>
        <a @click="fileClick(item.url)">
          {{ item.name }}
        </a>
      </span>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_price_file {
  .scheme_file_list {
    display: flex;

    .scheme_files_label {
      padding-right: 12px;
      width: 114px;
      text-align: right;
      line-height: 32px;
    }
    .scheme_files_value {
      width: calc(100% - 114px);
    }
  }

  .scheme_price_file_error {
    :deep(.ant-btn-default) {
      border-color: #ff5533;
      border-width: 2px;
    }
  }
}
</style>
