<script setup lang="ts">
// 方案变更
import { message, Modal } from 'ant-design-vue';
import { onMounted, onBeforeUnmount, ref, reactive, provide, inject, defineProps } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { debounce } from 'lodash';

import {
  saveDataBy,
  getDataBy,
  delData,
  resolveParam,
  routerParam,
  numComputedArrMethod,
  meetingProcessOrchestration,
} from '@haierbusiness-front/utils';
import { schemeApi, miceBidManOrderListApi } from '@haierbusiness-front/apis';
import { miceSchemeSubmitRequest, ProcessOrchestrationServiceTypeEnum } from '@haierbusiness-front/common-libs';

import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
const { loginUser } = storeToRefs(applicationStore());

import schemeInfo from './schemeComponent/schemeInfo.vue';
import changeHotel from './schemeChangeCom/changeHotel.vue';
import changePlan from './schemeChangeCom/changePlan.vue';
import changeMaterial from './schemeChangeCom/changeMaterial.vue';
import changePresents from './schemeChangeCom/changePresents.vue';
import changeOther from './schemeChangeCom/changeOther.vue';
import changeServiceFee from './schemeChangeCom/changeServiceFee.vue';
import changeFiles from './schemeChangeCom/changeFiles.vue';
import changePriceFiles from './schemeChangeCom/changePriceFiles.vue';
import changeTotal from './schemeChangeCom/changeTotal.vue';

const route = useRoute();
const router = useRouter();

const isCloseLastTab = inject<Ref<boolean>>('isCloseLastTab');

const props = defineProps({
  userType: {
    // platform,user,manage
    type: String,
    default: 'platform',
  },
});

const schemeContainerRef = ref();
const schemePlanRef = ref(null);
const schemeMaterialRef = ref(null);
const schemePresentRef = ref(null);
const schemeOtherRef = ref(null);
const schemeFeeRef = ref(null);
const schemeFileRef = ref(null);
const schemePriceFileRef = ref(null);

const autoSave = ref(null); // 自动保存
const countdownTimer = ref(null); //
const countdownTime = ref<number>(60);
const cacheLoading = ref<Boolean>(false);
const schemeLoading = ref<Boolean>(false);
const isSchemeCache = ref<Boolean>(false); // 是否缓存
const isSchemeBargain = ref<Boolean>(false); // 是否议价
const subLoading = ref(false); // 完成提报

const loadingEnd = ref(false); // 加载完成

const schemeAbandonReason = ref<string>(''); // 驳回内容反显

const schemePlanObj = ref<miceSchemeSubmitRequest>({}); // 每日计划
const schemeMaterialObj = ref<miceSchemeSubmitRequest>({}); // 布展物料
const schemePresentArr = ref<array>([]); // 礼品
const schemeOtherArr = ref<array>([]); // 其他
const schemeFeeObj = ref<miceSchemeSubmitRequest>({}); // 全单服务费
const schemeFileList = ref<array>([]); // 附件
const schemePriceFileList = ref<array>([]); // 附件
const schemeChangeReason = ref<string>(''); // 方案变更原因

const planPrice = ref<number>(0); // 每日计划 - 金额
const planEachPriceList = ref<array>([]); // 每日计划 - 金额
const materialPrice = ref<number>(0); // 布展物料 - 金额
const presentPrice = ref<number>(0); // 礼品 - 金额
const otherPrice = ref<number>(0); // 其他 - 金额
const totalPrice = ref<number>(0); // 全单服务费方案 - 总金额

const miceId = ref<number>(null);
const miceNewSchemeId = ref<number>(null);
const schemeChangeType = ref<string>(''); // schemeBargainingEdit - 方案议价, schemeBargainingView - 议价查看, schemeChangeEdit - 方案变更, schemeChangeView - 方案变更查看, schemeWinBidView - 方案中标查看

const merchantId = ref<number>(null); // 服务商Id
const merchantType = ref<number>(null); // 服务商类型

const schemeDetail = ref<any>({}); // 方案详情
const schemeCacheDetail = ref<any>({}); // 方案详情-缓存
const bargainSchemeDetails = ref<any>({}); // 方案议价详情

const schemeTotalInfo = ref<any>({}); // 合计详情

const processNode = ref<string>(''); // 流程节点

const demandSets = ref<Array>([]); // 需求配置
const showFee = ref<boolean>(false); // 全单服务费配置
const fullServiceRangeRateLimit = ref<number>(0); // 全单服务费
const fullServiceRemark = ref<string>(''); // 全单服务费
const serviceFeeSets = ref<array>([]); // 全单服务费配置项
const isCateringStandardControl = ref<string>(''); // 是否控制餐标等其他标准配置 - 1:不可修改,2:可以提高,3:可以降低
const meetingMinDaysNum = ref<number>(0); // 会议最短召开日期(单位: 天)

const isShowDel = ref<boolean>(true); // 展示删除按钮

const pdMainId = ref<number>(null);
const pdVerId = ref<number>(null);

const approveCode = ref<string>(''); // 审批流Code
const approvalModalShow = ref<boolean>(false);
const businessProcess = import.meta.env.VITE_BUSINESS_PROCESS_URL;

// 方案变更新增数量
const schemeChangeAddNum = ref(0);
const schemeChangeAddFunc = () => {
  schemeChangeAddNum.value++;
};
const schemeChangeDelFunc = (num = 1) => {
  schemeChangeAddNum.value -= num;
};

provide('schemeChangeAddNum', schemeChangeAddNum);
provide('schemeChangeAddFunc', schemeChangeAddFunc);
provide('schemeChangeDelFunc', schemeChangeDelFunc);

// 缓存查询
const getCache = async () => {
  if (!miceId.value) {
    return;
  }

  cacheLoading.value = true;
  loadingEnd.value = false;

  let resCacheStr = '';

  if (schemeChangeType.value === 'schemeChangeEdit') {
    // 方案变更时，才查询缓存

    resCacheStr = await getDataBy({
      applicationCode: 'haierbusiness-mice-merchant',
      cacheKey:
        'haierbusiness-mice-merchant_' +
        loginUser.value?.username +
        '_schemeChangeKey' +
        miceId.value +
        '_merchantId' +
        merchantId.value, // 方案变更
    });

    if (resCacheStr) {
      console.log(
        '%c [ 缓存查询 ]-171114',
        'font-size:13px; background:pink; color:#bf2c9f;',
        resCacheStr ? JSON.parse(resCacheStr) : resCacheStr,
      );

      isSchemeCache.value = true;
      schemeCacheDetail.value = JSON.parse(resCacheStr);

      schemeChangeAddNum.value = schemeCacheDetail.value.schemeChangeAddNum || 0;
    }
  }

  // 方案详情
  await getSchemeDetails();

  cacheLoading.value = false;
};
const getSchemeDetails = async () => {
  // 方案详情
  schemeLoading.value = true;

  // 议价时，右侧取新的议价方案详情
  if (schemeChangeType.value === 'schemeBargainingEdit') {
    // 方案议价
    await getPriceChangeRecord();

    if (!miceNewSchemeId.value) {
      message.error('未查询到新的议价记录！');
      return;
    }

    // 方案议价
    // 新的议价方案详情
    let bargainRes = [];
    if (props.userType === 'platform') {
      // 服务商端 - 方案详情
      bargainRes = await schemeApi.schemePlatDetails({
        miceId: miceId.value,
        miceSchemeId: miceNewSchemeId.value, // 最新的议价方案Id
      });
    } else {
      // 用户端 - 方案详情
      bargainRes = await schemeApi.getSchemeDetailsByUser({
        miceId: miceId.value,
        miceSchemeId: miceNewSchemeId.value,
      });
    }

    console.log('%c [ 议价方案详情 ]-151', 'font-size:13px; background:pink; color:#bf2c9f;', bargainRes);

    if (bargainRes && bargainRes.length > 0) {
      bargainSchemeDetails.value = bargainRes[0] || [];
      isSchemeBargain.value = true;
    }
  }

  // 中标方案详情
  let res = [];
  if (props.userType === 'platform') {
    // 服务商端 - 方案详情
    res = await schemeApi.schemePlatDetails({
      miceId: miceId.value,
      schemeTypes: '2',
    });
  } else {
    // 用户端 - 方案详情
    res = await schemeApi.getSchemeDetailsByUser({
      miceId: miceId.value,
      schemeTypes: '2',
    });
  }

  console.log('%c [ 中标方案详情 ]-151', 'font-size:13px; background:pink; color:#bf2c9f;', res);

  if (res && res.length > 0) {
    schemeDetail.value = res[0];

    // 流程详情
    await getProcessDetails(
      schemeDetail.value.pdMainId || pdMainId.value,
      schemeDetail.value.pdVerId || pdVerId.value,
      schemeDetail.value.pdmMerchantPoolId,
    );

    processNode.value = schemeDetail.value?.processNode;

    // 方案提报\竞价中 - 展示倒计时
    if (
      ['MICE_PENDING', 'MICE_EXECUTION', 'MICE_COMPLETED'].includes(processNode.value) &&
      props.userType === 'platform' &&
      schemeChangeType.value === 'schemeChangeEdit'
    ) {
      // 1min自动保存
      // countDownOneMin();
      // TODO
    }

    if (
      schemeDetail.value.processNode === schemeDetail.value.reverseProcessNode ||
      schemeDetail.value.processNode === schemeDetail.value.reverseAfterProcessNode
    ) {
      // 驳回内容反显
      schemeAbandonReason.value = schemeDetail.value?.abandonReason || null;
    }
  }

  schemeLoading.value = false;
};

// 日程安排
const schemePlanEmit = (miceSchemeSubData: miceSchemeSubmitRequest) => {
  schemePlanObj.value = { ...miceSchemeSubData };
};
const schemeChangePlanDateEmit = (miceSchemeDate: miceSchemeSubmitRequest) => {
  schemeDetail.value.startDate = miceSchemeDate.startDate;
  schemeDetail.value.endDate = miceSchemeDate.endDate;

  schemeCacheDetail.value.startDate = miceSchemeDate.startDate;
  schemeCacheDetail.value.endDate = miceSchemeDate.endDate;
};
// 布展物料
const schemeMaterialEmit = (materialObj: miceSchemeSubmitRequest) => {
  schemeMaterialObj.value = { ...materialObj };
};
// 礼品
const schemePresentEmit = (presentArr: array) => {
  schemePresentArr.value = [...presentArr];
};
// 其他
const schemeOtherEmit = (otherArr: array) => {
  schemeOtherArr.value = [...otherArr];
};
// 全单服务费
const schemeFeeEmit = (feeObj: miceSchemeSubmitRequest) => {
  schemeFeeObj.value = { ...feeObj };
  schemeTotalInfo.value = { ...schemeTotalInfo.value, ...feeObj };
};
// 附件
const schemeFileEmit = (arr: array) => {
  schemeFileList.value = [...arr];
};
// 附件
const schemePriceFileEmit = (arr: array) => {
  schemePriceFileList.value = [...arr];
};
// 变更原因
const schemeReasonEmit = (reason: string) => {
  schemeChangeReason.value = reason;
};

// 每日计划-方案金额
const planPriceEmit = (priceNum: number) => {
  planPrice.value = priceNum;

  totalPriceFn();
};
// 每日计划-各单项-方案金额
const planEachPriceEmit = (arr: array) => {
  planEachPriceList.value = arr;
};
// 布展物料-方案金额
const materialPriceEmit = (priceNum: number) => {
  materialPrice.value = priceNum;

  totalPriceFn();
};
// 礼品-方案金额
const presentPriceEmit = (priceNum: number) => {
  presentPrice.value = priceNum;

  totalPriceFn();
};
// 其他-方案金额
const otherPriceEmit = (priceNum: number) => {
  otherPrice.value = priceNum;

  totalPriceFn();
};
// 全单服务费方案 - 总金额
const totalPriceFn = debounce(() => {
  totalPrice.value = planPrice.value + materialPrice.value + presentPrice.value + otherPrice.value;

  // 方案暂存 - 合计
  handleTotal();
}, 300);

const handleTotal = debounce(() => {
  // 方案暂存 - 合计
  schemeTemporarily('total');
}, 300);

// 锚点
const anchorJump = (id: string) => {
  document.getElementById(id).scrollIntoView({ behavior: 'smooth', block: 'center' });
};

// 1min自动保存
const countDownOneMin = () => {
  autoSave.value = setInterval(() => {
    // 方案变更
    // 方案暂存
    schemeTemporarily('auto');
  }, 60000);

  countdownTimer.value = setInterval(() => {
    countdownTime.value = countdownTime.value === 0 ? 60 : countdownTime.value - 1;
  }, 1000);
};

// 缓存删除
const delCache = async () => {
  if (!miceId.value) {
    return;
  }

  delData({
    applicationCode: 'haierbusiness-mice-merchant',
    cacheKey:
      'haierbusiness-mice-merchant_' +
      loginUser.value?.username +
      '_schemeChangeKey' +
      miceId.value +
      '_merchantId' +
      merchantId.value, // 方案变更
  });
};

// 暂存
const schemeTemporarily = async (type) => {
  if (!miceId.value) {
    message.error('暂存失败，会议不存在！');
    return;
  }

  // 日程安排
  schemePlanRef.value && schemePlanRef.value.schemePlanTempSave();

  // 布展物料
  schemeMaterialRef.value && schemeMaterialRef.value.materialTempSave();

  // 礼品
  schemePresentRef.value && schemePresentRef.value.presentTempSave();

  // 其他
  schemeOtherRef.value && schemeOtherRef.value.otherTempSave();

  // 全单服务费
  schemeFeeRef.value && schemeFeeRef.value.serviceFeeTempSave();

  // 变更申请说明附件
  schemeFileRef.value && schemeFileRef.value.serviceFileTempSave();

  // 变更申请说明附件
  schemeChangeAddNum.value > 0 && schemePriceFileRef.value && schemePriceFileRef.value.serviceFileTempSave();

  const schemeAllPrice =
    totalPrice.value +
    (schemeFeeObj.value.serviceFee && schemeFeeObj.value.serviceFee.schemeServiceFeeReal
      ? schemeFeeObj.value.serviceFee.schemeServiceFeeReal
      : 0);

  const params = {
    schemeChangeAddNum: schemeChangeAddNum.value, // 方案变更新增数量

    miceId: miceId.value,
    schemeTotalPrice: Number(schemeAllPrice.toFixed(2)), // 方案总金额

    startDate: schemeCacheDetail.value.startDate || schemeDetail.value.startDate,
    endDate: schemeCacheDetail.value.endDate || schemeDetail.value.endDate,

    pdMainId: schemeDetail.value.pdMainId,
    pdVerId: schemeDetail.value.pdVerId,
    pdmMerchantPoolId: schemeDetail.value.pdmMerchantPoolId,
    processNode: processNode.value,

    hotels: [...schemeDetail.value.hotels],
    ...schemePlanObj.value,
    material: {
      ...schemeMaterialObj.value?.schemeMaterial,
    },
    traffic: {},
    presents: [...schemePresentArr.value],
    others: [...schemeOtherArr.value],
    ...schemeFeeObj.value,

    applyAttachment: [...schemeFileList.value], // 变更申请说明附件
    proveAttachment: [...schemePriceFileList.value], // 价格说明附件
    reason: schemeChangeReason.value,
  };

  if (type === 'total') {
    // 合计
    schemeTotalInfo.value = params || {};
    return;
  }

  if (type === 'changeLog') {
    // 方案变更log
    console.log('%c [ 变更log ]', 'font-size:13px; background:pink; color:#bf2c9f;', params);

    return;
  }

  // 后端缓存
  const res = await saveDataBy({
    applicationCode: 'haierbusiness-mice-merchant',
    // 规则: haierbusiness-mice-bid_工号_你业务的缓存key
    cacheKey:
      'haierbusiness-mice-merchant_' +
      loginUser.value?.username +
      '_schemeChangeKey' +
      miceId.value +
      '_merchantId' +
      merchantId.value, // 方案变更
    cacheValue: JSON.stringify({
      ...params,
    }),
  });

  if (res && type === 'hand') {
    // 手动保存
    message.success('方案互动已暂存！');
  }
};

// 完成提报
const schemeSub = async () => {
  setTimeout(() => {
    if (schemePlanRef.value && !schemePlanRef.value.SchemePlanSub()) {
      // 日程安排
      return;
    }

    if (schemeMaterialRef.value && !schemeMaterialRef.value.materialSub()) {
      // 布展物料
      return;
    }

    if (schemePresentRef.value && !schemePresentRef.value.presentSub()) {
      // 礼品
      return;
    }

    if (schemeOtherRef.value && !schemeOtherRef.value.otherSub()) {
      // 其他
      return;
    }

    if (schemeFeeRef.value && !schemeFeeRef.value.serviceFeeSub()) {
      // 全单服务费
      return;
    }

    if (schemeFileRef.value && !schemeFileRef.value.serviceFileSub()) {
      // 变更申请说明附件
      return;
    }

    if (schemeChangeAddNum.value > 0 && schemePriceFileRef.value && !schemePriceFileRef.value.serviceFileSub()) {
      // 变更申请说明附件
      return;
    }

    // 方案变更-酒店临时ID
    schemeDetail.value.hotels.forEach((e) => {
      e.tempId = e.id;
    });

    const schemeAllPrice =
      totalPrice.value +
      (schemeFeeObj.value.serviceFee && schemeFeeObj.value.serviceFee.schemeServiceFeeReal
        ? schemeFeeObj.value.serviceFee.schemeServiceFeeReal
        : 0);

    let params = {
      schemeId: schemeDetail.value.id,
      merchantId: merchantId.value,

      miceId: miceId.value,
      schemeTotalPrice: Number(schemeAllPrice.toFixed(2)), // 方案总金额
      // agreementTotalPrice: 0, // 协议总金额
      // marketTotalPrice: 0, // 	市场价总金额

      startDate: schemeCacheDetail.value.startDate || schemeDetail.value.startDate,
      endDate: schemeCacheDetail.value.endDate || schemeDetail.value.endDate,

      hotels: [...schemeDetail.value.hotels],
      ...schemePlanObj.value,
      material: {
        ...schemeMaterialObj.value?.schemeMaterial,
      },
      // traffic: {},
      presents: [...schemePresentArr.value],
      others: [...schemeOtherArr.value],
      ...schemeFeeObj.value,

      applyAttachment: [...schemeFileList.value], // 变更申请说明附件
      proveAttachment: [...schemePriceFileList.value], // 价格说明附件
      reason: schemeChangeReason.value,

      // sourceId: null,
    };

    //
    // 单项总和不能超过预算的120%
    //
    // 住宿
    let staysTotalOld = 0;
    let staysTotalNew = 0;

    if (schemePlanObj.value.stays) {
      schemeDetail.value?.stays.forEach((e) => {
        staysTotalOld += e.schemeRoomNum * e.schemeUnitPrice;
      });

      schemePlanObj.value.stays.forEach((e) => {
        staysTotalNew += e.schemeRoomNum * e.schemeUnitPrice;
      });
    }

    if (staysTotalOld && staysTotalNew > staysTotalOld * 1.2) {
      Modal.warning({
        title: '住宿超过原预算的20%！',
        okText: '确定',
      });

      anchorJump(
        'schemeStayId' +
          (schemeDetail.value?.stays[schemeDetail.value?.stays.length - 1]?.demandDate ||
            schemePlanObj.value?.stays[schemePlanObj.value?.stays.length - 1]?.demandDate) +
          0,
      );

      return;
    }

    // 会场
    let placesTotalOld = 0;
    let placesTotalNew = 0;

    if (schemePlanObj.value.places) {
      schemeDetail.value?.places.forEach((e) => {
        if (e.placeNum !== 0) {
          placesTotalOld += e.schemeUnitPlacePrice || 0;

          if (e.hasLed) {
            // 单价*LED数量
            placesTotalOld += e.schemeUnitLedPrice * e.schemeLedNum;
          }
          if (e.hasTea) {
            // 茶歇单价*会场人数
            placesTotalOld += e.teaEachTotalPrice * e.schemePersonNum;
          }
        }
      });

      schemePlanObj.value.places.forEach((e) => {
        if (e.placeNum !== 0) {
          placesTotalNew += e.schemeUnitPlacePrice || 0;

          if (e.hasLed) {
            // 单价*LED数量
            placesTotalNew += e.schemeUnitLedPrice * e.schemeLedNum;
          }
          if (e.hasTea) {
            // 茶歇单价*会场人数
            placesTotalNew += e.teaEachTotalPrice * e.schemePersonNum;
          }
        }
      });
    }

    if (placesTotalOld && placesTotalNew > placesTotalOld * 1.2) {
      Modal.warning({
        title: '会场超过原预算的20%！',
        okText: '确定',
      });

      anchorJump(
        'schemePlaceId' +
          (schemeDetail.value?.places[schemeDetail.value?.places.length - 1]?.demandDate ||
            schemePlanObj.value?.places[schemePlanObj.value?.places.length - 1]?.demandDate) +
          0,
      );

      return;
    }

    // 用餐
    let cateringsTotalOld = 0;
    let cateringsTotalNew = 0;

    if (schemePlanObj.value.caterings) {
      schemeDetail.value?.caterings.forEach((e) => {
        cateringsTotalOld += e.schemeUnitPrice * e.schemePersonNum;
      });

      schemePlanObj.value.caterings.forEach((e) => {
        cateringsTotalNew += e.schemeUnitPrice * e.schemePersonNum;
      });
    }

    if (cateringsTotalOld && cateringsTotalNew > cateringsTotalOld * 1.2) {
      Modal.warning({
        title: '用餐超过原预算的20%！',
        okText: '确定',
      });

      anchorJump(
        'schemeCateringId' +
          (schemeDetail.value?.caterings[schemeDetail.value?.caterings.length - 1]?.demandDate ||
            schemePlanObj.value?.caterings[schemePlanObj.value?.caterings.length - 1]?.demandDate) +
          0,
      );

      return;
    }

    // 用车
    let vehiclesTotalOld = 0;
    let vehiclesTotalNew = 0;

    if (schemePlanObj.value.vehicles) {
      schemeDetail.value?.vehicles.forEach((e) => {
        vehiclesTotalOld += e.schemeUnitPrice * e.schemeVehicleNum;
      });

      schemePlanObj.value.vehicles.forEach((e) => {
        vehiclesTotalNew += e.schemeUnitPrice * e.schemeVehicleNum;
      });
    }

    if (vehiclesTotalOld && vehiclesTotalNew > vehiclesTotalOld * 1.2) {
      Modal.warning({
        title: '用车超过原预算的20%！',
        okText: '确定',
      });

      anchorJump(
        'schemeVehicleId' +
          (schemeDetail.value?.vehicles[schemeDetail.value?.vehicles.length - 1]?.demandDate ||
            schemePlanObj.value?.vehicles[schemePlanObj.value?.vehicles.length - 1]?.demandDate) +
          0,
      );

      return;
    }

    // 服务人员
    let attendantsTotalOld = 0;
    let attendantsTotalNew = 0;

    if (schemePlanObj.value.attendants) {
      schemeDetail.value?.attendants.forEach((e) => {
        attendantsTotalOld += e.schemeUnitPrice * e.schemePersonNum;
      });

      schemePlanObj.value.attendants.forEach((e) => {
        attendantsTotalNew += e.schemeUnitPrice * e.schemePersonNum;
      });
    }

    if (attendantsTotalOld && attendantsTotalNew > attendantsTotalOld * 1.2) {
      Modal.warning({
        title: '服务人员超过原预算的20%！',
        okText: '确定',
      });

      anchorJump(
        'schemeAttendantId' +
          (schemeDetail.value?.attendants[schemeDetail.value?.attendants.length - 1]?.demandDate ||
            schemePlanObj.value?.attendants[schemePlanObj.value?.attendants.length - 1]?.demandDate) +
          0,
      );

      return;
    }

    // 方案拓展
    let activitiesTotalOld = 0;
    let activitiesTotalNew = 0;

    if (schemePlanObj.value.activities) {
      schemeDetail.value?.activities.forEach((e) => {
        activitiesTotalOld += e.schemeUnitPrice * e.schemePersonNum;
      });

      schemePlanObj.value.activities.forEach((e) => {
        activitiesTotalNew += e.schemeUnitPrice * e.schemePersonNum;
      });
    }

    if (activitiesTotalOld && activitiesTotalNew > activitiesTotalOld * 1.2) {
      Modal.warning({
        title: '方案拓展超过原预算的20%！',
        okText: '确定',
      });

      anchorJump(
        'schemeActivitiesId' +
          (schemeDetail.value?.activities[schemeDetail.value?.activities.length - 1]?.demandDate ||
            schemePlanObj.value?.activities[schemePlanObj.value?.activities.length - 1]?.demandDate) +
          0,
      );

      return;
    }

    // 布展物料
    let materialTotalOld = 0;
    let materialTotalNew = 0;

    if (
      schemeMaterialObj.value &&
      schemeMaterialObj.value.schemeMaterial &&
      schemeMaterialObj.value.schemeMaterial.materialDetails
    ) {
      schemeDetail.value?.material?.materialDetails.forEach((e) => {
        materialTotalOld += e.schemeUnitPrice * e.schemeMaterialNum;
      });

      schemeMaterialObj.value.schemeMaterial.materialDetails.forEach((e) => {
        materialTotalNew += e.schemeUnitPrice * e.schemeMaterialNum;
      });
    }

    if (materialTotalOld && materialTotalNew > materialTotalOld * 1.2) {
      Modal.warning({
        title: '布展物料超过原预算的20%！',
        okText: '确定',
      });

      anchorJump('changeSchemeMaterialId' + 0);

      return;
    }

    // 其他方案
    let othersTotalOld = 0;
    let othersTotalNew = 0;

    if (schemeOtherArr.value) {
      schemeDetail.value?.others.forEach((e) => {
        othersTotalOld += e.schemeTotalPrice;
      });

      schemeOtherArr.value.forEach((e) => {
        othersTotalNew += e.schemeTotalPrice;
      });
    }

    if (othersTotalOld && othersTotalNew > othersTotalOld * 1.2) {
      Modal.warning({
        title: '其他方案超过原预算的20%！',
        okText: '确定',
      });

      anchorJump('changeSchemeOtherId');

      return;
    }
    // 单项总和不能超过预算的120% - end

    //
    // 总额不能超过预算总额
    //
    const totalPriceOld = schemeDetail.value.schemeTotalPrice;

    const totalPriceNew =
      staysTotalNew +
        placesTotalNew +
        cateringsTotalNew +
        vehiclesTotalNew +
        attendantsTotalNew +
        activitiesTotalNew +
        materialTotalNew +
        othersTotalNew +
        schemeFeeObj.value.serviceFee.schemeServiceFeeReal || 0;

    if (totalPriceNew > totalPriceOld) {
      Modal.warning({
        title: '已超总预算' + (totalPriceNew - totalPriceOld) + '元！',
        okText: '确定',
      });

      return;
    }
    // 总额不能超过预算总额 - end

    if (schemeChangeType.value === 'schemeChangeEdit') {
      // 方案调整
      params.sourceId = schemeDetail.value.id;

      // 住宿
      params.stays &&
        params.stays.forEach((e) => {
          e.sourceId = e.id;
        });
      // 会场
      params.places &&
        params.places.forEach((e) => {
          e.sourceId = e.id;
        });
      // 用餐
      params.caterings &&
        params.caterings.forEach((e) => {
          e.sourceId = e.id;
        });
      // 用车
      params.vehicles &&
        params.vehicles.forEach((e) => {
          e.sourceId = e.id;
        });
      // 服务人员
      params.attendants &&
        params.attendants.forEach((e) => {
          e.sourceId = e.id;
        });
      // 方案拓展
      params.activities &&
        params.activities.forEach((e) => {
          e.sourceId = e.id;
        });
      // 保险
      params.insurances &&
        params.insurances.forEach((e) => {
          e.sourceId = e.id;
        });

      // 布展物料
      if (params.material && Object.keys(params.material).length > 0) {
        params.material.sourceId = params.material.id;

        params.material &&
          params.material.materialDetails &&
          params.material.materialDetails.forEach((e) => {
            e.sourceId = e.id;
          });
      }
      // 礼品
      params.presents &&
        params.presents.forEach((e) => {
          e.sourceId = e.id;
          e.presentDetails.forEach((e2) => {
            e2.optionType = e.optionType;
            e2.sourceId = e2.id;
          });
        });
      // 其他
      params.others &&
        params.others.forEach((e) => {
          e.sourceId = e.id;
        });

      // 服务费
      if (params.serviceFee && Object.keys(params.serviceFee).length > 0) {
        params.serviceFee.sourceId = schemeDetail.value.id;
      }
    }

    Modal.confirm({
      title: '确定提交？',
      // icon: null,
      content: '',
      onOk: async () => {
        subLoading.value = true;

        schemeApi
          .schemeExecutionChangeSubmitBid({ ...params })
          .then((res) => {
            // 缓存删除
            delCache();

            message.success('方案提报成功！');

            approvalModalShow.value = true;

            // 审批Code赋值
            approveCode.value = res.data.processCode;
          })
          .finally(() => {
            subLoading.value = false;
          });
      },
      onCancel() {},
    });
  }, 30);
};

// 价格提报
const biddingSub = async () => {
  setTimeout(() => {
    if (schemePlanRef.value && !schemePlanRef.value.SchemePlanSub()) {
      // 日程安排
      return;
    }

    if (schemeMaterialRef.value && !schemeMaterialRef.value.materialSub()) {
      // 布展物料
      return;
    }

    if (schemePresentRef.value && !schemePresentRef.value.presentSub()) {
      // 礼品
      return;
    }

    if (schemeOtherRef.value && !schemeOtherRef.value.otherSub()) {
      // 其他
      return;
    }

    if (schemeFeeRef.value && !schemeFeeRef.value.serviceFeeSub()) {
      // 全单服务费
      return;
    }

    // if (schemeFileRef.value && !schemeFileRef.value.serviceFileSub()) {
    //   // 议价附件
    //   return;
    // }

    let stays = [];
    let places = [];
    let caterings = [];
    let vehicles = [];
    let attendants = [];
    let activities = [];
    let insurances = [];

    if (schemePlanObj.value) {
      // 住宿
      if (schemePlanObj.value.stays) {
        stays = schemePlanObj.value.stays.map((e) => {
          return { id: e.id, schemeUnitPrice: e.schemeUnitPrice };
        });
      }

      if (schemePlanObj.value.places) {
        places = schemePlanObj.value.places.map((e) => {
          let totalPrice = e.schemeUnitPlacePrice || 0;

          if (e.hasLed) {
            // 单价*LED数量
            totalPrice += e.schemeUnitLedPrice * e.schemeLedNum;
          }
          if (e.hasTea) {
            // 茶歇单价*会场人数
            totalPrice += e.teaEachTotalPrice * e.schemePersonNum;
          }

          return {
            id: e.id,
            schemeTotalPrice: totalPrice,
            schemeUnitPlacePrice: e.schemeUnitPlacePrice,
            schemeUnitLedPrice: e.schemeUnitLedPrice,
            schemeUnitTeaPrice: e.schemeUnitTeaPrice,
          };
        });
      }
      if (schemePlanObj.value.caterings) {
        caterings = schemePlanObj.value.caterings.map((e) => {
          return { id: e.id, schemeUnitPrice: e.schemeUnitPrice };
        });
      }
      if (schemePlanObj.value.vehicles) {
        vehicles = schemePlanObj.value.vehicles.map((e) => {
          return { id: e.id, schemeUnitPrice: e.schemeUnitPrice };
        });
      }
      if (schemePlanObj.value.attendants) {
        attendants = schemePlanObj.value.attendants.map((e) => {
          return { id: e.id, schemeUnitPrice: e.schemeUnitPrice };
        });
      }
      if (schemePlanObj.value.activities) {
        activities = schemePlanObj.value.activities.map((e) => {
          return { id: e.id, schemeUnitPrice: e.schemeUnitPrice };
        });
      }
      if (schemePlanObj.value.insurances) {
        insurances = schemePlanObj.value.insurances.map((e) => {
          return { id: e.id, schemeUnitPrice: e.schemeUnitPrice };
        });
      }
    }

    let material = {};

    if (
      schemeMaterialObj.value &&
      schemeMaterialObj.value.schemeMaterial &&
      schemeMaterialObj.value.schemeMaterial.materialDetails
    ) {
      material.id = bargainSchemeDetails.value?.material?.id;
      material.materialDetails = schemeMaterialObj.value.schemeMaterial.materialDetails.map((e, idx) => {
        return {
          id: bargainSchemeDetails.value?.material?.materialDetails[idx].id,
          miceSchemeMaterialId: e.miceSchemeMaterialId,
          schemeUnitPrice: e.schemeUnitPrice,
        };
      });
    }

    let presents = [];
    presents = schemePresentArr.value.map((e, idx) => {
      return {
        id: e.id,
        schemeTotalPrice: e.schemeTotalPrice,
        presentDetails: [
          {
            id: e.id,
            optionType: e.optionType,
            miceSchemePresentDetailsId: e.miceSchemePresentDetailsId,
          },
        ],
      };
    });

    let others = [];
    others = schemeOtherArr.value.map((e, idx) => {
      return {
        id: bargainSchemeDetails.value?.others[idx]?.id,
        schemeTotalPrice: e.schemeTotalPrice,
      };
    });

    const schemeServiceFeeRealTwo = schemeFeeObj.value.serviceFee?.schemeServiceFeeReal || 0;

    const serviceFee = {
      id: schemeFeeObj.value.serviceFee?.id || null,
      schemeServiceFeeReal: Number(schemeServiceFeeRealTwo.toFixed(2)),
      serviceFeeRate: schemeFeeObj.value.serviceFee?.serviceFeeRate,
    };

    const schemeTotalPriceTwo =
      Number(totalPrice.value) + (serviceFee.schemeServiceFeeReal ? Number(serviceFee.schemeServiceFeeReal) : 0) || 0;

    let params = {
      schemeId: miceNewSchemeId.value,
      merchantId: merchantId.value,
      // sourceId: null,
      schemeTotalPrice: Number(schemeTotalPriceTwo.toFixed(2)), // 方案总金额

      stays: [...stays],
      places: [...places],
      caterings: [...caterings],
      vehicles: [...vehicles],
      attendants: [...attendants],
      activities: [...activities],
      insurances: [...insurances],

      material: { ...material },
      // traffic: {},
      presents: [...presents],
      others: [...others],
      serviceFee: { ...serviceFee },
    };

    Modal.confirm({
      title: '确定提交？',
      // icon: null,
      content: '',
      onOk: async () => {
        subLoading.value = true;

        schemeApi
          .schemePriceChangeSubmitBid({ ...params })
          .then((res) => {
            message.success('议价成功！');

            approvalModalShow.value = true;

            // 审批Code赋值
            approveCode.value = res.data.processCode;
          })
          .finally(() => {
            subLoading.value = false;
          });
      },
      onCancel() {},
    });
  }, 30);
};

const closeApproval = () => {
  if (isCloseLastTab) {
    // 关闭当前页面
    isCloseLastTab.value = true;
  }

  router.push({
    path: '/mice-merchant/billUploadScheme/index',
  });
};

const getUser = async () => {
  // 获取登录服务商的类型
  const res = await schemeApi.getMerchantByUser({});

  // 服务商的类型
  // 1-酒店,2-旅行社,3-保险,4-礼品,5-用车
  merchantType.value = res.merchantType;
  merchantId.value = res.id;
};

// 流程详情
const getProcessDetails = async (
  processId = localStorage.getItem('processId') || '',
  verId = '',
  pdmMerchantPoolId = '',
) => {
  // 流程ID
  if (!processId) {
    message.error('流程ID不存在！');
    return;
  }

  const res = await miceBidManOrderListApi.processDetails({
    id: processId,
    verId: verId,
  });

  // 需求配置
  const demandProcessList = ProcessOrchestrationServiceTypeEnum.getTypeOptions().map((e) => {
    return e.value;
  });
  demandSets.value = numComputedArrMethod(res.items, [...demandProcessList]);

  // 需求配置 - 全单服务费是否配置
  showFee.value = demandSets.value.includes(2048);

  // 是否控制餐标等其他标准配置 - 1:不可修改,2:可以提高,3:可以降低
  isCateringStandardControl.value = meetingProcessOrchestration(
    'SCHEME_SUBMIT',
    res.nodes || [],
    'schemeSubmitMealLabelConfigDefine',
  );

  // 会议最短召开日期(单位: 天)
  const overDay = meetingProcessOrchestration(
    'DEMAND_SUBMIT',
    res.nodes || [],
    'demandSubmitMinimumMiceStartDateConfigDefine',
  );
  meetingMinDaysNum.value = overDay ? Number(overDay) : 0;

  if (!showFee.value) {
    // 未设置全单服务费
    fullServiceRangeRateLimit.value = 0;
  } else {
    // 需求配置 - 全单服务费上限
    const feeConfigList = res.merchantPools || [];
    const feeRange = feeConfigList.filter((e) => e.id === pdmMerchantPoolId) || [];

    // 全单服务费配置项
    serviceFeeSets.value = numComputedArrMethod(feeRange[0]?.fullServiceRange - 2048, [...demandProcessList]);
    // serviceFeeSets.value = [1, 2, 4, 16];
    fullServiceRangeRateLimit.value = feeRange[0]?.fullServiceRangeRateLimit;
    fullServiceRemark.value = feeRange[0]?.fullServiceRemark;
  }

  // 加载完成
  loadingEnd.value = true;
};

const delCacheBtn = async () => {
  await delCache();
  window.location.reload();
};

// 获取议价记录
const getPriceChangeRecord = async () => {
  const res = await schemeApi.merchantPriceChangeRecord({
    miceId: miceId.value,
  });

  miceNewSchemeId.value = res[0]?.miceNewSchemeId || null;
};

onMounted(async () => {
  const record = resolveParam(route.query.record as string);
  console.log('%c [ record ]-845', 'font-size:13px; background:pink; color:#bf2c9f;', record);

  miceId.value = record.miceId;
  schemeChangeType.value = record.schemeChangeType;

  pdMainId.value = record.pdMainId;
  pdVerId.value = record.pdVerId;

  isShowDel.value = localStorage.getItem('testProcessSignForCiCi') === '1';

  await getUser();

  // 缓存查询
  await getCache();
});

onBeforeUnmount(() => {
  clearInterval(autoSave.value);
  clearInterval(countdownTimer.value);
});
</script>

<template>
  <!-- 方案变更 -->
  <div class="scheme_interact" ref="schemeContainerRef">
    <a-spin :spinning="cacheLoading || schemeLoading || subLoading" tip="Loading..." size="large">
      <a-alert
        v-if="schemeAbandonReason"
        class="mb16 demand_reject_reason"
        message="驳回原因："
        :description="schemeAbandonReason"
        show-icon
        type="warning"
      />
      <!-- 顶部 -->
      <schemeInfo class="interact_header" :demandInfo="schemeDetail" />

      <div class="interact_content mt20">
        <!-- 标题 -->
        <a-affix :offset-top="0" :target="() => schemeContainerRef">
          <div class="interact_demand_title mb12 pb12">
            <div class="plan_title">
              <img src="@/assets/image/common/demand_icon.png" width="16" />
              <span class="ml12">中标方案</span>
            </div>
            <div class="plan_title">
              <img src="@/assets/image/common/plan_icon.png" width="16" />
              <span class="ml12">
                {{
                  ['schemeBargainingEdit', 'schemeBargainingView'].includes(schemeChangeType) ? '议价方案' : '变更方案'
                }}
              </span>
            </div>
          </div>
        </a-affix>

        <!-- 酒店需求 -->
        <div
          class="interact_hotel common_content p24 mb16"
          v-if="(merchantType === 1 || merchantType === 2 || userType === 'user') && loadingEnd"
        >
          <changeHotel :schemeHotels="schemeDetail.hotels" />
        </div>
        <!-- 日程安排 -->
        <div class="interact_schedule_plan common_content p24 mb16">
          <changePlan
            ref="schemePlanRef"
            v-if="(merchantType !== 4 || userType === 'user') && loadingEnd"
            :schemeContainerRef="schemeContainerRef"
            :demandSets="demandSets"
            :processNode="processNode"
            :schemeChangeType="schemeChangeType"
            :userType="userType"
            :schemeInfo="schemeDetail"
            :schemeCacheInfo="schemeCacheDetail"
            :isSchemeCache="isSchemeCache"
            :isSchemeBargain="isSchemeBargain"
            :bargainSchemeInfo="bargainSchemeDetails"
            :hotelList="schemeDetail.hotels"
            :merchantType="merchantType"
            :isCateringStandardControl="isCateringStandardControl"
            :meetingMinDaysNum="meetingMinDaysNum"
            @planPriceEmit="planPriceEmit"
            @planEachPriceEmit="planEachPriceEmit"
            @schemePlanEmit="schemePlanEmit"
            @schemeChangePlanDateEmit="schemeChangePlanDateEmit"
          />
        </div>
        <!-- 布展物料 -->
        <div
          v-if="
            ((schemeDetail.material &&
              schemeDetail.material.materialDetails &&
              schemeDetail.material.materialDetails.length > 0) ||
              (schemeCacheDetail.material &&
                schemeCacheDetail.material.materialDetails &&
                schemeCacheDetail.material.materialDetails.length > 0) ||
              schemeChangeType === 'schemeChangeEdit') &&
            loadingEnd &&
            (merchantType === 1 || merchantType === 2 || userType === 'user')
          "
          class="interact_wu common_content p24 mb16"
        >
          <change-material
            ref="schemeMaterialRef"
            :processNode="processNode"
            :schemeChangeType="schemeChangeType"
            :schemeInfo="schemeDetail"
            :schemeCacheInfo="schemeCacheDetail"
            :isSchemeCache="isSchemeCache"
            :isSchemeBargain="isSchemeBargain"
            :bargainSchemeInfo="bargainSchemeDetails"
            @materialPriceEmit="materialPriceEmit"
            @schemeMaterialEmit="schemeMaterialEmit"
          />
        </div>
        <!-- 礼品 -->
        <div
          v-if="
            ((schemeDetail.presents && schemeDetail.presents.length > 0) ||
              (schemeCacheDetail.presents && schemeCacheDetail.presents.length > 0) ||
              schemeChangeType === 'schemeChangeEdit') &&
            loadingEnd &&
            (merchantType === 4 || userType === 'user')
          "
          class="interact_gift common_content p24 mb16"
        >
          <change-presents
            ref="schemePresentRef"
            :schemeChangeType="schemeChangeType"
            :schemeInfo="schemeDetail"
            :schemeCacheInfo="schemeCacheDetail"
            :isSchemeCache="isSchemeCache"
            :isSchemeBargain="isSchemeBargain"
            :bargainSchemeInfo="bargainSchemeDetails"
            @presentPriceEmit="presentPriceEmit"
            @schemePresentEmit="schemePresentEmit"
          />
        </div>
        <!-- 其他 -->
        <div
          v-if="
            ((schemeDetail.others && schemeDetail.others.length > 0) ||
              (schemeCacheDetail.others && schemeCacheDetail.others.length > 0) ||
              schemeChangeType === 'schemeChangeEdit') &&
            loadingEnd &&
            (merchantType === 1 || merchantType === 2 || userType === 'user')
          "
          class="interact_other common_content p24 mb16"
        >
          <change-other
            ref="schemeOtherRef"
            :processNode="processNode"
            :schemeChangeType="schemeChangeType"
            :schemeInfo="schemeDetail"
            :schemeCacheInfo="schemeCacheDetail"
            :isSchemeCache="isSchemeCache"
            :isSchemeBargain="isSchemeBargain"
            :bargainSchemeInfo="bargainSchemeDetails"
            @otherPriceEmit="otherPriceEmit"
            @schemeOtherEmit="schemeOtherEmit"
          />
        </div>
        <!-- 全单服务费方案 -->
        <div
          class="interact_service_fee common_content p24 mb16"
          v-if="(((merchantType === 1 || merchantType === 2) && showFee) || userType === 'user') && loadingEnd"
        >
          <change-service-fee
            ref="schemeFeeRef"
            :schemeChangeType="schemeChangeType"
            :materialPrice="materialPrice"
            :presentPrice="presentPrice"
            :otherPrice="otherPrice"
            :planEachPriceList="planEachPriceList"
            :fullServiceRangeRateLimit="fullServiceRangeRateLimit"
            :fullServiceRemark="fullServiceRemark"
            :serviceFeeSets="serviceFeeSets"
            :schemeInfo="schemeDetail"
            :schemeCacheInfo="schemeCacheDetail"
            :isSchemeCache="isSchemeCache"
            :isSchemeBargain="isSchemeBargain"
            :bargainSchemeInfo="bargainSchemeDetails"
            @schemeFeeEmit="schemeFeeEmit"
          />
        </div>
        <!-- 变更申请说明资料 -->
        <div
          class="interact_service_file common_content p24 mb16"
          v-if="['schemeChangeEdit'].includes(schemeChangeType) && loadingEnd"
        >
          <changeFiles
            ref="schemeFileRef"
            :schemeChangeType="schemeChangeType"
            :schemeInfo="schemeDetail"
            :schemeCacheInfo="schemeCacheDetail"
            :isSchemeCache="isSchemeCache"
            :isSchemeBargain="isSchemeBargain"
            :bargainSchemeInfo="bargainSchemeDetails"
            @schemeFileEmit="schemeFileEmit"
            @schemeReasonEmit="schemeReasonEmit"
          />
        </div>
        <!-- 价格说明附件 -->
        <div
          class="interact_service_file common_content p24 mb16"
          v-if="['schemeChangeEdit'].includes(schemeChangeType) && loadingEnd && schemeChangeAddNum"
        >
          <changePriceFiles
            ref="schemePriceFileRef"
            :schemeChangeType="schemeChangeType"
            :schemeInfo="schemeDetail"
            :schemeCacheInfo="schemeCacheDetail"
            :isSchemeCache="isSchemeCache"
            @schemePriceFileEmit="schemePriceFileEmit"
          />
        </div>
        <!-- 合计 -->
        <div class="interact_total_table common_content p24">
          <change-total :schemeInfo="schemeTotalInfo" :totalPrice="totalPrice" />
        </div>
      </div>

      <!-- 操作 -->
      <a-affix
        :offset-bottom="0"
        v-if="
          ['MICE_PENDING', 'MICE_EXECUTION', 'MICE_COMPLETED'].includes(processNode) &&
          ['schemeBargainingEdit', 'schemeChangeEdit'].includes(schemeChangeType)
        "
      >
        <div class="btns_mar"></div>

        <div class="interact_btns">
          <div class="flex_between">
            <div
              class="sub_auto_save mr24"
              v-if="
                ['MICE_PENDING', 'MICE_EXECUTION', 'MICE_COMPLETED'].includes(processNode) &&
                ['schemeChangeEdit'].includes(schemeChangeType)
              "
            >
              <div v-show="countdownTime === 0" class="auto_save_img"></div>
              <div class="auto_save_time pl5">
                {{ countdownTime === 0 ? '已自动保存' : countdownTime + 's后自动保存' }}
              </div>
            </div>

            <!-- 方案提报 -->
            <div
              class="sub_btns"
              v-if="
                ['MICE_PENDING', 'MICE_EXECUTION', 'MICE_COMPLETED'].includes(processNode) &&
                ['schemeChangeEdit'].includes(schemeChangeType)
              "
            >
              <span v-if="isShowDel" class="mr12">变更新增{{ schemeChangeAddNum }}</span>

              <a-button v-if="isShowDel" class="mr8" type="primary" danger @click="schemeTemporarily('changeLog')">
                变更log
              </a-button>

              <a-button v-if="isShowDel" class="mr8" danger @click="delCacheBtn()">缓存删除</a-button>

              <a-button class="mr8" @click="schemeTemporarily('hand')">暂存</a-button>
              <a-button type="primary" :loading="subLoading" @click="schemeSub">完成提报</a-button>
            </div>
            <!-- 竞价 -->
            <div
              class="sub_btns"
              v-if="
                ['MICE_PENDING', 'MICE_EXECUTION', 'MICE_COMPLETED'].includes(processNode) &&
                ['schemeBargainingEdit'].includes(schemeChangeType)
              "
            >
              <a-button type="primary" :loading="subLoading" @click="biddingSub()">价格提报</a-button>
            </div>
          </div>
        </div>
      </a-affix>
    </a-spin>

    <!-- 审批流 -->
    <a-modal
      v-model:open="approvalModalShow"
      title="已提交如下人员审批"
      width="80%"
      :keyboard="false"
      :maskClosable="false"
      :closable="false"
    >
      <div>
        <iframe width="100%" :src="businessProcess + '?code=' + approveCode + '#/detailsPcSt'" frameborder="0"></iframe>
      </div>
      <template #footer>
        <a-button @click="closeApproval">确定</a-button>
      </template>
    </a-modal>
  </div>
</template>

<style>
@import './schemeChangeCom/schemeChange.scss';
</style>
<style scoped lang="less">
.scheme_interact {
  height: 100%;
  overflow-y: auto;

  .demand_reject_reason {
    padding: 24px;
    border-radius: 12px;
  }

  .interact_header {
  }

  .interact_content {
  }

  .interact_demand_title {
    display: flex;
    justify-content: space-between;
    background: #f5f5f5;

    .plan_title {
      display: flex;
      justify-content: center;
      align-items: center;

      width: calc(50% - 6px);
      height: 32px;

      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #1d2129;
      background: #d5e6ff;
      border-radius: 4px;
      border: 1px solid rgba(24, 104, 219, 0.3);
    }
  }

  .interact_hotel {
  }

  .interact_schedule_plan {
  }

  .interact_wu {
  }

  .interact_gift {
  }

  .interact_other {
  }

  .interact_service_fee {
  }

  .interact_total_table {
  }

  .btns_mar {
    height: 16px;
    background: #f5f5f5;
  }
  .interact_btns {
    width: 100%;

    height: 56px !important;
    line-height: 56px;
    padding: 0 24px;
    background: #ffffff;
    box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.03);
    filter: blur(0px);
    border-top: 1px solid #e8e8e8;

    .flex_between {
      display: flex;
      justify-content: right;
      align-items: center;

      .sub_auto_save {
        display: flex;

        color: #4e5969;
        line-height: 20px;

        .auto_save_img {
          width: 18px;
          height: 18px;
          background: url('@/assets/image/demand/right_green.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }
        .auto_save_time {
          text-align: right;
        }
      }

      .sub_btns {
      }
    }
  }
}
</style>
