.scheme_interact {
  .common_content {
    background: #ffffff;
    border-radius: 6px;

    .interact_title {
      margin-left: -24px;

      display: flex;
      align-items: center;

      height: 25px;
      line-height: 25px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 18px;
      color: #1d2129;
      .interact_shu {
        width: 4px;
        height: 20px;
        background: #1868db;
      }
    }

    .scheme_plan_title {
      display: flex;
      align-items: center;

      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 18px;
      color: #1d2129;
      line-height: 25px;

      .scheme_plan_img {
        width: 18px;
        height: 18px;
      }
    }

    .common_table {
      display: flex;
      justify-content: space-between;
      border: 1px solid #e5e6eb;
      position: relative;

      .common_table_l,
      .common_table_r {
        padding: 24px;

        width: 50%;
        height: 100%;
        background: #ffffff;
      }
      .common_table_divide {
        position: absolute;
        left: 50%;
        top: 0;
        transform: translateX(-50%);

        width: 1px;
        height: 100%;
        background: #e5e6eb;
      }
    }

    .scheme_plan_table {
      display: flex;
      align-items: stretch;

      .scheme_plan_index {
        flex: 0 0 auto;

        display: flex;
        align-items: center;
        justify-content: center;

        width: 80px;
        background: #f7f8fa;
        border: 1px solid #e5e6eb;
        border-right: 0;

        color: #4e5969;
      }
      .scheme_plan_list1,
      .scheme_plan_list2 {
        flex: 0 0 auto;
        width: 100px;

        .scheme_plan_label,
        .scheme_plan_value {
          margin-top: -1px;
          border: 1px solid #e5e6eb;

          width: 100%;
          height: 40px;
          line-height: 40px;
          background: #ffffff;

          font-size: 14px;
          color: #86909c;
          text-align: right;

          &:first-child {
            margin-top: 0px;
          }

          .scheme_plan_border {
            padding: 0 12px;
            border: 2px solid rgba(0, 0, 0, 0);
            width: 100%;
            height: 100%;
            line-height: 36px;
            border-radius: 2px;
          }
          .error_tip {
            border-color: #ff5533;
          }
        }

        .scheme_plan_label {
          padding-right: 12px;
        }

        .scheme_plan_value {
          margin-left: -1px;

          text-align: left;
          color: #1d2129;

          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;

          &:hover {
            background: #f2f7ff;
          }
        }

        .color_blue {
          color: #1868db;
        }
      }
      .scheme_plan_list2 {
        border-left: 0;
        width: calc(100% - 90px - 100px - 150px);
      }

      .scheme_plan_list3 {
        position: relative;
        margin-left: -1px;

        width: 150px;
        flex: 0 0 auto;
        background: #f2f7ff;
        border: 1px solid #e5e6eb;
        border-left: none;

        display: flex;
        flex-direction: column;
        justify-content: center;

        .scheme_plan_price {
          display: flex;
          justify-content: flex-end;

          line-height: 24px;

          .scheme_plan_price_label {
            text-align: right;
            // width: 116px;
            color: #4e5969;
          }
          .scheme_plan_price_value {
            height: 24px;
            text-align: end;

            width: 84px;
            font-weight: 500;
            font-size: 14px;
            color: #1868db;
            text-align: right;
          }
        }
        .scheme_plan_price_tip {
          height: 17px;
          font-size: 12px;
          color: #86909c;
          line-height: 17px;
          text-align: right;
        }
        .action_icons {
          position: absolute;
          right: -24px;
          top: 50%;
          transform: translateY(-50%);

          .del_icon {
            width: 16px;
            height: 40px;

            cursor: pointer;
            background: url('@/assets/image/common/del_red.png');
            background-repeat: no-repeat;
            background-size: 16px 16px;
            background-position: center;
          }
        }
      }
    }

    .scheme_plan_subtotal {
      height: 40px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #1d2129;
      line-height: 40px;
      text-align: right;
    }

    .add_scheme_plan {
      height: 32px;
      display: flex;
      justify-content: center;
      align-items: center;

      color: #4e5969;
      background: #ffffff;
      border-radius: 2px;
      border: 1px dashed #e5e6eb;
      cursor: pointer;
      user-select: none;

      &:hover {
        color: #1868db;
        border-color: #1868db;

        .plan_add_img {
          width: 16px;
          height: 16px;
          background: url('@/assets/image/demand/demand_add_blue.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }
      }

      .plan_add_img {
        width: 16px;
        height: 16px;
        background: url('@/assets/image/demand/add_white.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
    }

  }

  // table
  .interact_table_bgc_gray {
    /* 单元格背景色 */
    background: #f7f8fa;
  }
}
