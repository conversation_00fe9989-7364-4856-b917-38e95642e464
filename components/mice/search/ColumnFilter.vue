<!-- 表格列过滤组件 -->
<script setup lang="ts">
import {
    Button as hButton,
    Input as hInput,
    Select as hSelect,
    SelectOption as hSelectOption,
    RangePicker as hRangePicker,
} from 'ant-design-vue';
import { SearchOutlined } from '@ant-design/icons-vue';
import { PropType, ref, h, onMounted } from 'vue';

// 定义属性
const props = defineProps({
    // 列名
    column: {
        type: Object as PropType<any>,
        required: true
    },
    // 确认函数
    confirm: {
        type: Function as PropType<() => void>,
        required: true
    },
    // 清除过滤器函数
    clearFilters: {
        type: Function as PropType<() => void>,
        required: true
    },
    // 下拉选项（用于下拉框类型）
    options: {
        type: Array as PropType<{ value: string | number; label: string }[]>,
        default: () => []
    },
    // 列类型: 'input' | 'select' | 'date-range'
    filterType: {
        type: String as PropType<'input' | 'select' | 'date-range'>,
        default: 'input'
    },
    // 初始值（用于回显）
    initialValue: {
        type: [String, Number, Array] as PropType<string | number | [string, string] | null>,
        default: null
    }
});

// 定义事件
const emit = defineEmits<{
    (e: 'search', dataIndex: string, value: any): void;
    (e: 'reset', dataIndex: string): void;
}>();

// 过滤值
const filterValue = ref<any>('');
// 日期范围值 - 使用string类型以适配ant-design-vue的RangePicker
const dateRangeValue = ref<[string, string] | null>(null);

// 处理搜索
const handleSearch = () => {
    if (props.filterType === 'date-range') {
        if (dateRangeValue.value && dateRangeValue.value.length === 2) {
            // 设置开始日期为当天0点
            const startDate = new Date(dateRangeValue.value[0]);
            startDate.setHours(0, 0, 0, 0);
            
            // 设置结束日期为当天23:59:59
            const endDate = new Date(dateRangeValue.value[1]);
            endDate.setHours(23, 59, 59, 999);
            
            // 格式化为日期时间字符串
            const formatDate = (date: Date): string => {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const seconds = String(date.getSeconds()).padStart(2, '0');
                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            };
            
            // 发送开始和结束日期
            emit('search', props.column.dataIndex, {
                startDate: formatDate(startDate),
                endDate: formatDate(endDate),
                dateRange: dateRangeValue.value
            });
        } else {
            emit('search', props.column.dataIndex, null);
        }
    } else {
        emit('search', props.column.dataIndex, filterValue.value);
    }
    props.confirm();
};

// 处理重置
const handleReset = () => {
    filterValue.value = '';
    dateRangeValue.value = null;
    emit('reset', props.column.dataIndex);
    props.clearFilters();
};

// 初始化组件时设置初始值
onMounted(() => {
    if (props.initialValue !== null) {
        if (props.filterType === 'date-range' && Array.isArray(props.initialValue)) {
            dateRangeValue.value = props.initialValue as [string, string];
        } else {
            filterValue.value = props.initialValue;
        }
    }
});

// 渲染输入框
const renderInput = () => {
    return h(hInput, {
        value: filterValue.value,
        'onUpdate:value': (val: string) => {
            filterValue.value = val;
        },
        placeholder: `搜索${props.column.title}`,
        style: { width: '188px', marginBottom: '8px' },
        allowClear: true,
        onPressEnter: handleSearch,
        maxlength: 200
    });
};

// 渲染下拉框
const renderSelect = () => {
    return h(hSelect, {
        value: filterValue.value,
        'onUpdate:value': (val: any) => {
            filterValue.value = val;
        },
        placeholder: `请选择${props.column.title}`,
        style: { width: '188px', marginBottom: '8px' },
        allowClear: true,
        options: props.options
    });
};

// 渲染日期范围选择器
const renderDateRange = () => {
    // @ts-ignore - 忽略类型错误
    return h(hRangePicker, {
        value: dateRangeValue.value,
        'onUpdate:value': (val: [string, string] | null) => {
            console.log('日期选择:', val);
            dateRangeValue.value = val;
        },
        placeholder: ['开始日期', '结束日期'],
        style: { width: '100%', marginBottom: '8px' },
        allowClear: true,
        valueFormat: 'YYYY-MM-DD', // 使用字符串格式，避免使用dayjs
        format: 'YYYY-MM-DD'
    });
};

// 根据过滤类型获取渲染函数
const getFilterComponent = () => {
    if (props.filterType === 'select') {
        return renderSelect();
    } else if (props.filterType === 'date-range') {
        return renderDateRange();
    } else {
        return renderInput();
    }
};
</script>

<template>
    <div style="padding: 8px">
        <!-- 根据过滤类型渲染不同的控件 -->
        <component :is="getFilterComponent()" />

        <div style="display: flex; justify-content: space-between">
            <h-button type="primary" size="small" @click="handleSearch" style="width: 90px; margin-right: 8px">
                <SearchOutlined />搜索
            </h-button>
            <h-button size="small" @click="handleReset" style="width: 90px">
                重置
            </h-button>
        </div>
    </div>
</template>