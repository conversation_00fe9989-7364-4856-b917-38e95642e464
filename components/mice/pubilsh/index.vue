<script setup lang="ts">
import { message, DatePicker, TimePicker, Modal, Button } from 'ant-design-vue';
import VisibleTable from '@haierbusiness-front/components/mice/advisors/components/VisibleTable/index.vue';
import { miceBidManOrderListApi, fileApi } from '@haierbusiness-front/apis';
import { ref, reactive, onMounted, computed, inject, nextTick, watch } from 'vue';
import {
  MiceItemConstant,
  hotelLevelAllConstant,
  InteractState,
  lockStateConstant,
} from '@haierbusiness-front/common-libs';
import { useRouter, useRoute } from 'vue-router';
import type { VxeGridInstance } from 'vxe-table';
import { SearchOutlined, QuestionCircleOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { meetingProcessOrchestration, resolveParam } from '@haierbusiness-front/utils';
import dayjs, { Dayjs } from 'dayjs';
const widthSearch = ref('80%');
const route = useRoute();
const routeQuery = reactive({
  record: resolveParam(route.query.record) || JSON.parse(route.query.record),
});
const isCloseLastTab = inject<Ref<boolean>>('isCloseLastTab');
const columnsHotelJoin = ref([
  { title: '序号', width: '60px', type: 'seq', align: 'center' },
  {
    field: 'lockState',
    title: '是否参与',
    width: '100px',
    align: 'center',

    slots: {
      default: 'name_lockState',
    },
  },
  {
    field: 'hotelFlag',
    title: '标识',
    width: '100px',
    slots: {
      header: 'name_header_tip',
    },
  },

  {
    field: 'hotelName',
    title: '酒店名称',
    width: '180px',
    showOverflow: 'tooltip',
    slots: {
      header: 'name_header',
      default: 'name_default',
    },
  },
  { field: 'distance', title: '距离', width: '100px' },
]);
const columnsHotel = ref([
  { field: 'checked', title: '', width: '40px', align: 'center', slots: { default: 'checked_default' } },
  { title: '序号', width: '60px', type: 'seq', align: 'center' },
  {
    field: 'hotelFlag',
    title: '标识',
    width: '100px',
    slots: {
      header: 'name_header_tip',
    },
  },
  {
    field: 'name',
    title: '酒店名称',
    width: '180px',
    showOverflow: 'tooltip',
    slots: {
      header: 'name_header',
      default: 'name_default',
    },
  },
  { field: 'distance', title: '距离', width: '100px' },
]);
const columnsMerJoin = ref([
  { title: '序号', width: '60px', type: 'seq', align: 'center' },
  { field: 'merchantCode', title: '服务商编码', width: '120px', showOverflow: 'tooltip' },
  {
    field: 'merchantName',
    title: '服务商名称',
    slots: {
      header: 'name_header',
    },
    width: '200px',
    showOverflow: 'tooltip',
  },
  {
    field: 'interactState',
    title: '是否参与',
    width: '100px',
    showOverflow: 'tooltip',
    align: 'center',
    slots: {
      default: 'name_interactState',
    },
  },
]);
const columnsMer = ref([
  { title: '序号', width: '60px', type: 'seq', align: 'center' },
  { field: 'merchantCode', title: '服务商编码', width: '120px', showOverflow: 'tooltip' },
  {
    field: 'merchantName',
    title: '服务商名称',
    slots: {
      header: 'name_header',
    },
    width: '200px',
    showOverflow: 'tooltip',
  },
  {
    field: ['show'].includes(routeQuery.record?.status) ? 'merchantScore' : 'score',
    title: '服务商评分',
    width: '100px',
  },
]);
const configListC = ref([]);
const configListHotel = ref([]);
const check = ref(false);
const hideBtn = ref<string>('');
const hotelModal = ref(false);
watch(hotelModal, (newVal) => {
  if (!newVal) currentItem.gridOptions.data = currentItem.gridOptions.dataS;
});
const columns = ref([]);
const dataSource = ref([]);
const currentItem = reactive({});
const hours = ref<string[]>([
  '00',
  '01',
  '02',
  '03',
  '04',
  '05',
  '06',
  '07',
  '08',
  '09',
  '10',
  '11',
  '12',
  '13',
  '14',
  '15',
  '16',
  '17',
  '18',
  '19',
  '20',
  '21',
  '22',
  '23',
]);
const currentTableType = ref('');
const getHotelList = (hotel, type) => {
  currentTableType.value = type;
  Object.assign(currentItem, hotel);
  currentItem.gridOptions.Height = '100%';
  currentItem.gridOptions.data = currentItem.gridOptions.dataS.slice(0, 10);
  currentPage.value = 1;
  pageSize.value = 10;
  hotelModal.value = true;
};
const getList = async () => {
  const status = routeQuery.record?.status;
  let res;
  let res1;
  //scheme方案结果互动，/bidman/scheme/publish/view发布服务商列表，show需求发布复审
  if (['scheme', 'show'].includes(status) || ['/bidman/scheme/publish/view'].includes(route.path)) {
    getTopDataHD();
    res = await miceBidManOrderListApi.platformDetails({
      miceId: routeQuery.record.miceId,
      miceDemandId: routeQuery.record.miceDemandId,
    });
    getBottomDataHD(res?.hotels);
  } else {
    res = await miceBidManOrderListApi.platformDetails({
      miceId: routeQuery.record.miceId,
      miceDemandId: routeQuery.record.miceDemandId,
    });
    getConfig(res);
    res1 = await miceBidManOrderListApi.queryPoolIds({
      miceId: routeQuery.record.miceId,
      miceDemandId: res.id,
    });
    getTopData(res, res1);
    getBottomData(res);
  }
};
const isPush = (row: object, hotel: object) => {
  if (!hotel.check) {
    if (row.checked) return '取消';
    else return '推送';
  }
  return '推送';
};
const openDetail = (row: object, hotel: object) => {
  configListHotel.value.forEach((item) => {
    // 酒店表格信息
    if (item.id === hotel.id) {
      item.check = false;
      let sumCheck = true;
      item.gridOptions.dataS.forEach((item2) => {
        // 每条酒店信息
        if (item2.name === row.name) {
          if (hotel.check) {
            item2.checked = true;
          } else {
            item2.checked = !item2.checked;
          }
        }
        if (item2.checked) sumCheck = false;
      });
      if (sumCheck) item.check = true;
      Object.assign(currentItem, item);
    }
  });
};
const computedMethod = (num) => {
  if (!num) {
    return;
  }

  const list = MiceItemConstant.toArray().map((item) => item.code);
  // method
  const array = [];
  list.map((item) => {
    if ((num & item) != 0) {
      array.push(item);
    }
  });
  return array;
};
const loadTop = ref(true);
const getTopDataHD = async () => {
  loadTop.value = true;
  let list = await miceBidManOrderListApi.groupDetails({
    miceId: routeQuery.record.miceId,
  });
  if (list.length === 0) loadBottom.value = false;
  list.forEach(async (item, index) => {
    configListC.value[index] = {
      id: index,
      poolId: item.pdmMerchantPoolId,
      search: '',
      showSearch: false,
      title: item.pdmMerchantPoolName,
      tabList: computedMethod(item.pdmMerchantPoolItems),
      gridOptions: {
        tooltipConfig: {
          enterable: true,
          contentMethod: ({ type, column, row, items, _columnIndex }) => {
            // 重写默认的提示内容
            if (column.field === 'merchantName' && row.hotelName) {
              return row.hotelName;
            }
            if (column.field === 'interactState' && row.abandonReason) {
              return row.abandonReason;
            }
          },
        },
        border: 'inner',
        columnConfig: {
          resizable: true,
        },
        columns:
          ['/bidman/scheme/publish/view'].includes(route.path) || ['show'].includes(routeQuery.record?.status)
            ? columnsMer.value
            : columnsMerJoin.value,
        dataS: item.details,
        data: item.details,
      },
    };
    if (index === list.length - 1) {
      loadTop.value = false;
    }
  });
};
const getTopData = async (userDetail: object, list: Array) => {
  loadTop.value = true;
  if (list.length === 0) loadBottom.value = false;
  list.forEach(async (item, index) => {
    const res = await miceBidManOrderListApi.merchantList({
      'mice.miceId': routeQuery.record.miceId,
      'mice.miceDemandId': userDetail.id,
      poolId: item,
    });
    if (res === null) {
      loadBottom.value = false;
      return;
    } else
      configListC.value[index] = {
        id: index,
        poolId: item,
        search: '',
        showSearch: false,
        title: res.poolName,
        tabList: res.typeList,
        gridOptions: {
          tooltipConfig: {
            enterable: true,
            contentMethod: ({ type, column, row, items, _columnIndex }) => {
              // 重写默认的提示内容
              if (column.field === 'merchantName' && row.hotelName) {
                return row.hotelName;
              }
            },
          },
          border: 'inner',
          columnConfig: {
            resizable: true,
          },
          columns: columnsMer.value,
          dataS: res.merchantList,
          data: res.merchantList,
        },
      };
    if (index === list.length - 1) {
      loadTop.value = false;
    }
  });
};
// 禁止选择前一天时间
const disabledDate = (current) => {
  const today = dayjs();
  return current.isBefore(today, 'day');
};
const currentHour = ref<string>('');

const loadBottom = ref(true);
const getBottomDataHD = async (hotels: object) => {
  loadBottom.value = true;
  if (hotels?.length === 0) loadBottom.value = false;
  hotels?.forEach(async (item, index) => {
    let TempRes = await miceBidManOrderListApi.hotelsDetails({
      miceId: routeQuery.record.miceId,
      miceDemandHotelId: item.miceDemandHotelId || item.id,
    });
    let res = TempRes.records;
    configListHotel.value[index] = {
      id: index + Math.random,
      check: true,
      showSearch: false,
      showSearchTip: false,
      searchTip: '',
      search: '',
      title: (
        '酒店' + (index + 1) + '-' + item.centerMarker + '-' + hotelLevelAllConstant?.ofType(item.level)?.desc || ''
      ).replaceAll('-undefined', ''),
      gridOptions: {
        border: 'inner',
        columnConfig: {
          resizable: true,
        },
        tooltipConfig: {
          enterable: true,
          contentMethod: ({ type, column, row, items, _columnIndex }) => {
            // 重写默认的提示内容
            if (column.field === 'hotelName' && row.hotelName) {
              return row.hotelName;
            }
          },
        },
        columns:
          ['/bidman/scheme/publish/view'].includes(route.path) || ['show'].includes(routeQuery.record?.status)
            ? columnsHotel.value
            : columnsHotelJoin.value,
        dataS: res?.map((itemD) => {
          return {
            ...itemD,
            distance: itemD.distance ? itemD.distance + 'km' : '-',
            miceDemandHotelId: item.id,
            checked: false,
          };
        }),
        data: res?.map((itemD) => {
          return {
            ...itemD,
            distance: itemD.distance ? itemD.distance + 'km' : '-',
            miceDemandHotelId: item.id,
            checked: false,
          };
        }),
      },
    };
    if (index === hotels.length - 1) {
      loadBottom.value = false;
    }
  });
};
const getBottomData = async (userDetail: object) => {
  let tempArr = userDetail.hotels;
  let specilHotelRes;
  if (routeQuery.record?.status === 'show') {
    specilHotelRes = await miceBidManOrderListApi.specialHotelWithPool({
      miceId: routeQuery.record.miceId,
      miceDemandId: routeQuery.record.miceDemandId,
    });
    tempArr = specilHotelRes.map((item) => {
      return {
        id: item.miceDemandHotelId,
      };
    });
  }
  loadBottom.value = true;
  if (tempArr.length === 0) loadBottom.value = false;
  tempArr.forEach(async (item, index) => {
    const res = await miceBidManOrderListApi.satisfyList({
      miceId: routeQuery.record.miceId,
      miceDemandHotelId: item.id,
    });
    configListHotel.value[index] = {
      id: index,
      check: [1, 5].includes(specilHotelRes?.[index]?.selectedFlag) ? false : true,
      showSearch: false,
      showSearchTip: false,
      searchTip: '',
      search: '',
      title:
        '酒店' +
        (index + 1) +
        '-' +
        (item.centerMarker || '') +
        '-' +
        (hotelLevelAllConstant?.ofType(item.level)?.desc || ''),
      gridOptions: {
        border: 'inner',
        columnConfig: {
          resizable: true,
        },

        tooltipConfig: {
          enterable: true,
          contentMethod: ({ type, column, row, items, _columnIndex }) => {
            // 重写默认的提示内容
            if (column.field === 'name' && row.name) {
              return row.name;
            }
          },
        },
        checkboxConfig: {
          // checkAll: true,
          checkMethod: ({ row }) => {
            if (row?.name?.includes('青岛海尔洲际酒店') || row?.name?.includes('青岛海尔山庄(崂山仰口风景区)'))
              return true;
          },
        },
        columns: columnsHotel.value,
        dataS: res?.map((itemD) => {
          return {
            ...itemD,
            miceDemandHotelId: item.id,
            checked: [1, 5].includes(specilHotelRes?.[index]?.selectedFlag)
              ? specilHotelRes[index]?.specialHotelList?.map((hotel) => hotel.hotelName).includes(itemD.name)
              : false,
          };
        }),
        data: res?.map((itemD) => {
          return {
            ...itemD,
            miceDemandHotelId: item.id,
            checked: [1, 5].includes(specilHotelRes?.[index]?.selectedFlag)
              ? specilHotelRes[index]?.specialHotelList?.map((hotel) => hotel.hotelName).includes(itemD.name)
              : false,
          };
        }),
      },
    };
    if (index === tempArr.length - 1) {
      loadBottom.value = false;
    }
  });
};
const pushHotel = (row) => {
  const businessHotel = import.meta.env.VITE_BUSINESS_HOTEL + '/#';
  const url = businessHotel + '/hotel-analysis/hotelInfo?code=' + (row.code || row.hotelCode) + '&date';
  window.open(url);
};
const publishDate = ref<Dayjs>();
const publishHour = ref<Dayjs>();
const currentPublishHour = ref<Dayjs>();
const businessProcess = import.meta.env.VITE_BUSINESS_PROCESS_URL;
const approvalModalShow = ref(false);
const approveCode = ref<string>(''); // 审批流Code
const publishModalShow = ref(false);
const publishLoading = ref(false);
const currentPublishDate = ref<Dayjs>();
// 记录初始值，用于判断是否修改
const initialPublishDate = ref<Dayjs>();
const initialPublishHour = ref<string>('');
// 证明材料文件
const proofFile = ref<File | null>(null);
// 计算属性：判断是否修改了截止时间
const isPublishTimeChanged = computed(() => {
  return (
    dayjs(publishDate.value).format('YYYY-MM-DD') != dayjs(initialPublishDate.value).format('YYYY-MM-DD') ||
    publishHour.value != initialPublishHour.value
  );
});

const getCurrentHours = () => {
  if (dayjs(publishDate.value).format('YYYY-MM-DD') == dayjs(currentPublishDate.value).format('YYYY-MM-DD')) {
    publishHour.value = Number(currentPublishHour.value) + 1;
    if (!String(publishHour.value).includes(':00')) publishHour.value += ':00';
  }
};
const publishCheck = () => {
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const month = String(currentDate.getMonth() + 1).padStart(2, '0');
  let dayCurrent = currentDate.getDate();
  if (configCheck('demandPushSchemeSubmitAppointDurationConfigDefine')) {
    dayCurrent += Number(configCheck('demandPushSchemeSubmitDefaultDurationConfigDefine')?.split(',')[0]);
  } else if (configCheck('demandPushSchemeSubmitUrgentAppointDurationConfigDefine')) {
    dayCurrent += Number(configCheck('demandPushSchemeSubmitUrgentDurationConfigDefine')?.split(',')[0]);
  }
  const day = String(dayCurrent).padStart(2, '0');
  let hourCurrent = currentDate.getHours();
  let minuteCurrent = currentDate.getMinutes();
  if (configCheck('demandPushSchemeSubmitAppointDurationConfigDefine')) {
    hourCurrent = Number(
      configCheck('demandPushSchemeSubmitDefaultDurationConfigDefine')?.split(',')[1]?.split(':')[0],
    );
    minuteCurrent = Number(
      configCheck('demandPushSchemeSubmitDefaultDurationConfigDefine')?.split(',')[1]?.split(':')[1],
    );
  } else if (configCheck('demandPushSchemeSubmitUrgentAppointDurationConfigDefine')) {
    hourCurrent = Number(configCheck('demandPushSchemeSubmitUrgentDurationConfigDefine')?.split(',')[1]?.split(':')[0]);
    minuteCurrent = Number(
      configCheck('demandPushSchemeSubmitUrgentDurationConfigDefine')?.split(',')[1]?.split(':')[1],
    );
  }
  if (String(hourCurrent).includes('NaN')) hourCurrent = currentDate.getHours();
  const hour = String(hourCurrent).padStart(2, '0');
  currentHour.value = currentDate.getHours();
  const minute = String(minuteCurrent).padStart(2, '0');
  const second = String(currentDate.getSeconds()).padStart(2, '0');
  currentPublishDate.value = dayjs(`${year}-${month}-${String(currentDate.getDate()).padStart(2, '0')}`);
  publishDate.value = dayjs(`${year}-${month}-${day}`);
  publishHour.value = String(
    configCheck('demandPushSchemeSubmitAppointDurationConfigDefine') ||
      configCheck('demandPushSchemeSubmitUrgentAppointDurationConfigDefine')
      ? hourCurrent
      : currentDate.getHours() === '23'
      ? '00'
      : currentDate.getHours() + 2,
  ).padStart(2, '0');
  currentPublishHour.value = String(currentDate.getHours() === '23' ? '00' : currentDate.getHours() + 2).padStart(
    2,
    '0',
  );
  // 记录初始值
  initialPublishDate.value = publishDate.value;
  initialPublishHour.value = publishHour.value;
  proofFile.value = null; // 重置文件

  publishModalShow.value = true;
};
const isLt50M = ref<boolean>(true);

const beforeUpload = (file) => {
  isLt50M.value = file.size / 1024 / 1024 < 50;

  if (!isLt50M.value) {
    message.error('文件最大不超过50M！');
    return Upload.LIST_IGNORE;
  }

  return isLt50M.value;
};

const uploadLoading = ref<boolean>(false);
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const attachmentList = ref<array>([]);
const uploadRequest = (options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;
      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};
const handleRemove = (file: any) => {
  const index = attachmentList.value.findIndex((item) => item.uid === file.uid);
  if (index > -1) {
    attachmentList.value.splice(index, 1);
  }
};

const handlePublish = async () => {
  // 校验：如果修改了截止时间但未上传证明材料
  if (isPublishTimeChanged.value && attachmentList.value.length == 0) {
    message.error('请上传证明材料');
    return;
  }
  let tempPublishHour = publishHour.value;
  if (tempPublishHour.length == 5) tempPublishHour += ':00';
  else if (tempPublishHour.length == 2) tempPublishHour += ':00:00';
  publishLoading.value = true;
  // 获取当前时间(yyyy-MM-dd HH:mm:ss)
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const month = String(currentDate.getMonth() + 1).padStart(2, '0');
  const day = String(currentDate.getDate()).padStart(2, '0');
  const hour = String(currentDate.getHours()).padStart(2, '0');
  const minute = String(currentDate.getMinutes()).padStart(2, '0');
  const second = String(currentDate.getSeconds()).padStart(2, '0');

  const formattedDate = `${year}-${month}-${day} ${hour}:${minute}:${second}`;
  let hotelsArray = [];
  let codeSet = new Set();
  let hotelPush = false;
  configListHotel.value.forEach((item) => {
    let itemHotel = [];
    itemHotel = item.gridOptions.data.filter((item2) => item.check || item2.checked);
    itemHotel = itemHotel.map((item2) => {
      if (
        (item2.name.includes('青岛海尔洲际酒店') || item2.name.includes('青岛海尔山庄(崂山仰口风景区)')) &&
        itemHotel.length < 3
      ) {
        hotelPush = true;
      }
      codeSet.add(item2.code);
      return {
        hotelFlag: item2.hotelFlag,
        miceDemandHotelId: item2.miceDemandHotelId,
        hotelCode: item2.code,
        hotelName: item2.name,
        decorationYear: item2.decorationDate,
        distance: item2.distance == '-' ? null : parseFloat(item2.distance.split(' km')[0]),
        cityId: item2.cityId,
        cityName: item2.cityName,
        districtId: item2.regionId,
        hotelAddress: item2.enAddress,
        districtName: item2.regionName,
        level: item2.starLevel,
        latitude: item2.gdLat,
        longitude: item2.gdLon,
      };
    });

    hotelsArray = hotelsArray.concat(itemHotel);
  });
  let merchantsArray = [];
  configListC.value.forEach((item) => {
    merchantsArray.push(
      ...item.gridOptions.data
        .filter((item1) => {
          if (hotelPush) {
            if (item1.merchantType !== 1) return item1;
          } else {
            let hotelPush = false;
            if (item1.merchantType == 1 && item1.hotelCode) {
              item1.hotelCode.split(',').forEach((item2) => {
                if (codeSet.has(item2)) hotelPush = true;
              });
              if (hotelPush) return item1;
            } else return item1;
          }
        })
        .map((item2) => {
          return { pdmMerchantPoolId: item.poolId, merchantId: item2.id };
        }),
    );
  });
  if (merchantsArray.length == 0) {
    message.error('发布服务商不能为空！');
    publishLoading.value = false;
    publishModalShow.value = false;
    return;
  }
  // 如果有证明材料文件，使用FormData；否则使用普通对象
  let requestData = {
    miceId: routeQuery.record.miceId,
    pushDate: formattedDate,
    pushControlType: 0,
    interactEndDate: publishDate.value
      ? dayjs(publishDate.value).format('YYYY-MM-DD') + ' ' + tempPublishHour
      : formattedDate,
    merchants: merchantsArray,
    hotels: hotelsArray,
    attachment: undefined,
  };

  if (attachmentList.value.length > 0) {
    // 创建FormData对象用于文件上传
    requestData.attachment = attachmentList.value.map((item) => {
      return JSON.stringify({
        name: item.name,
        url: item.filePath,
      });
    });
  }
  const res = await miceBidManOrderListApi.publish(requestData);

  if (res.success && res.data && res.data.processCode) {
    approveCode.value = res.data.processCode;
    approvalModalShow.value = true;
    // router.push({ path: '/bidman/orderList/index', query: { status: '0' } });
  } else {
    message.error('需求发布失败');
    closeApproval();
  }
  publishLoading.value = false;
};
const router = useRouter();
const closeApproval = () => {
  router.push({
    path: '/bidman/orderList/index',
    query: {
      miceId: routeQuery.record.miceId,
      miceDemandId: routeQuery.record.miceDemandId,
      status: '0',
    },
  });

  // 关闭当前页签
  isCloseLastTab.value = true;
};
interface RowVO {
  name?: string;
  checked?: boolean;
  cityId?: number;
  cityName?: string;
  code?: string;
  decorationDate?: string;
  distance?: number;
  gdLat?: number;
  gdLon?: number;
  hotelFlag?: string;
  regionId?: number;
  starLevel?: number;
}
const gridRef = ref<VxeGridInstance<RowVO>>();

const confirmFilterEventC = (option) => {
  configListC.value.forEach((item) => {
    if (item === option) {
      if (option.search) {
        item.gridOptions.data = item.gridOptions.dataS.filter((item2) => {
          if (item2.merchantName.includes(option.search)) {
            return item2;
          }
        });
      } else {
        item.gridOptions.data = item.gridOptions.dataS;
      }
      item.showSearch = !item.showSearch;
    }
  });
};
const confirmFilterEventHTip = (option) => {
  configListHotel.value.forEach((item) => {
    if (item === option) {
      if (option.searchTip) {
        item.gridOptions.data = item.gridOptions.dataS.filter((item2) => {
          if (item2.hotelFlag.includes(option.searchTip)) {
            return item2;
          }
        });
      } else {
        item.gridOptions.data = item.gridOptions.dataS;
      }
      item.showSearchTip = !item.showSearchTip;
    }
  });
};
const confirmFilterEventH = (option) => {
  configListHotel.value.forEach((item) => {
    if (item === option) {
      if (option.search) {
        let name = item?.name ? 'name' : 'hotelName';
        item.gridOptions.data = item.gridOptions.dataS.filter((item2) => {
          if (item2?.[name].includes(option.search)) {
            return item2;
          }
        });
      } else {
        item.gridOptions.data = item.gridOptions.dataS;
      }
      item.showSearch = !item.showSearch;
    }
  });
};
const handleOk = () => {
  approvalModalShow.value = false;
};
const frameModel = ref(inject<any>('frameModel'));

const pageSize = ref(10);
const currentPage = ref(1);
const total = computed(() =>
  Array.isArray(currentItem.gridOptions?.dataS) ? currentItem.gridOptions.dataS.length : 0,
);

// 计算当前页数据
const pagedData = computed(() => {
  if (!Array.isArray(currentItem.gridOptions?.dataS)) return [];
  const start = (currentPage.value - 1) * pageSize.value;
  return currentItem.gridOptions.dataS.slice(start, start + pageSize.value);
});

// 监听分页变化，更新表格数据
watch(
  [
    currentPage,
    pageSize,
    () => {
      return currentItem.gridOptions?.dataS;
    },
  ],
  () => {
    if (pagedData?.value && currentItem.gridOptions?.data) currentItem.gridOptions.data = pagedData?.value;
  },
  { immediate: true },
);

const handlePageChange = (page, pageSize) => {
  currentPage.value = page;
  // pageSize.value = pageSize;
};
const configNode = reactive({});
const getConfig = async (demand) => {
  const res = await miceBidManOrderListApi.processDetails({
    id: demand.pdMainId,
    verId: demand.pdVerId,
  });
  res.nodes.forEach((item) => {
    if (['DEMAND_PUSH'].includes(item.metaKey)) {
      Object.assign(configNode, item);
    }
  });
  console.log('configNode', configNode);
};

const configCheck = (str) => {
  let res = false;
  configNode?.configs?.forEach((item) => {
    if (item.metaKey === str) {
      if (item.configParam == '1') res = true;
      else if (item.configParam == '0') res = false;
      else res = item.configParam;
    }
  });
  return res;
};
onMounted(() => {
  let element = document.getElementById('myElement')?.offsetWidth + 'px';
  widthSearch.value = element || '80%';
  window.addEventListener('resize', function () {
    let element = document.getElementById('myElement')?.offsetWidth + 'px';
    widthSearch.value = element || '80%';
  });
  hideBtn.value = routeQuery.record.hideBtn;
  frameModel.value = routeQuery.record.hideBtn === '1' ? 1 : 0;
  getList();
});
</script>
<template>
  <div class="publish-container" id="myElement">
    <div class="publish-top">
      <a-spin class="top-spin" v-show="loadTop" />
      <div class="empty" v-show="!loadTop && configListC.length === 0">
        <a-empty />
      </div>

      <div class="publish-title">
        <div class="publish-top-left"></div>
        <div class="title-span">
          参与服务商
          <a-tooltip>
            <template #title
              >直签酒店服务商筛选逻辑说明：<br />
              1.若涉及多酒店需求，则不推荐推送直签酒店服务商；<br />
              2.针对单酒店需求，首先根据对应流程的资源池筛选出所有业务类型符合用户需求条件的直签酒店服务商；<br />
              3.查询这些服务商所关联的酒店资源信息；<br />
              4.将符合条件的直签酒店服务商关联的酒店信息与满足需求的酒店资源进行交集筛选，确保最终筛选出能完整满足酒店需求的直签酒店服务商。</template
            >
            <QuestionCircleOutlined style="color: #ccc; font-size: 18px" />
          </a-tooltip>
        </div>
      </div>
      <div class="publish-content" v-for="(item, index) in configListC" :key="item?.id" v-show="item?.title">
        <div class="content-top flex">
          <a-button class="more-btn" type="link" @click="getHotelList(item, 'merchant')"><SearchOutlined /></a-button>
          <!-- <div class="top-icon">
            <img src="@/assets/image/publish/plane.png" alt="plane" width="32" height="32" />
          </div> -->
          <div class="top-title">
            {{ item?.title }}
          </div>
          <a-tooltip>
            <template #title>{{ item?.tabList.map((tab) => MiceItemConstant?.ofType(tab)?.desc).join(',') }}</template>
            <div class="top-btn">
              <a-button size="small" type="primary" v-for="tab in item?.tabList" :key="tab">{{
                MiceItemConstant?.ofType(tab)?.desc
              }}</a-button>
            </div>
          </a-tooltip>
        </div>
        <div class="top-content">
          <div class="content-search" v-if="item?.showSearch">
            <vxe-input
              @blur="item.showSearch = false"
              type="text"
              v-model="item.search"
              size="small"
              @keydown.enter="confirmFilterEventC(item)"
              @clear="confirmFilterEventC(item)"
              placeholder="服务商名称"
              clearable
            ></vxe-input>
            <div class="content-search-btn">
              <a-button size="small" type="primary" @click="confirmFilterEventC(item)">搜索</a-button>
              <a-button size="small" @click="(item.search = ''), confirmFilterEventC(item)">重置</a-button>
            </div>
          </div>
          <vxe-grid
            size="mini"
            ref="gridRef"
            :auto-resize="true"
            height="100%"
            :column-config="{
              useKey: true,
              resizable: true,
            }"
            :scroll-y="{
              enabled: true,
            }"
            :row-config="{
              useKey: true,
            }"
            class="mygrid-style"
            v-bind="item?.gridOptions"
          >
            <template #name_header="{ column }">
              <div class="name_header">
                <div>{{ column.title }}</div>
                <SearchOutlined @click="item.showSearch = !item.showSearch" />
              </div>
            </template>
            <template #name_interactState="{ row }">
              {{ InteractState?.ofType(row.interactState)?.desc || '' }}
            </template>
          </vxe-grid>
        </div>
      </div>
    </div>

    <div class="publish-bottom">
      <a-spin class="top-spin" v-show="loadBottom" />
      <div class="empty" v-show="!loadBottom && configListHotel.length === 0">
        <a-empty />
      </div>
      <div class="publish-title1">
        <div class="publish-top-left"></div>
        <div class="title-span">满足需求酒店</div>
      </div>
      <div
        :style="routeQuery.record?.status == 'scheme' ? 'width:570px' : ''"
        class="publish-content1"
        v-for="(item, index) in configListHotel"
        :key="item?.id"
      >
        <div
          :style="routeQuery.record?.status == 'scheme' ? 'left:170px' : ''"
          class="content-search-tip"
          v-if="item?.showSearchTip"
        >
          <vxe-input
            @blur="item.showSearch = false"
            type="text"
            v-model="item.searchTip"
            size="small"
            @keydown.enter="confirmFilterEventHTip(item)"
            @clear="confirmFilterEventHTip(item)"
            placeholder="标识"
            clearable
          ></vxe-input>
          <div class="content-search-btn">
            <a-button size="small" type="primary" @click="confirmFilterEventHTip(item)">搜索</a-button>
            <a-button
              size="small"
              @click="
                item.searchTip = '';
                confirmFilterEventHTip(item);
              "
              >重置</a-button
            >
          </div>
        </div>
        <div
          :style="routeQuery.record?.status == 'scheme' ? 'left:269px' : ''"
          class="content-search1"
          v-if="item?.showSearch"
        >
          <vxe-input
            @blur="item.showSearch = false"
            type="text"
            v-model="item.search"
            size="small"
            @keydown.enter="confirmFilterEventH(item)"
            @clear="confirmFilterEventH(item)"
            placeholder="酒店名称"
            clearable
          ></vxe-input>
          <div class="content-search-btn">
            <a-button size="small" type="primary" @click="confirmFilterEventH(item)">搜索</a-button>
            <a-button size="small" @click="(item.search = ''), confirmFilterEventH(item)">重置</a-button>
          </div>
        </div>
        <div class="content-top flex">
          <a-button class="more-btn" type="link" @click="getHotelList(item, 'hotel')"><SearchOutlined /></a-button>
          <!-- <div class="top-icon">
            <img src="@/assets/image/publish/plane.png" alt="plane" width="32" height="32" />
          </div> -->
          <div class="top-title" style="width: 90%">
            <a-tooltip>
              <template #title>{{ item?.title.replaceAll('--', '') }}</template>
              {{ item?.title.replaceAll('--', '') }}
            </a-tooltip>
          </div>
        </div>
        <div class="top-content">
          <vxe-grid
            size="mini"
            ref="gridRef"
            :auto-resize="true"
            :column-config="{
              useKey: true,
              resizable: true,
            }"
            :scroll-y="{
              enabled: true,
            }"
            height="100%"
            :row-config="{
              useKey: true,
            }"
            class="mygrid-style"
            v-bind="item?.gridOptions"
          >
            <template #name_header="{ column }">
              <div class="name_header">
                <div>{{ column.title }}</div>
                <SearchOutlined @click="item.showSearch = !item.showSearch" />
              </div>
            </template>
            <template #name_header_tip="{ column }">
              <div class="name_header_tip">
                <div>{{ column.title }}</div>
                <SearchOutlined @click="item.showSearchTip = !item.showSearchTip" />
              </div>
            </template>
            <template #name_default="{ row }">
              <div class="name_default">
                <span
                  @click="pushHotel(row)"
                  :style="{
                    cursor: 'pointer',
                    color: '#2079F2',
                    width:
                      row?.name?.includes('青岛海尔洲际酒店') || row?.name?.includes('青岛海尔山庄(崂山仰口风景区)')
                        ? '140px'
                        : '160px',
                  }"
                >
                  {{ row.name || row.hotelName }}
                </span>
                <a-button
                  v-if="!['scheme', 'show'].includes(routeQuery.record?.status)"
                  v-show="
                    hideBtn !== '1' &&
                    (row.name.includes('青岛海尔洲际酒店') || row.name.includes('青岛海尔山庄(崂山仰口风景区)'))
                  "
                  size="small"
                  :type="isPush(row, item) === '推送' ? 'primary' : 'Default'"
                  @click="openDetail(row, item)"
                  >{{ isPush(row, item) }}</a-button
                >
              </div>
            </template>
            <template #checked_default="{ row }">
              <a-checkbox
                :disabled="
                  !configCheck('demandPushSelectedHotelConfigDefine') || ['show'].includes(routeQuery.record?.status)
                "
                size="mini"
                @click="
                  () => {
                    configCheck('demandPushSelectedHotelConfigDefine') ? openDetail(row, item) : '';
                  }
                "
                :checked="item.check ? item.check : row.checked"
              />
            </template>
            <template #name_lockState="{ row }">
              {{ lockStateConstant?.ofType(row.lockState)?.desc || '' }}
            </template>
          </vxe-grid>
        </div>
      </div>
    </div>
    <div class="publish-btn" v-if="hideBtn !== '1'" :style="`width:${widthSearch}`">
      <a-button
        class="mr10"
        @click="
          router.go(-1);
          isCloseLastTab = true;
        "
        >返回</a-button
      >
      <a-button v-if="!['scheme', 'show'].includes(routeQuery.record?.status)" type="primary" @click="publishCheck()"
        >需求发布</a-button
      >
    </div>

    <a-modal
      v-model:open="approvalModalShow"
      title="已提交如下人员审批"
      width="80%"
      v-if="approvalModalShow"
      :keyboard="false"
      :maskClosable="false"
      :closable="false"
    >
      <div>
        <iframe width="100%" :src="businessProcess + '?code=' + approveCode + '#/detailsPcSt'" frameborder="0"></iframe>
      </div>
      <template #footer>
        <a-button
          @click="
            approvalModalShow = false;
            router.push({ path: '/bidman/orderList/index', query: { status: '0' } });
            isCloseLastTab = true;
          "
          >确定</a-button
        >
      </template>
    </a-modal>

    <a-modal v-model:open="publishModalShow" :footer="null" title="需求发布确认" width="700px" v-if="publishModalShow">
      <a-spin :spinning="publishLoading">
        <div
          v-if="
            configCheck('demandPushSchemeSubmitAppointDurationConfigDefine') ||
            configCheck('demandPushSchemeSubmitUrgentAppointDurationConfigDefine')
          "
        >
          <span style="margin-right: 20px">方案互动截止时间：</span>
          <a-date-picker
            v-model:value="publishDate"
            @change="getCurrentHours"
            format="YYYY-MM-DD"
            :disabled-date="disabledDate"
          />
          <a-select v-model:value="publishHour" style="margin-left: 10px; width: 100px">
            <a-select-option
              v-for="hour in hours"
              :disabled="
                dayjs(publishDate).format('YYYY-MM-DD') == dayjs(currentPublishDate).format('YYYY-MM-DD')
                  ? hour <= currentHour
                  : false
              "
              :key="hour"
              :value="hour"
              >{{ hour + ':00' }}</a-select-option
            >
          </a-select>
        </div>
        <div v-else>是否确认发布？</div>
        <!-- 证明材料上传区域 -->
        <div v-if="isPublishTimeChanged" style="margin-top: 20px">
          <div style="margin-bottom: 10px; color: #ff4d4f; font-size: 14px">
            <QuestionCircleOutlined style="margin-right: 5px" />
            由于修改了截止时间，请上传证明材料
          </div>
          <a-upload
            :before-upload="beforeUpload"
            v-model:fileList="attachmentList"
            :custom-request="uploadRequest"
            @remove="handleRemove"
            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
            :multiple="true"
            :max-count="10"
            :showUploadList="{ showRemoveIcon: true }"
          >
            <a-button type="primary">
              <UploadOutlined />
              上传证明材料
            </a-button>
          </a-upload>
        </div>

        <div class="footer">
          <a-button type="primary" @click="handlePublish">确定</a-button>
          <a-button @click="publishModalShow = false">取消</a-button>
        </div>
      </a-spin>
    </a-modal>
    <a-modal
      v-if="hotelModal"
      v-model:open="hotelModal"
      :width="routeQuery.record?.status === 'scheme' && currentTableType === 'hotel' ? '620px' : '550px'"
      :footer="null"
      :title="currentItem.title"
    >
      <div
        v-if="currentTableType === 'hotel'"
        :style="routeQuery.record?.status == 'scheme' ? 'width:570px' : ''"
        class="publish-content1-modal"
        :key="currentItem?.id"
      >
        <div
          :style="routeQuery.record?.status == 'scheme' ? 'left: 170px; top: 47px' : 'left: 110px; top: 47px'"
          class="content-search-tip"
          v-show="currentItem.showSearchTip"
        >
          <vxe-input
            @blur="currentItem.showSearch = false"
            type="text"
            v-model="currentItem.searchTip"
            size="small"
            @keydown.enter="confirmFilterEventHTip(currentItem)"
            @clear="confirmFilterEventHTip(currentItem)"
            placeholder="标识"
            clearable
          ></vxe-input>
          <div class="content-search-btn">
            <a-button size="small" type="primary" @click="confirmFilterEventHTip(currentItem)">搜索</a-button>
            <a-button
              size="small"
              @click="
                currentItem.searchTip = '';
                confirmFilterEventHTip(currentItem);
              "
              >重置</a-button
            >
          </div>
        </div>
        <div
          :style="routeQuery.record?.status == 'scheme' ? 'left: 270px; top: 47px' : 'left: 210px; top: 47px'"
          class="content-search1"
          v-show="currentItem.showSearch"
        >
          <vxe-input
            @blur="currentItem.showSearch = false"
            type="text"
            v-model="currentItem.search"
            size="small"
            @keydown.enter="confirmFilterEventH(currentItem)"
            @clear="confirmFilterEventH(currentItem)"
            placeholder="酒店名称"
            clearable
          ></vxe-input>
          <div class="content-search-btn">
            <a-button size="small" type="primary" @click="confirmFilterEventH(currentItem)">搜索</a-button>
            <a-button size="small" @click="(currentItem.search = ''), confirmFilterEventH(currentItem)">重置</a-button>
          </div>
        </div>
        <div class="top-content">
          <vxe-grid
            size="mini"
            ref="gridRef"
            :auto-resize="true"
            :column-config="{
              useKey: true,
              resizable: true,
            }"
            :scroll-y="{
              enabled: true,
            }"
            :row-config="{
              useKey: true,
            }"
            class="mygrid-style"
            v-bind="currentItem?.gridOptions"
          >
            <template #name_header="{ column }">
              <div class="name_header">
                <div>{{ column.title }}</div>
                <SearchOutlined @click="currentItem.showSearch = !currentItem.showSearch" />
              </div>
            </template>
            <template #name_header_tip="{ column }">
              <div class="name_header_tip">
                <div>{{ column.title }}</div>
                <SearchOutlined @click="currentItem.showSearchTip = !currentItem.showSearchTip" />
              </div>
            </template>
            <template #name_default="{ row }">
              <div class="name_default">
                <span
                  @click="pushHotel(row)"
                  :style="{
                    cursor: 'pointer',
                    color: '#2079F2',
                    width:
                      row?.name?.includes('青岛海尔洲际酒店') || row?.name?.includes('青岛海尔山庄(崂山仰口风景区)')
                        ? '140px'
                        : '160px',
                  }"
                >
                  {{ row.name || row.hotelName }}
                </span>
                <a-button
                  v-if="!['scheme', 'show'].includes(routeQuery.record?.status)"
                  v-show="
                    hideBtn !== '1' &&
                    (row.name.includes('青岛海尔洲际酒店') || row.name.includes('青岛海尔山庄(崂山仰口风景区)'))
                  "
                  size="small"
                  :type="isPush(row, currentItem) === '推送' ? 'primary' : 'Default'"
                  @click="openDetail(row, currentItem)"
                  >{{ isPush(row, currentItem) }}</a-button
                >
              </div>
            </template>
            <template #checked_default="{ row }">
              <a-checkbox
                :disabled="
                  !configCheck('demandPushSelectedHotelConfigDefine') || ['show'].includes(routeQuery.record?.status)
                "
                size="mini"
                @click="
                  () => {
                    configCheck('demandPushSelectedHotelConfigDefine') ? openDetail(row, currentItem) : '';
                  }
                "
                :checked="currentItem.check ? currentItem.check : row.checked"
              />
            </template>
            <template #name_lockState="{ row }">
              {{ lockStateConstant?.ofType(row.lockState)?.desc || '' }}
            </template>
          </vxe-grid>
        </div>
      </div>
      <div v-else class="publish-content-modal">
        <div class="top-content">
          <div class="content-search" v-show="currentItem.showSearch">
            <vxe-input
              @blur="currentItem.showSearch = false"
              type="text"
              v-model="currentItem.search"
              size="small"
              @keydown.enter="confirmFilterEventC(currentItem)"
              @clear="confirmFilterEventC(currentItem)"
              placeholder="服务商名称"
              clearable
            ></vxe-input>
            <div class="content-search-btn">
              <a-button size="small" type="primary" @click="confirmFilterEventC(currentItem)">搜索</a-button>
              <a-button size="small" @click="(currentItem.search = ''), confirmFilterEventC(currentItem)"
                >重置</a-button
              >
            </div>
          </div>
          <vxe-grid
            size="mini"
            ref="gridRef"
            :auto-resize="true"
            height="100%"
            :column-config="{
              useKey: true,
              resizable: true,
            }"
            :scroll-y="{
              enabled: true,
            }"
            :row-config="{
              useKey: true,
            }"
            class="mygrid-style"
            v-bind="currentItem?.gridOptions"
          >
            <template #name_header="{ column }">
              <div class="name_header">
                <div>{{ column.title }}</div>
                <SearchOutlined @click="currentItem.showSearch = !currentItem.showSearch" />
              </div>
            </template>
            <template #name_interactState="{ row }">
              {{ InteractState?.ofType(row.interactState)?.desc || '' }}
            </template>
          </vxe-grid>
        </div>
      </div>
      <a-pagination
        v-model:current="currentPage"
        @change="handlePageChange"
        :total="total"
        :showSizeChanger="false"
        show-less-items
      />
      <!-- <vxe-pager
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        @page-change="handlePageChange"
        style="margin-top: 16px; text-align: right"
      /> -->
    </a-modal>
    <slot name="footer" class="pushlish-footer"></slot>
  </div>
</template>
<style lang="less" scoped>
.publish-container {
  min-height: 600px;
  // padding-bottom: 40px;
  position: relative;
  background-color: #fff;
  width: 100%;
  height: 100%;
  background: #fff;
}
.publish-top {
  padding-top: 40px;
  width: 100%;
  height: 46%;
  overflow: auto;
  white-space: nowrap;
}
.top-spin {
  width: 100%;
  height: 100%;
  display: flex;
  text-align: center;
  justify-content: space-around;
  align-items: center;
}
.tableC {
  padding: 20px;
  padding-top: 0;
  width: 100%;
  height: 100%;
}
:deep(.vxe-header--row) {
  background: #fff;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #86909c;
  line-height: 20px;
  text-align: left;
  font-style: normal;
}
:deep(.vxe-cell--label) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  // color: #1d2129;
  line-height: 20px;
  padding: 0 11px;
  text-align: left;
  font-style: normal;
}
:deep(.vxe-header--column) {
  padding: 0 11px;
}
::v-deep(.mygrid-style) {
  // .full-border {
  //   border-right: 1px solid #e9eaec;
  // }
  .filed-first2 {
    border-left: 1px solid #e9eaec;
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-size: 14px;
    color: #86909c;
  }
  .filed-first3 {
    text-align: center;
    font-family: PingFangSC, PingFang SC;
    font-size: 14px;
    color: #86909c;
  }
  .filed-first4 {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    // color: #1d2129;
    line-height: 20px;
    // padding: 0 11px;
    text-align: left;
    font-style: normal;
    color: #007aff;
    cursor: pointer;
    /* text-decoration: underline; */
  }
  .filed-first {
    border-left: 1px solid #e9eaec;
    padding: 0 11px;
    text-align: right;
    font-family: PingFangSC, PingFang SC;
    font-size: 14px;
    color: #86909c;
  }
  .vxe-cell--html {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    height: 100%;
    padding-left: 11px;
  }
  .row-bg1 {
    text-align: left;
    background-color: #f2f3f5;
    color: #1d2129 !important;
  }
  .cell-bg1 {
    background-color: #f1faee;
    color: #1d2129 !important;
  }
  .cell-bg2 {
    background-color: #fff9ed;
    color: #1d2129 !important;
  }
  .cell-bg3 {
    background-color: #fff1f0;
    color: #1d2129 !important;
  }
}
.publish-bottom {
  padding-top: 40px;
  // margin-top: 1%;
  width: 100%;
  height: 47%;
  overflow: auto;
  white-space: nowrap;
}
.publish-btn {
  background-color: #fff;
  border-top: 1px solid #e5e6eb;
  position: fixed;
  bottom: 0px;
  line-height: 40px;
  right: 10px;
  width: calc(100% - 270px);
  height: 40px;
  text-align: right;
  z-index: 0;
  padding-right: 20px;
  // :deep(.ant-btn) {
  //   background: rgba(255, 255, 255, 0);
  //   border-radius: 4px;
  //   border: 1px solid rgba(24, 104, 219, 0.6);
  //   color: #1868db;
  //   font-size: 14px;
  //   z-index: 1000;
  // }
}
.publish-title {
  position: absolute;
  top: 0;
  left: 0;
  padding-top: 12px;
  padding-left: 24px;
  display: flex;
  align-items: center;
  padding-left: 20px;
}
.publish-title1 {
  position: absolute;
  top: 46%;
  left: 0;
  padding-top: 12px;
  padding-left: 24px;
  display: flex;
  align-items: center;
  padding-left: 20px;
}
.publish-top-left {
  margin-right: 20px;
  display: inline-block;
  width: 4px;
  height: 18px;
  background: #1868db;
}
.title-span {
  display: inline-block;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 20px;
  color: #1d2129;
  line-height: 28px;
  text-align: left;
  font-style: normal;
}
.publish-content {
  display: inline-block;
  align-items: center;
  margin-top: 10px;
  margin-left: 24px;
  width: 510px;
  height: calc(100% - 40px);
  border-radius: 8px;
  border: 1px solid #e5e6eb;
  vertical-align: top;
}
.publish-content-modal {
  position: relative;
  display: inline-block;
}
.publish-content1 {
  position: relative;
  display: inline-block;
  align-items: center;
  margin-top: 20px;
  margin-left: 24px;
  width: 510px;
  height: calc(100% - 40px);
  border-radius: 8px;
  border: 1px solid #e5e6eb;
  vertical-align: top;
}
.publish-content1-modal {
  position: relative;
  display: inline-block;

  .top-content {
    position: relative;
    overflow: hidden;
    height: null;
    padding: 10px;
  }
}
.content-top {
  width: 100%;
  height: 20px;
  padding: 10px;
  padding-top: 20px;
  padding-left: 0px;
  align-items: center;
}
.top-icon {
  width: 8%;
  // padding-left: 10px;
}
.top-title {
  // margin-left: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 400px;
  white-space: nowrap;
  margin-right: 40px;
  // vertical-align: middle;
  text-align: left;
  font-size: 14px;
  display: inline-block;
  align-items: center;
  font-weight: bold;
}
.top-btn {
  width: 80%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: right;
  padding-right: 0px;
  :deep(.ant-btn) {
    background: rgba(255, 255, 255, 0);
    border-radius: 4px;
    border: 1px solid rgba(24, 104, 219, 0.6);
    color: #1868db;
    font-size: 12px;
    margin-right: 4px;
  }
}
.top-content {
  position: relative;
  overflow: hidden;
  height: calc(100% - 40px);
  padding: 10px;
}
:deep(.vxe-header--row) {
  background: #f7f8fa;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #1d2129;
  line-height: 22px;
  text-align: left;
  font-style: normal;
}
:deep(.vxe-grid--layout-body-content-wrapper) {
  overflow: hidden;
  // height: calc(100% - 70px);
}
.flex {
  display: flex;
  justify-content: space-around;
}
.content-search {
  padding: 5px;
  text-align: center;
  width: 200px;
  height: 70px;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  position: absolute;
  left: 188px;
  top: 47px;
  z-index: 999;
  :deep(.vxe-input) {
    width: 180px;
  }
  .content-search-btn {
    padding: 5px;
    display: flex;
    justify-content: space-between;
  }
}
.content-search1 {
  padding: 5px;
  text-align: center;
  width: 180px;
  height: 70px;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  position: absolute;
  left: 208px;
  top: 77px;
  z-index: 999;
  :deep(.vxe-input) {
    width: 160px;
  }
  .content-search-btn {
    padding: 5px;
    display: flex;
    justify-content: space-between;
  }
}
.content-search-tip {
  padding: 5px;
  text-align: center;
  width: 100px;
  height: 70px;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  position: absolute;
  left: 108px;
  top: 77px;
  z-index: 999;
  :deep(.vxe-input) {
    width: 80px;
  }
  .content-search-btn {
    padding: 5px 0;
    display: flex;
    justify-content: space-between;
    :deep(.ant-btn) {
      padding: 0 5px;
    }
  }
}
.name_header_tip {
  width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  span {
    width: 70px;
    display: inline-block;
  }
  .anticon-search {
    cursor: pointer;
  }
}
.name_header {
  width: 240px;
  display: flex;
  align-items: center;
  justify-content: center;
  span {
    width: 100px;
    display: inline-block;
  }
  .anticon-search {
    cursor: pointer;
  }
}
.name_default1 {
  font-size: 14px;
  white-space: nowrap;
  width: 150px;
}
.name_default {
  width: 100%;

  position: relative;
  span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    font-size: 14px;
  }
  :deep(.ant-btn) {
    position: absolute;
    right: 0px;
    top: 0;
  }
}
:deep(.ant-btn-Default) {
  border: 1px solid #ccc;
}
:deep(.ant-picker-ranges) {
  display: inline-block;
}
.footer {
  margin-top: 20px;
  text-align: right;
  :deep(.ant-btn) {
    margin-left: 10px;
  }
}
.empty {
  height: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.more-btn {
  color: #007aff;
}
</style>
