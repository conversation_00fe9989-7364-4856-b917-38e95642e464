<!-- // 顾问列表页面 -->
<script lang="ts" setup>
import { But<PERSON>, Modal, Drawer, message, Collapse, CollapsePanel } from 'ant-design-vue';
import { Button as hButton } from 'ant-design-vue';
import Advisors from '@haierbusiness-front/components/mice/advisors/index.vue';
import { ref, reactive, computed, useAttrs, onMounted, inject } from 'vue';
import MeetingConsultantDrawer from '@haierbusiness-front/components/meetingConsultantDrawer/index.vue';
import { getDealTime, routerParam, resolveParam } from '@haierbusiness-front/utils';
import { miceBidManOrderListApi } from '@haierbusiness-front/apis';
import { TestData } from '@haierbusiness-front/common-libs';
import { useRouter, useRoute } from 'vue-router';
import { DownOutlined, UpOutlined } from '@ant-design/icons-vue';
import finalNode from '@haierbusiness-front/components/mice/orderList/finalNode.vue';
import orderLog from '@haierbusiness-front/components/mice/orderList/orderLog.vue';
import memorandum from '@haierbusiness-front/components/mice/orderList/memorandum.vue';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';
const store = applicationStore();
// 状态变量定义
const editModalOpen = ref(false); // 编辑抽屉是否打开
const testData = ref<TestData[]>([]); // 顾问列表数据
const testDataSum = ref<TestData[]>([]); // 顾问列表数据备份
const open = ref(false); // 驳回弹窗是否打开
const reason = ref(''); // 驳回原因
const route = useRoute();
const footerChecked = ref('a');
const $attrs = useAttrs();
const isCloseLastTab = inject<Ref<boolean>>('isCloseLastTab');
// 处理驳回操作
const handleReject = () => {
  open.value = true;
  console.log('驳回');
};

// 确认驳回
const handleOk = async () => {
  const res = await miceBidManOrderListApi.receive_reject({
    miceId: route.query.miceId,
    demandRejectReason: reason.value,
  });
  console.log(res);
  if (res.success) {
    message.success('驳回成功');
    open.value = false;
    router.push('/bidman/orderList/index');
  }
};

const router = useRouter();
// 分配人员
const assignPerson = async (item) => {
  const res = await miceBidManOrderListApi.userAssign({
    username: item.username,
    id: route.query.miceId,
  });
  if (res.success) {
    message.success('分配成功');
    editModalOpen.value = false;
    router.push('/bidman/orderList/index');
  }
};

// 处理发布
const handlePublish = () => {
  console.log('需求发布');
};

// 搜索关键词
const searchKeyword = (value) => {
  if (value)
    testData.value = testDataSum.value.filter((item) => {
      if (item.username.includes(value) || item.name.includes(value)) return item;
    });
  else testData.value = testDataSum.value;
};

// 方案视图
const handleScheme = (type) => {
  let query = {
    miceId: route.query?.miceId,
    miceDemandId: route.query?.miceDemandId,
    type: type,
    record: route.query?.record,
  };
  if (route.path === '/bidman/scheme/view') router.push({ path: '/bidman/scheme/index', query: query });
  else if (route.path === '/bidman/scheme/confirm/view') router.push({ path: '/bidman/scheme/confirm', query: query });
  else if (route.path === '/bidman/bid/view') router.push({ path: '/bidman/bid/index', query: query });
  isCloseLastTab.value = true;
};
const view = ref(null);
const hideBtn = ref('0');
const frameModel = ref(inject<any>('frameModel'));
const record = resolveParam(route.query.record);
const backupOrderList = () => {
  if ($attrs.orderSource === 'manage') {
    // 关闭当前页签
    isCloseLastTab.value = true;
    router.push('/bidman/orderList/index');
  } else {
    const localUrl = window.location.href;

    const businessMiceBid = import.meta.env.VITE_BUSINESS_INDEX_URL + '#';

    // 跳转需求确认页面
    const url = businessMiceBid + '/card-order/miceOrder?record=' + routerParam(resolveParam(route.query.record));
    window.location.href = url;
    return;
  }
};
onMounted(() => {
  hideBtn.value = record?.hideBtn || '0';
  frameModel.value = hideBtn.value === '1' ? 1 : 0;
  permission();
});

// 查看流程节点
const showProcess = ref<boolean>(false);

const viewProcess = () => {
  showProcess.value = !showProcess.value;
};

// 备忘录
const viewMemo = () => {
  memorandumvisible.value = true;
};
// 日志
const viewLog = () => {
  visible.value = true;
};
const arrConfirmView = ref([
  'COST_APPROVAL',
  'MICE_PENDING',
  'MICE_EXECUTION',
  'MICE_COMPLETED',
  'BILL_CONFIRM',
  'BILL_APPROVAL',
  'BILL_RE_APPROVAL',
  'PAYMENT_CONFIRM',
  'PLATFORM_INVOICE_ENTRY',
  'VENDOR_INVOICE_ENTRY',
  'INVOICE_CONFIRM',
  'PLATFORM_REFUND_RECEIPT_UPLOAD',
  'REFUND_CONFIRM',
  'PLATFORM_PAY_RECEIPT_UPLOAD',
  'PLATFORM_INVOICE_CONFIRM',
  'SETTLEMENT_PENDING',
  'SETTLEMENT_RECORDED',
  'END',
]);
//日志备忘录
const visible = ref<boolean>(false);
const memorandumvisible = ref<boolean>(false);
const confirmLoading = ref(false);
const Confirm = (num) => {
  if (num == 1) {
    visible.value = false;
  } else {
    memorandumvisible.value = false;
  }
};

const isViewLog = ref(false);

const permission = () => {
  console.log(store.loginUser.authorities, 'store.loginUser.authorities');

  store.loginUser.authorities.forEach((item) => {
    if (item.authority == '211' || item.authority == '213') {
      isViewLog.value = true;
    }
  });
};
</script>
<template>
  <!-- 顾问列表页面主容器 -->
  <div class="container">
    <!-- 顾问列表组件 -->
    <Advisors
      v-bind="$attrs"
      preview-source="demandOne"
      ref="view"
      :class="record.hideBtn == '1' ? 'footer-user-width' : ''"
    >
      <!-- 头部插槽：备忘录和日志按钮 -->
      <template #header>
        <div v-if="hideBtn !== '1'">
          <a-button class="mr10" size="small" type="link" @click="viewProcess()">
            {{ showProcess ? '收起流程节点' : '查看流程节点' }}
            <UpOutlined v-if="!showProcess"/>
            <DownOutlined v-else/>
          </a-button>
        </div>
        <Button v-if="isViewLog && $attrs.orderSource === 'manage'" size="small" style="margin-right: 10px" @click="viewMemo()">备忘录</Button>
        <Button v-if="isViewLog && $attrs.orderSource === 'manage'" size="small" type="primary" style="margin-right: 10px" @click="viewLog()">日志</Button>
      </template>
      <!-- 流程节点 -->
      <template #processSlot>
        <!-- 流程信息 -->
        <div class="" v-show="showProcess">
          <finalNode :nodeId="route.query.record" :key="route.fullPath"></finalNode>
        </div>
      </template>

      <!-- 底部插槽：操作按钮组 -->
      <template #footer>
        <a-radio-group v-model:value="footerChecked" button-style="solid">
          <a-radio-button value="a">需求视图</a-radio-button>
          <a-radio-button @click="handleScheme('b')" value="b">方案视图</a-radio-button>
          <a-radio-button
            v-if="
              ['BID_RESULT_CONFIRM'].concat(arrConfirmView).includes(view?.orderDetail?.processNode) &&
              resolveParam(route.query.record).orderType === 'detail'
            "
            @click="handleScheme('c')"
            value="c"
            >竞价方案</a-radio-button
          >
        </a-radio-group>
        <a-button
          v-if="
            (hideBtn !== '1' &&
              ['/bidman/scheme/confirm/view'].includes(route.path) &&
              $attrs.orderSource === 'manage') ||
            resolveParam(route.query.record).orderType === 'detail'
          "
          style="float: right"
          @click="backupOrderList()"
          type="primary"
          >返回</a-button
        >
        <a-button v-show="hideBtn !== '1'" v-else style="float: right" @click="handleScheme('b')" type="primary"
          >下一步</a-button
        >
      </template>
    </Advisors>

    <!-- 驳回原因弹窗 -->
    <a-modal v-model:open="open" title="驳回" @ok="handleOk">
      <p>驳回原因</p>
      <a-textarea v-model:value="reason" :rows="4" :maxLength="200" placeholder="请输入驳回原因" />
    </a-modal>

    <!-- 会务顾问抽屉组件 -->
    <meeting-consultant-drawer
      title="会务顾问"
      v-model="editModalOpen"
      class="meeting-consultant-drawer"
      :items="testData"
      @search="searchKeyword"
      @assign="assignPerson"
    />
    <!-- 日志弹窗 -->
    <a-modal
      :visible="visible"
      width="70%"
      :confirmLoading="confirmLoading"
      @ok="Confirm(1)"
      @cancel="visible = false"
      title="日志"
    >
      <orderLog :nodeId="route.query.record" />
      <template #footer>
        <h-button @click="visible = false">关闭</h-button>
      </template>
    </a-modal>
    <!-- 备忘录弹窗 -->
    <a-modal
      :visible="memorandumvisible"
      width="70%"
      :confirmLoading="confirmLoading"
      @ok="Confirm(2)"
      @cancel="memorandumvisible = false"
      title="备忘录"
    >
      <memorandum :nodeId="route.query.record" />
      <template #footer>
        <h-button @click="memorandumvisible = false">关闭</h-button>
      </template>
    </a-modal>
  </div>
</template>
<style lang="less" scoped>
.container {
  background: #f1f2f6;
  padding: 0 auto;
}
.footer-user-width {
  width: 1280px !important;
  left: calc(50% - 640px);
}
:deep(.ant-btn-default) {
  padding: 3px 8px;
  height: 32px;
  width: 80px;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #4e5969;
  line-height: 22px;
  text-align: center;
  font-style: normal;
}

:deep(.ant-btn-primary) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 22px;
  text-align: center;
  font-style: normal;
  padding: 3px 8px;
  height: 32px;
  background: #1868db;
  border-radius: 2px;
}

.reject-btn {
  background: #f5222d;
}

:deep(.demand_contrast_footer) {
  text-align: left !important;
}
:where(.css-dev-only-do-not-override-bq26c2).ant-btn >span+.anticon {
    margin-inline-start: 2px;
}
</style>
