<template>
  <a-popover
    v-model:open="visible"
    class="city-popover"
    trigger="click"
    placement="bottomLeft"
  >
    <template #title>
      <a-tabs v-model:activeKey="activeCityKey" @change="changeTab">
        <a-tab-pane v-for="tab in cityList" :key="tab.cityId" :tab="tab.cityName">
        </a-tab-pane>
      </a-tabs>
    </template>
    <template #content>
      <div class="city-main-box" >
        <!-- 默认城市三字码 -->
          <a-tabs  v-model:activeKey="activeKey">
            <a-tab-pane  v-for="tab in trainOptions[0]?.lowerList" :key="tab.cityId" :tab="tab.cityName">
              <a-list :loading="listLoading" style="height: 320px; overflow-y: scroll;" :data-source="tab?.zdList">
                <template #renderItem="{ item }">
                  <a-list-item style="cursor: pointer;" @click="trainChose(item)">
                    <div>{{ item.stationName}}</div>
                    <div>{{ item.stationCode}}</div>
                  </a-list-item>
                </template>
              
              </a-list>
            </a-tab-pane>
          </a-tabs>

       
      </div>
    </template>
    <a-input readonly v-model:value="props.value" :bordered="props.bordered" placeholder="选择火车站" :allowClear="true" />
  </a-popover>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch, watchEffect, toRefs, createVNode } from 'vue';
import { cityApi } from '@haierbusiness-front/apis';

const emit = defineEmits(['chosed']);

interface Props {
    value: string,
    cityId: string,
    bordered?: boolean,
    cityList?: any,
}

const props = withDefaults(defineProps<Props>(), {
    value: '',
    cityId: '',
    bordered: true,
    cityList: []
});
const listLoading = ref(false);

const activeCityKey = ref<string>('');

const changeTab = (val) => {
  trainStationGroupingByCityId(val)
}

const visible = ref<boolean>(false);
const activeKey = ref<string>('');

const trainOptions = ref([]);

const trainChose = (ZD: ZD) => {
  visible.value = false;
  debugger
  emit('chosed', ZD);
};


const trainStationGroupingByCityId = (cityId) => {
  const param = {
    cityId: cityId
  }
  listLoading.value = true
  // 查询火车三字码列表
  cityApi.trainStationGroupingByCityId(param).then((res) => {
    trainOptions.value = res || [];
    activeKey.value = trainOptions.value[0]?.lowerList[0]?.cityId
    listLoading.value=false
  }).catch(() => {
    listLoading.value=false
  })
};


watch(
  () => props.cityId,
  (N,O) => {
    if (N) {
      activeCityKey.value = N
      trainStationGroupingByCityId(N)
    }
  },
  { immediate: true }
)

</script>

<style scoped lang="less">
.city-main-box {
  width: 600px;
}
.city-list {
  .city-box {
    display: flex;
    .box-left {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 10%;
      color: orange;
      font-size: 20px;
    }
    .box-right {
      display: flex;
      flex-wrap: wrap;
      flex: 1;
    }
  }
}
</style>