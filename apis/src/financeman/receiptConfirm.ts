import { download, get, post } from '../request'
import {
    IPaymentFromFilter,
    IPaymentFrom,
    ICreatePaymentFromParams,
    IConfirmPaymentParams,
    IRejectPaymentParams,
    IUploadInvoiceParams,
    IPageResponse,
    Result
} from '@haierbusiness-front/common-libs'


export const receiptConfirmApi = {

    // 收款单详情查询-缴费
    getPaymentDetails: (id: number): Promise<IPaymentFrom> => {
        return get('/financeman/api/financeman/receive/payment/getDetails', {
            id
        })
    },

    // 财务确认付款并上传付款凭证-缴费
    confirmPaymentPayment: (params: IConfirmPaymentParams): Promise<Result> => {
        return post('/financeman/api/financeman/receive/payment/confirm', params)
    },
    // 财务驳回付款-缴费
    rejectPayment: (params: IRejectPaymentParams): Promise<Result> => {
        return post('/financeman/api/financeman/receive/payment/reject', params)
    },

    // 分页查询收款单列表
    getPayMentList: (params: IPaymentFromFilter): Promise<IPageResponse<IPaymentFrom>> => {
        return get('/financeman/api/financeman/receive/payment/getPage', params)
    },

    // 校验收sap单号是否已关联
    receiveIsReceive: (params: IPaymentFromFilter): Promise<IPageResponse<IPaymentFrom>> => {
        return get('/financeman/api/financeman/receive/payment/isReceive', params)
    },
}
